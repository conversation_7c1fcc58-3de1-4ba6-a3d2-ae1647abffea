FROM openjdk:17.0.2-slim
LABEL authors="liuchao"

# 外部传进来的jar包名，并将其转换为环境变量，确保运行ENTRYPOINT中能够正确读取
ARG JAR_NAME
ENV JAR_NAME=${JAR_NAME}
# 服务端口
ARG SERVER_PORT
# 项目环控服务端口
ARG PROJECT_ENV_SERVER_PORT
# 生效的Spring Profile环境，并将其转换为环境变量，确保运行ENTRYPOINT中能够正确读取
ARG SPRING_PROFILE
ENV SPRING_PROFILE=${SPRING_PROFILE}

WORKDIR /app

COPY ${JAR_NAME}.jar ${JAR_NAME}.jar

RUN mkdir -p /app/logs && \
    mkdir -p /app/files && \
    mkdir -p /app/active && \
    chmod 755 /app/files

VOLUME ["/app/log", "/app/files", "/app/active"]

# 暴露端口
EXPOSE ${PROJECT_ENV_SERVER_PORT} ${SERVER_PORT}

ENTRYPOINT java -jar ${JAR_NAME}.jar --spring.profiles.active=${SPRING_PROFILE}