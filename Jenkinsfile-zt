pipeline {
	agent any
	tools {
		maven 'Maven-3.9.9'
		jdk 'openjdk-17.0.2'
	}
	environment {
		// jar包名
		JAR_NAME='smart'
		// docker构建目录
		DOCKER_BUILD_DIR='build-docker-context'
		// docker镜像名
		DOCKER_IMAGE_NAME='smart-zt'
		// docker镜像版本
		DOCKER_IMAGE_VERSION = "1.0"
		// docker容器名
		DOCKER_CONTAINER_NAME = "smart-zt"
		// Spring Profile环境
		SPRING_PROFILE = "zt"
		// 服务端口
		SERVER_PORT = "28084"
		// 项目环境服务端口
		PROJECT_ENV_SERVER_PORT = "28083"
		// 钉钉webhook token
		DINGTALK_TOKEN = "5790f02baab912027d08ec14a8c0b139fa889ac781f40ffcceca7da1d188d83e"
		// 获取提交信息
		GIT_COMMIT_MESSAGE = sh(script: 'git log -1 --pretty=%B ${GIT_COMMIT} | head -n 1', returnStdout: true).trim()
		// 获取提交人
		GIT_COMMIT_AUTHOR = sh(script: 'git log -1 --pretty=%an ${GIT_COMMIT}', returnStdout: true).trim()
	}
	stages {
		stage('Maven构建') {
			steps {
				sh "java -version"
				sh "mvn --version"
				sh 'mvn clean package -DskipTests'
			}
		}
		stage('构建产物归档') {
			steps {
				archiveArtifacts artifacts: 'target/*.jar', followSymlinks: false, onlyIfSuccessful: true
			}
		}
		stage('清理旧镜像和容器') {
			steps {
				// 清理旧容器
				sh 'docker stop ${DOCKER_CONTAINER_NAME} || true'
				sh 'docker rm ${DOCKER_CONTAINER_NAME} || true'
				// 清理旧镜像
				sh 'docker image rm ${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_VERSION} || true'
			}
		}
		stage('Docker镜像构建') {
			steps {
			    sh 'mkdir -p ${DOCKER_BUILD_DIR}'
				sh 'rm -rf ${DOCKER_BUILD_DIR}/*'
			    sh 'cp target/${JAR_NAME}.jar ${DOCKER_BUILD_DIR}/'
			    sh 'cp Dockerfile ${DOCKER_BUILD_DIR}/'
			    sh '''
				    docker build \
				    -t ${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_VERSION} \
				    -f ${DOCKER_BUILD_DIR}/Dockerfile \
				    --build-arg JAR_NAME=${JAR_NAME} \
				    --build-arg SERVER_PORT=${SERVER_PORT} \
				    --build-arg PROJECT_ENV_SERVER_PORT=${PROJECT_ENV_SERVER_PORT} \
					--build-arg SPRING_PROFILE=${SPRING_PROFILE} \
				    ${DOCKER_BUILD_DIR}
			    '''
			}
		}
		stage('启动容器') {
			steps {
				sh '''
					docker run -d \
					--name ${DOCKER_CONTAINER_NAME} \
					--restart no \
					-p ${PROJECT_ENV_SERVER_PORT}:${PROJECT_ENV_SERVER_PORT} \
					-p ${SERVER_PORT}:${SERVER_PORT} \
					-v /home/<USER>/apps/${DOCKER_CONTAINER_NAME}/${DOCKER_CONTAINER_NAME}/log:/app/log \
                    -v /home/<USER>/apps/${DOCKER_CONTAINER_NAME}/${DOCKER_CONTAINER_NAME}/files:/app/files \
					-e TZ=Asia/Shanghai \
					${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_VERSION}
				'''
			}
		}
	}
	post {
		success {
			script {
				sh """
					curl -X POST \
					-H "Content-Type: application/json" \
					-d '{
					"msgtype": "actionCard",
					"actionCard": {
						"title": "智慧平台2.0服务端(展厅版本)构建完成",
						"text": "#### 智慧平台2.0服务端(展厅版本)第 ${BUILD_ID} 次构建 \\n\\n • 构建分支: ${GIT_BRANCH} \\n\\n • Git提交: ${GIT_COMMIT_MESSAGE} \\n\\n • 服务端口：${SERVER_PORT}， 环控服务端口：${PROJECT_ENV_SERVER_PORT}",
						"btns": [
							{
								"title": "构建详情",
								"actionURL": "${BUILD_URL}"
							}
						]
					}
					}' \
					https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}
				"""
			}
		}
		failure {
			script {
				sh """
					curl -X POST \
					-H "Content-Type: application/json" \
					-d '{
					"msgtype": "actionCard",
					"actionCard": {
						"title": "智慧平台2.0服务端(展厅版本)构建失败",
						"text": "#### 智慧平台2.0服务端(展厅版本)构建失败 \\n\\n • 构建号：${BUILD_ID} \\n\\n • 构建分支: ${GIT_BRANCH} \\n\\n • Git提交信息: ${GIT_COMMIT_MESSAGE}",
						"btns": [
							{
								"title": "构建详情",
								"actionURL": "${BUILD_URL}"
							},
							{
								"title": "构建日志",
								"actionURL": "${BUILD_URL}/console"
							}
						]
					}
					}' \
					https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}
				"""
			}
		}
		always {
			// 清理构建目录
			sh 'rm -rf ${DOCKER_BUILD_DIR}'
		}
	}
}