<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.inventory.RobotInventoryTaskMapper">
  <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.inventory.RobotInventoryTask">
    <!--@mbg.generated-->
    <!--@Table robot_inventory_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="robot_id" jdbcType="BIGINT" property="robotId" />
    <result column="task_cycle" jdbcType="VARCHAR" property="taskCycle" />
    <result column="task_cycle_day" jdbcType="INTEGER" property="taskCycleDay" />
    <result column="task_time" jdbcType="TIME" property="taskTime" />
    <result column="created_id" jdbcType="BIGINT" property="createdId" />
    <result column="updated_id" jdbcType="BIGINT" property="updatedId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="delete_at" jdbcType="BIGINT" property="deleteAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, warehouse_id, task_name, task_status, robot_id, task_cycle, task_cycle_day, task_time, 
    created_id, updated_id, created_at, updated_at, delete_at
  </sql>
</mapper>