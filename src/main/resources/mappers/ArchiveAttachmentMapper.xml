<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveAttachmentMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveAttachment">
        <!--@mbg.generated-->
        <!--@Table archive_attachment-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="attachment_path" jdbcType="VARCHAR" property="attachmentPath"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        archive_id,
        attachment_name,
        attachment_path,
        created_id,
        updated_id,
        created_at,
        updated_at
    </sql>

    <select id="selectListByArchiveId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_attachment
        where archive_id = #{archiveId,jdbcType=BIGINT}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_attachment
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.archive.ArchiveAttachment" useGeneratedKeys="true">
        insert into archive_attachment (archive_id, attachment_name, attachment_path,
                                        created_id, updated_id)
        values (#{archiveId,jdbcType=BIGINT}, #{attachmentName,jdbcType=VARCHAR}, #{attachmentPath,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById">
        update archive_attachment
        set attachment_name = #{attachmentName,jdbcType=VARCHAR},
            attachment_path = #{attachmentPath,jdbcType=VARCHAR},
            updated_id      = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteById">
        delete
        from archive_attachment
        where id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>