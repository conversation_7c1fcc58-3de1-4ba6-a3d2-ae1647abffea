<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.user.UserRoleMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.UserRole">
        <!--@mbg.generated-->
        <!--@Table `sys_user_role`-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        user_id,
        role_id,
        organization_id,
        created_id,
        created_at
    </sql>

    <select id="selectByRoleId" resultType="org.zxwl.smart.mybatis.entity.sys.UserRole">
        select
        <include refid="Base_Column_List"/>
        from sys_user_role
        where role_id = #{roleId,jdbcType=BIGINT}
    </select>

    <select id="selectRoleIdListByUserIdAndOrgId" resultType="java.lang.Long">
        select role_id
        from sys_user_role
        where user_id = #{userId,jdbcType=BIGINT}
          and organization_id = #{organizationId,jdbcType=BIGINT}
    </select>

    <insert id="insertBatch">
        insert into sys_user_role (user_id, role_id, organization_id, created_id)
        values
        <foreach collection="userRoleList" item="userRole" separator=",">
            (#{userRole.userId,jdbcType=BIGINT}, #{userRole.roleId,jdbcType=BIGINT},
             #{userRole.organizationId,jdbcType=BIGINT}, #{userRole.createdId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByUserIdAndOrgId">
        delete
        from sys_user_role
        where user_id = #{userId,jdbcType=BIGINT}
          and organization_id = #{organizationId,jdbcType=BIGINT}
    </delete>
</mapper>