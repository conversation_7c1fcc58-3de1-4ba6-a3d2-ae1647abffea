<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveOperatorRecordMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveOperatorRecord">
        <!--@mbg.generated-->
        <!--@Table archive_operator_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="operator_type" jdbcType="VARCHAR" property="operatorType"/>
        <result column="archive_type_id" jdbcType="BIGINT" property="archiveTypeId"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="archive_type" jdbcType="VARCHAR" property="archiveType"/>
        <result column="archive_name" jdbcType="VARCHAR" property="archiveName"/>
        <result column="archive_no" jdbcType="VARCHAR" property="archiveNo"/>
        <result column="rack_group_no" jdbcType="INTEGER" property="rackGroupNo"/>
        <result column="rack_column_no" jdbcType="INTEGER" property="rackColumnNo"/>
        <result column="rack_panel_No" jdbcType="INTEGER" property="rackPanelNo"/>
        <result column="rack_section_no" jdbcType="INTEGER" property="rackSectionNo"/>
        <result column="rack_layer_no" jdbcType="INTEGER" property="rackLayerNo"/>
        <result column="rack_grid_no" jdbcType="INTEGER" property="rackGridNo"/>
        <result column="hr_cabinet_group_no" jdbcType="INTEGER" property="hrCabinetGroupNo"/>
        <result column="hr_cabinet_no" jdbcType="INTEGER" property="hrCabinetNo"/>
        <result column="hr_cabinet_layer_no" jdbcType="INTEGER" property="hrCabinetLayerNo"/>
        <result column="hr_cabinet_grid_no" jdbcType="INTEGER" property="hrCabinetGridNo"/>
        <result column="cabinet_group_no" jdbcType="INTEGER" property="cabinetGroupNo"/>
        <result column="cabinet_no" jdbcType="INTEGER" property="cabinetNo"/>
        <result column="cabinet_grid_no" jdbcType="INTEGER" property="cabinetGridNo"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        warehouse_id,
        operator_type,
        archive_type_id,
        archive_id,
        archive_type,
        archive_name,
        archive_no,
        rack_group_no,
        rack_column_no,
        rack_panel_No,
        rack_section_no,
        rack_layer_no,
        rack_grid_no,
        hr_cabinet_group_no,
        hr_cabinet_no,
        hr_cabinet_layer_no,
        hr_cabinet_grid_no,
        cabinet_group_no,
        cabinet_no,
        cabinet_grid_no,
        operator_id,
        created_at
    </sql>

    <select id="selectStatByOperatorType" resultType="org.zxwl.smart.mybatis.ext.archive.OperatorStat">
        select operator_type,
               count(*) as count
        from archive_operator_record
        <where>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId,jdbcType=BIGINT}
            </if>
            <if test="rackGroupNo != null">
                and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
            </if>
            <if test="hrCabinetGroupNo != null">
                and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
            </if>
            <if test="cabinetGroupNo != null">
                and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
            </if>
            <if test="beginCreatedAt != null">
                and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
            </if>
            <if test="endCreatedAt != null">
                and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
            </if>
        </where>
        group by operator_type
    </select>

    <select id="selectLastArchiveOperatorRecord" resultMap="BaseResultMap">
        select max(id) as id
        from archive_operator_record
        <where>
            <if test="archiveIdList != null and archiveIdList.size() != 0">
                and archive_id in
                <foreach close=")" collection="archiveIdList" item="archiveId" open="(" separator=",">
                    #{archiveId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="operatorType != null and operatorType != ''">
                and operator_type = #{operatorType,jdbcType=VARCHAR}
            </if>
        </where>
        group by archive_id
    </select>

    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_operator_record
        where id in
        <foreach close=")" collection="recordIdList" item="recordId" open="(" separator=",">
            #{recordId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_operator_record
        <where>
            <if test="operatorTypeList != null and operatorTypeList.size() != 0">
                operator_type in
                <foreach close=")" collection="operatorTypeList" item="operatorType" open="(" separator=",">
                    #{operatorType,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="archiveName != null and archiveName != ''">
                and archive_name = #{archiveName,jdbcType=VARCHAR}
            </if>
            <if test="archiveNameLike != null and archiveNameLike != ''">
                <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
                and archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
            </if>
            <if test="archiveNo != null and archiveNo != ''">
                and archive_no = #{archiveNo,jdbcType=VARCHAR}
            </if>
            <if test="archiveNoLike != null and archiveNoLike != ''">
                <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
                and archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectListForStat" resultMap="BaseResultMap">
        select id,
               warehouse_id,
               operator_type,
               archive_id,
               created_at
        from archive_operator_record
        <where>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId,jdbcType=BIGINT}
            </if>
            <if test="operatorTypeList != null and operatorTypeList.size() != 0">
                and operator_type in
                <foreach close=")" collection="operatorTypeList" item="operatorType" open="(" separator=",">
                    #{operatorType,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="beginCreatedAt != null">
                and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
            </if>
            <if test="endCreatedAt != null">
                and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.archive.ArchiveOperatorRecord" useGeneratedKeys="true">
        insert into archive_operator_record (operator_type, organization_id, warehouse_id, archive_type_id,
                                             archive_id, archive_type, archive_name,
                                             archive_no, rack_group_no, rack_column_no,
                                             rack_panel_No, rack_section_no, rack_layer_no,
                                             rack_grid_no, hr_cabinet_group_no, hr_cabinet_no,
                                             hr_cabinet_layer_no, hr_cabinet_grid_no, cabinet_group_no,
                                             cabinet_no, cabinet_grid_no, operator_id)
        values (#{operatorType,jdbcType=VARCHAR}, #{organizationId,jdbcType=BIGINT}, #{warehouseId,jdbcType=BIGINT},
                #{archiveTypeId,jdbcType=BIGINT},
                #{archiveId,jdbcType=BIGINT}, #{archiveType,jdbcType=VARCHAR}, #{archiveName,jdbcType=VARCHAR},
                #{archiveNo,jdbcType=VARCHAR}, #{rackGroupNo,jdbcType=INTEGER}, #{rackColumnNo,jdbcType=INTEGER},
                #{rackPanelNo,jdbcType=INTEGER}, #{rackSectionNo,jdbcType=INTEGER}, #{rackLayerNo,jdbcType=INTEGER},
                #{rackGridNo,jdbcType=INTEGER}, #{hrCabinetGroupNo,jdbcType=INTEGER}, #{hrCabinetNo,jdbcType=INTEGER},
                #{hrCabinetLayerNo,jdbcType=INTEGER}, #{hrCabinetGridNo,jdbcType=INTEGER},
                #{cabinetGroupNo,jdbcType=INTEGER}, #{cabinetNo,jdbcType=INTEGER},
                #{cabinetGridNo,jdbcType=INTEGER}, #{operatorId,jdbcType=BIGINT})
    </insert>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_operator_record
        (operator_type, organization_id, warehouse_id, archive_type_id, archive_id, archive_type, archive_name,
         archive_no, rack_group_no, rack_column_no, rack_panel_No, rack_section_no, rack_layer_no,
         rack_grid_no, hr_cabinet_group_no, hr_cabinet_no,
         hr_cabinet_layer_no, hr_cabinet_grid_no, cabinet_group_no, cabinet_no, cabinet_grid_no, operator_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.operatorType,jdbcType=VARCHAR}, #{item.organizationId,jdbcType=BIGINT},
             #{item.warehouseId,jdbcType=BIGINT}, #{item.archiveTypeId,jdbcType=BIGINT},
             #{item.archiveId,jdbcType=BIGINT}, #{item.archiveType,jdbcType=VARCHAR},
             #{item.archiveName,jdbcType=VARCHAR},
             #{item.archiveNo,jdbcType=VARCHAR}, #{item.rackGroupNo,jdbcType=INTEGER},
             #{item.rackColumnNo,jdbcType=INTEGER},
             #{item.rackPanelNo,jdbcType=INTEGER}, #{item.rackSectionNo,jdbcType=INTEGER},
             #{item.rackLayerNo,jdbcType=INTEGER},
             #{item.rackGridNo,jdbcType=INTEGER}, #{item.hrCabinetGroupNo,jdbcType=INTEGER},
             #{item.hrCabinetNo,jdbcType=INTEGER},
             #{item.hrCabinetLayerNo,jdbcType=INTEGER}, #{item.hrCabinetGridNo,jdbcType=INTEGER},
             #{item.cabinetGroupNo,jdbcType=INTEGER},
             #{item.cabinetNo,jdbcType=INTEGER},
             #{item.cabinetGridNo,jdbcType=INTEGER}, #{item.operatorId,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>