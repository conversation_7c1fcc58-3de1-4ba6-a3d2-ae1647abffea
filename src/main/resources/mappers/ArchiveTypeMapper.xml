<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveType">
        <!--@mbg.generated-->
        <!--@Table archive_type-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="other_name" jdbcType="VARCHAR" property="otherName"/>
        <result column="is_support_manual" jdbcType="TINYINT" property="supportManual"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        pid,
        `type`,
        type_name,
        other_name,
        is_support_manual,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_type
        where delete_at = 0
          and type = #{type,jdbcType=VARCHAR}
        <if test="organizationId != null">
            and organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        order by id desc
    </select>

    <select id="selectNameList" resultMap="BaseResultMap">
        select id,
               type_name,
               other_name
        from archive_type
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_type
        where id = #{id,jdbcType=BIGINT}
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="existById" resultType="int">
        select count(*)
        from archive_type
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
        limit 1
    </select>

    <select id="existTypeName" resultType="int">
        select count(*)
        from archive_type
        where type_name = #{typeName,jdbcType=VARCHAR}
          and delete_at = 0
        limit 1
    </select>

    <select id="existByOrganizationId" resultType="int">
        select count(*)
        from archive_type
        where organization_id = #{organizationId,jdbcType=BIGINT}
          and delete_at = 0
        limit 1
    </select>

    <select id="count" resultType="int">
        select count(*)
        from archive_type
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.archive.ArchiveType" useGeneratedKeys="true">
        insert into archive_type (organization_id, pid, type, type_name, other_name, is_support_manual, created_id,
                                  updated_id)
        values (#{organizationId,jdbcType=BIGINT}, #{pid,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR},
                #{typeName,jdbcType=VARCHAR},
                #{otherName,jdbcType=VARCHAR}, #{supportManual,jdbcType=TINYINT}, #{createdId,jdbcType=BIGINT},
                #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.archive.ArchiveType">
        update archive_type
        set pid               = #{pid,jdbcType=BIGINT},
            type_name         = #{typeName,jdbcType=VARCHAR},
            other_name        = #{otherName,jdbcType=VARCHAR},
            is_support_manual = #{supportManual,jdbcType=TINYINT},
            updated_id        = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update archive_type
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>