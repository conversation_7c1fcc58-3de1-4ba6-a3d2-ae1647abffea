<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.user.UserMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.user.User">
        <!--@mbg.generated-->
        <!--@Table `user`-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="admin_flag" jdbcType="TINYINT" property="adminFlag"/>
        <result column="user_status" jdbcType="VARCHAR" property="userStatus"/>
        <result column="user_sex" jdbcType="VARCHAR" property="userSex"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_account" jdbcType="VARCHAR" property="userAccount"/>
        <result column="user_pwd" jdbcType="VARCHAR" property="userPwd"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="finger_id" jdbcType="VARCHAR" property="fingerId"/>
        <result column="face_id" jdbcType="VARCHAR" property="faceId"/>
        <result column="gesture" jdbcType="VARCHAR" property="gesture"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        admin_flag,
        user_status,
        user_sex,
        mobile,
        email,
        user_name,
        user_account,
        user_pwd,
        id_card,
        finger_id,
        face_id,
        gesture,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `user` where delete_at = 0
        <if test="userNameLike != null">
            <bind name="_userNameLike" value="'%' + userNameLike + '%'"/>
            and user_name like #{_userNameLike,jdbcType=VARCHAR}
        </if>
        <if test="updatedAt != null">
            and updated_at &gt;= #{updatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <select id="selectNameListByIdList" resultMap="BaseResultMap">
        select id,
               user_name,
               user_account
        from `user`
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `user`
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="selectNameById" resultMap="BaseResultMap">
        select id,
               user_name,
               user_account
        from `user`
        where id = #{id,jdbcType=BIGINT}
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectByUserAccountOrIdCard" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `user`
        where delete_at = 0
        <if test="userAccount != null">
            and user_account = #{userAccount,jdbcType=VARCHAR}
        </if>
        <if test="idCard != null">
            and id_card = #{idCard,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="existByUserAccount" resultType="int">
        select count(*)
        from `user`
        where user_account = #{userAccount,jdbcType=VARCHAR}
          and delete_at = 0
        limit 1
    </select>

    <select id="existByIdCard" resultType="int">
        select count(*)
        from `user`
        where id_card = #{idCard,jdbcType=VARCHAR}
          and delete_at = 0
    </select>

    <select id="count" resultType="int">
        select count(*)
        from `user`
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="org.zxwl.smart.mybatis.entity.user.User"
            useGeneratedKeys="true">
        insert into `user` (dept_id, admin_flag,
                            user_status, user_sex, mobile,
                            email, user_name, user_account,
                            user_pwd, id_card, finger_id,
                            face_id, gesture, created_id,
                            updated_id)
        values (#{deptId,jdbcType=BIGINT}, #{adminFlag,jdbcType=TINYINT},
                #{userStatus,jdbcType=VARCHAR}, #{userSex,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR},
                #{email,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{userAccount,jdbcType=VARCHAR},
                #{userPwd,jdbcType=VARCHAR}, #{idCard,jdbcType=VARCHAR}, #{fingerId,jdbcType=VARCHAR},
                #{faceId,jdbcType=VARCHAR}, #{gesture,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT},
                #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.user.User">
        update `user`
        set dept_id      = #{deptId,jdbcType=BIGINT},
            user_status  = #{userStatus,jdbcType=VARCHAR},
            user_sex     = #{userSex,jdbcType=VARCHAR},
            mobile       = #{mobile,jdbcType=VARCHAR},
            email        = #{email,jdbcType=VARCHAR},
            user_name    = #{userName,jdbcType=VARCHAR},
            user_account = #{userAccount,jdbcType=VARCHAR},
            user_pwd     = #{userPwd,jdbcType=VARCHAR},
            id_card      = #{idCard,jdbcType=VARCHAR},
            finger_id    = #{fingerId,jdbcType=VARCHAR},
            face_id      = #{faceId,jdbcType=VARCHAR},
            gesture      = #{gesture,jdbcType=VARCHAR},
            updated_id   = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update `user`
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>