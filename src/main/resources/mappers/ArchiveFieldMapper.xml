<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveFieldMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveField">
        <!--@mbg.generated-->
        <!--@Table archive_field-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_type_id" jdbcType="BIGINT" property="archiveTypeId"/>
        <result column="field_type" jdbcType="VARCHAR" property="fieldType"/>
        <result column="fixed_field_name" jdbcType="VARCHAR" property="fixedFieldName"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="field_ordinal" jdbcType="INTEGER" property="fieldOrdinal"/>
        <result column="field_width" jdbcType="INTEGER" property="fieldWidth"/>
        <result column="is_show_list" jdbcType="TINYINT" property="showList"/>
        <result column="is_input" jdbcType="TINYINT" property="input"/>
        <result column="is_required" jdbcType="TINYINT" property="required"/>
        <result column="is_search" jdbcType="TINYINT" property="search"/>
        <result column="is_ad_search" jdbcType="TINYINT" property="adSearch"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        archive_type_id,
        field_type,
        fixed_field_name,
        field_name,
        field_ordinal,
        field_width,
        is_show_list,
        is_input,
        is_required,
        is_search,
        is_ad_search,
        remark,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectListByArchiveTypeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_field
        where delete_at = 0
        <choose>
            <when test="archiveTypeId != null">
                and archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
            </when>
            <otherwise>
                and archive_type_id is null
            </otherwise>
        </choose>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_field
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="countByArchiveTypeId" resultType="int">
        select count(*)
        from archive_field
        where delete_at = 0
        <choose>
            <when test="archiveTypeId != null">
                and archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
            </when>
            <otherwise>
                and archive_type_id is null
            </otherwise>
        </choose>
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.archive.ArchiveField" useGeneratedKeys="true">
        insert into archive_field (archive_type_id, field_type, field_name,
                                   field_ordinal, field_width, is_show_list,
                                   is_required, is_search, is_ad_search,
                                   remark, created_id, updated_id)
        values (#{archiveTypeId,jdbcType=BIGINT}, #{fieldType,jdbcType=VARCHAR}, #{fieldName,jdbcType=VARCHAR},
                #{fieldOrdinal,jdbcType=INTEGER}, #{fieldWidth,jdbcType=INTEGER}, #{showList,jdbcType=TINYINT},
                #{required,jdbcType=TINYINT}, #{search,jdbcType=TINYINT}, #{adSearch,jdbcType=TINYINT},
                #{remark,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_field
        (archive_type_id, field_type, fixed_field_name, field_name, field_ordinal, field_width,
         is_show_list, is_input, is_required, is_search, is_ad_search, remark, created_id, updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.archiveTypeId,jdbcType=BIGINT}, #{item.fieldType,jdbcType=VARCHAR},
             #{item.fixedFieldName,jdbcType=VARCHAR},
             #{item.fieldName,jdbcType=VARCHAR}, #{item.fieldOrdinal,jdbcType=INTEGER},
             #{item.fieldWidth,jdbcType=INTEGER},
             #{item.showList,jdbcType=TINYINT}, #{item.input,jdbcType=BOOLEAN}, #{item.required,jdbcType=TINYINT},
             #{item.search,jdbcType=TINYINT},
             #{item.adSearch,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.createdId,jdbcType=BIGINT},
             #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.archive.ArchiveField">
        update archive_field
        set archive_type_id = #{archiveTypeId,jdbcType=BIGINT},
            field_type      = #{fieldType,jdbcType=VARCHAR},
            field_name      = #{fieldName,jdbcType=VARCHAR},
            field_ordinal   = #{fieldOrdinal,jdbcType=INTEGER},
            field_width     = #{fieldWidth,jdbcType=INTEGER},
            is_show_list    = #{showList,jdbcType=TINYINT},
            is_required     = #{required,jdbcType=TINYINT},
            is_search       = #{search,jdbcType=TINYINT},
            is_ad_search    = #{adSearch,jdbcType=TINYINT},
            remark          = #{remark,jdbcType=VARCHAR},
            updated_id      = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update archive_field
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteByArchiveTypeId">
        update archive_field
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
    </update>
</mapper>