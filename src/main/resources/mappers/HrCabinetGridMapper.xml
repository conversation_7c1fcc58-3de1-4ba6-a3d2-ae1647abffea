<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGridMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGrid">
        <!--@mbg.generated-->
        <!--@Table hr_cabinet_grid-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cabinet_group_id" jdbcType="BIGINT" property="cabinetGroupId"/>
        <result column="cabinet_layer_id" jdbcType="BIGINT" property="cabinetLayerId"/>
        <result column="capacity" jdbcType="INTEGER" property="capacity"/>
        <result column="used_capacity" jdbcType="INTEGER" property="usedCapacity"/>
        <result column="grid_no" jdbcType="INTEGER" property="gridNo"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        cabinet_group_id,
        cabinet_layer_id,
        capacity,
        used_capacity,
        grid_no,
        created_id,
        updated_id,
        created_at,
        updated_at
    </sql>

    <select id="selectListByCabinetLayerIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_cabinet_grid
        where cabinet_layer_id in
        <foreach close=")" collection="cabinetLayerIdList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectListByCabinetGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_cabinet_grid
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </select>

    <select id="selectLocationList" resultType="org.zxwl.smart.mybatis.ext.cabinet.HrCabinetGridLocationExt">
        select hr_cabinet_group.organization_id,
               hr_cabinet_group.group_no,
               hr_cabinet_layer.cabinet_no,
               hr_cabinet_layer.layer_no,
               hr_cabinet_grid.grid_no,
               hr_cabinet_grid.id as grid_id
        from hr_cabinet_group
                 inner join hr_cabinet_layer on hr_cabinet_group.id = hr_cabinet_layer.cabinet_group_id
                 inner join hr_cabinet_grid on hr_cabinet_layer.id = hr_cabinet_grid.cabinet_layer_id
        where hr_cabinet_group.delete_at = 0
          and
        (hr_cabinet_group.group_no, hr_cabinet_layer.cabinet_no, hr_cabinet_layer.layer_no, hr_cabinet_grid.grid_no) in
        <foreach close=")" collection="wrapperList" item="item" open="(" separator=",">
            (#{item.groupNo,jdbcType=INTEGER}, #{item.cabinetNo,jdbcType=INTEGER}, #{item.layerNo,jdbcType=INTEGER},
             #{item.gridNo,jdbcType=INTEGER})
        </foreach>
    </select>

    <select id="selectListByCabinetGroupIdForStatTask" resultMap="BaseResultMap">
        select id,
               cabinet_group_id,
               cabinet_layer_id,
               grid_no
        from hr_cabinet_grid
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into hr_cabinet_grid
        (cabinet_group_id, cabinet_layer_id, capacity, used_capacity, grid_no,
         created_id, updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.cabinetGroupId,jdbcType=BIGINT}, #{item.cabinetLayerId,jdbcType=BIGINT},
             #{item.capacity,jdbcType=INTEGER}, #{item.usedCapacity,jdbcType=INTEGER},
             #{item.gridNo,jdbcType=INTEGER}, #{item.createdId,jdbcType=BIGINT}, #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateUsedCapacityForStatTask" parameterType="java.util.List">
        update hr_cabinet_grid
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                        then #{item.usedCapacity,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteByCabinetGroupId">
        delete
        from hr_cabinet_grid
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </delete>
</mapper>