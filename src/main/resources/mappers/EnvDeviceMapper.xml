<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EnvDeviceMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EnvDevice">
        <!--@mbg.generated-->
        <!--@Table env_device-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="is_linked_data_hub" jdbcType="BOOLEAN" property="linkedDataHub"/>
        <result column="device_ip" jdbcType="VARCHAR" property="deviceIp"/>
        <result column="device_port" jdbcType="INTEGER" property="devicePort"/>
        <result column="device_serial_port" jdbcType="TINYINT" property="deviceSerialPort"/>
        <result column="device_serial_addr" jdbcType="TINYINT" property="deviceSerialAddr"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_id,
        device_type,
        device_name,
        device_no,
        is_linked_data_hub,
        device_ip,
        device_port,
        device_serial_port,
        device_serial_addr,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
        <if test="idList != null">
            and id in
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="deviceType != null">
            and device_type = #{deviceType,jdbcType=VARCHAR}
        </if>
        <if test="deviceNo != null">
            and device_no = #{deviceNo,jdbcType=VARCHAR}
        </if>
        <if test="deviceName != null">
            and device_name = #{deviceName,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device
        where delete_at = 0
          and id in
        <foreach close=")" collection="idList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device
        where delete_at = 0
          and id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByDeviceNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device
        where device_no = #{deviceNo,jdbcType=VARCHAR}
        and delete_at = 0
    </select>

    <select id="selectListByDeviceType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device
        where delete_at = 0
        and device_type = #{deviceType,jdbcType=VARCHAR}
    </select>

    <select id="existByDeviceNo" resultType="int">
        select count(*)
        from env_device
        where device_no = #{deviceNo,jdbcType=VARCHAR}
          and delete_at = 0
        limit 1
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="org.zxwl.smart.mybatis.entity.device.EnvDevice"
            useGeneratedKeys="true">
        insert into env_device (warehouse_id, device_type, device_name,
                                device_no, is_linked_data_hub, device_ip, device_port,
                                device_serial_port, device_serial_addr, visual_config,
                                created_id, updated_id)
        values (#{warehouseId,jdbcType=BIGINT}, #{deviceType,jdbcType=VARCHAR}, #{deviceName,jdbcType=VARCHAR},
                #{deviceNo,jdbcType=VARCHAR}, #{linkedDataHub,jdbcType=BOOLEAN}, #{deviceIp,jdbcType=VARCHAR},
                #{devicePort,jdbcType=INTEGER},
                #{deviceSerialPort,jdbcType=INTEGER}, #{deviceSerialAddr,jdbcType=INTEGER},
                #{visualConfig,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.device.EnvDevice">
        update env_device
        set device_name        = #{deviceName,jdbcType=VARCHAR},
            device_no          = #{deviceNo,jdbcType=VARCHAR},
            device_ip          = #{deviceIp,jdbcType=VARCHAR},
            device_port        = #{devicePort,jdbcType=INTEGER},
            device_serial_port = #{deviceSerialPort,jdbcType=INTEGER},
            device_serial_addr = #{deviceSerialAddr,jdbcType=INTEGER},
            visual_config      = #{visualConfig,jdbcType=VARCHAR},
            updated_id         = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update env_device
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>