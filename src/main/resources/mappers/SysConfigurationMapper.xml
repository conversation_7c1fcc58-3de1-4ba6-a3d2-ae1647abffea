<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.SysConfigurationMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.SysConfiguration">
        <!--@mbg.generated-->
        <!--@Table sys_configuration-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="terminal_code" jdbcType="VARCHAR" property="terminalCode"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="config_type" jdbcType="VARCHAR" property="configType"/>
        <result column="config_context" jdbcType="VARCHAR" property="configContext"/>
        <result column="config_template" jdbcType="VARCHAR" property="configTemplate"/>
        <result column="config_status" jdbcType="VARCHAR" property="configStatus"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        terminal_code,
        version,
        config_type,
        config_context,
        config_template,
        config_status,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_configuration
        <where>
            delete_at = 0
            <if test="id != null and id != ''">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="terminalCode != null and terminalCode != ''">
                and terminal_code = #{terminalCode,jdbcType=VARCHAR}
            </if>
            <if test="version != null and version != ''">
                and version = #{version,jdbcType=VARCHAR}
            </if>
            <if test="configType != null and configType != ''">
                and config_type = #{configType,jdbcType=VARCHAR}
            </if>
            <if test="configContext != null and configContext != ''">
                and config_context = #{configContext,jdbcType=VARCHAR}
            </if>
            <if test="configTemplate != null and configTemplate != ''">
                and config_template = #{configTemplate,jdbcType=VARCHAR}
            </if>
            <choose>
                <!--config_status传入all时不过滤-->
                <when test="configStatus != null and configStatus != 'all'">
                    and config_status = #{configStatus,jdbcType=VARCHAR}
                </when>
            </choose>
        </where>
    </select>

    <select id="existById" resultType="int">
        select count(*)
        from sys_configuration
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
        limit 1
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.sys.SysConfiguration" useGeneratedKeys="true">
        insert into sys_configuration (terminal_code, version, config_type,
                                       config_context, config_template, config_status,
                                       created_id, updated_id)
        values (#{terminalCode,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{configType,jdbcType=VARCHAR},
                #{configContext,jdbcType=VARCHAR}, #{configTemplate,jdbcType=VARCHAR}, #{configStatus,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.sys.SysConfiguration">
        update sys_configuration
        <set>
            <if test="terminalCode != null">
                terminal_code = #{terminalCode,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=VARCHAR},
            </if>
            <if test="configType != null and configType != ''">
                config_type = #{configType,jdbcType=VARCHAR},
            </if>
            <if test="configContext != null">
                config_context = #{configContext,jdbcType=VARCHAR},
            </if>
            <if test="configTemplate != null">
                config_template = #{configTemplate,jdbcType=VARCHAR},
            </if>
            <if test="configStatus != null and configStatus != ''">
                config_status = #{configStatus,jdbcType=VARCHAR},
            </if>
            <if test="createdId != null">
                created_id = #{createdId,jdbcType=BIGINT},
            </if>
            <if test="updatedId != null">
                updated_id = #{updatedId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateOtherConfigsToHistory">
        update sys_configuration
        set config_status = 'history',
            updated_id    = #{userId,jdbcType=BIGINT}
        where id != #{configId,jdbcType=BIGINT}
          and config_type = #{configType,jdbcType=VARCHAR}
          and terminal_code = #{terminalCode,jdbcType=VARCHAR}
          and config_status = 'in_use'
          and delete_at = 0
    </update>

    <update id="deleteByIdList">
        update sys_configuration
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>