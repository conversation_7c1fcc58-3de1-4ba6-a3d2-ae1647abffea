<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.warehouse.AreaMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.warehouse.Area">
        <!--@mbg.generated-->
        <!--@Table area-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_id,
        area_name,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectNameList" resultMap="BaseResultMap">
        select id, area_name
        from area
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.warehouse.Area"
            useGeneratedKeys="true">
        insert
        into area (warehouse_id, area_name, visual_config, created_id, updated_id)
        values (#{warehouseId,jdbcType=BIGINT}, #{areaName,jdbcType=VARCHAR}, #{visualConfig,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.warehouse.Area">
        update area
        set area_name     = #{areaName,jdbcType=VARCHAR},
            visual_config = #{visualConfig,jdbcType=VARCHAR},
            updated_id    = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update area
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>