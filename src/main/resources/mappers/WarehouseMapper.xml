<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.warehouse.Warehouse">
        <!--@mbg.generated-->
        <!--@Table warehouse-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="warehouse_floor_id" jdbcType="BIGINT" property="warehouseFloorId"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        warehouse_floor_id,
        warehouse_no,
        warehouse_name,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        where delete_at = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() != 0">
            and id in
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="warehouseFloorId != null">
            and warehouse_floor_id = #{warehouseFloorId,jdbcType=BIGINT}
        </if>
        order by id desc
    </select>

    <select id="selectNoAndNameList" resultMap="BaseResultMap">
        select id, warehouse_floor_id, warehouse_no, warehouse_name
        from warehouse where id in
        <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="existByFloorId" resultType="int">
        select count(*)
        from warehouse
        where warehouse_floor_id = #{warehouseFloorId,jdbcType=BIGINT}
          and delete_at = 0
        limit 1
    </select>

    <select id="existByFloorIdAndWarehouseNo" resultType="int">
        select count(*)
        from warehouse
        where warehouse_floor_id = #{warehouseFloorId,jdbcType=BIGINT}
          and warehouse_no = #{warehouseNo,jdbcType=INTEGER}
          and delete_at = 0
        limit 1
    </select>

    <select id="existById" resultType="int">
        select count(*)
        from warehouse
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
        limit 1
    </select>

    <select id="existByOrganizationId" resultType="int">
        select count(*)
        from warehouse
        where organization_id = #{organizationId,jdbcType=BIGINT}
          and delete_at = 0
        limit 1
    </select>

    <select id="count" resultType="int">
        select count(*)
        from warehouse
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.warehouse.Warehouse" useGeneratedKeys="true">
        insert into warehouse (organization_id, warehouse_floor_id, warehouse_no, warehouse_name,
                               visual_config, created_id, updated_id)
        values (#{organizationId,jdbcType=BIGINT}, #{warehouseFloorId,jdbcType=BIGINT}, #{warehouseNo,jdbcType=INTEGER},
                #{warehouseName,jdbcType=VARCHAR},
                #{visualConfig,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.warehouse.Warehouse">
        update warehouse
        set warehouse_no   = #{warehouseNo,jdbcType=INTEGER},
            warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            visual_config  = #{visualConfig,jdbcType=VARCHAR},
            updated_id     = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update warehouse
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </update>
</mapper>