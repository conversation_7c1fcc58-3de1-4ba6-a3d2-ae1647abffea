<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.inventory.InventoryDetailMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.inventory.InventoryDetail">
        <!--@mbg.generated-->
        <!--@Table inventory_detail-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="inventory_record_id" jdbcType="BIGINT" property="inventoryRecordId"/>
        <result column="rack_group_no" jdbcType="INTEGER" property="rackGroupNo"/>
        <result column="rack_column_no" jdbcType="INTEGER" property="rackColumnNo"/>
        <result column="rack_panel_no" jdbcType="INTEGER" property="rackPanelNo"/>
        <result column="rack_section_no" jdbcType="INTEGER" property="rackSectionNo"/>
        <result column="rack_layer_no" jdbcType="INTEGER" property="rackLayerNo"/>
        <result column="rack_grid_no" jdbcType="INTEGER" property="rackGridNo"/>
        <result column="hr_cabinet_group_no" jdbcType="INTEGER" property="hrCabinetGroupNo"/>
        <result column="hr_cabinet_no" jdbcType="INTEGER" property="hrCabinetNo"/>
        <result column="hr_cabinet_layer_no" jdbcType="INTEGER" property="hrCabinetLayerNo"/>
        <result column="hr_cabinet_grid_no" jdbcType="INTEGER" property="hrCabinetGridNo"/>
        <result column="cabinet_group_no" jdbcType="INTEGER" property="cabinetGroupNo"/>
        <result column="cabinet_no" jdbcType="INTEGER" property="cabinetNo"/>
        <result column="cabinet_grid_no" jdbcType="INTEGER" property="cabinetGridNo"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="archive_no" jdbcType="VARCHAR" property="archiveNo"/>
        <result column="archive_name" jdbcType="VARCHAR" property="archiveName"/>
        <result column="tid" jdbcType="VARCHAR" property="tid"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        inventory_record_id,
        rack_group_no,
        rack_column_no,
        rack_panel_no,
        rack_section_no,
        rack_layer_no,
        rack_grid_no,
        hr_cabinet_group_no,
        hr_cabinet_no,
        hr_cabinet_layer_no,
        hr_cabinet_grid_no,
        cabinet_group_no,
        cabinet_no,
        cabinet_grid_no,
        archive_id,
        archive_no,
        archive_name,
        tid,
        `status`,
        created_at,
        updated_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from inventory_detail
        <where>
            <if test="inventoryRecordId != null">
                and inventory_record_id = #{inventoryRecordId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into inventory_detail
        (inventory_record_id, rack_group_no, rack_column_no, rack_panel_no, rack_section_no,
         rack_layer_no, rack_grid_no, hr_cabinet_group_no, hr_cabinet_no, hr_cabinet_layer_no,
         hr_cabinet_grid_no, cabinet_group_no, cabinet_no, cabinet_grid_no, archive_id,
         archive_no, archive_name, tid, `status`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.inventoryRecordId,jdbcType=BIGINT}, #{item.rackGroupNo,jdbcType=INTEGER},
             #{item.rackColumnNo,jdbcType=INTEGER}, #{item.rackPanelNo,jdbcType=INTEGER},
             #{item.rackSectionNo,jdbcType=INTEGER},
             #{item.rackLayerNo,jdbcType=INTEGER}, #{item.rackGridNo,jdbcType=INTEGER},
             #{item.hrCabinetGroupNo,jdbcType=INTEGER},
             #{item.hrCabinetNo,jdbcType=INTEGER}, #{item.hrCabinetLayerNo,jdbcType=INTEGER},
             #{item.hrCabinetGridNo,jdbcType=INTEGER}, #{item.cabinetGroupNo,jdbcType=INTEGER},
             #{item.cabinetNo,jdbcType=INTEGER}, #{item.cabinetGridNo,jdbcType=INTEGER},
             #{item.archiveId,jdbcType=BIGINT}, #{item.archiveNo,jdbcType=VARCHAR},
             #{item.archiveName,jdbcType=VARCHAR},
             #{item.tid,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>