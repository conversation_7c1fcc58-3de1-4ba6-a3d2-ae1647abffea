<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.RoleMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.Role">
        <!--@mbg.generated-->
        <!--@Table `sys_role`-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="system_flag" jdbcType="TINYINT" property="systemFlag"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        role_name,
        description,
        system_flag,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_role
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_role
        where delete_at = 0
        <if test="roleNameLike != null">
            <bind name="_roleNameLike" value="'%' + roleNameLike + '%'"/>
            and role_name like #{_roleNameLike,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_role
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectByRoleName" resultType="java.lang.Integer">
        select count(*)
        from sys_role
        where role_name = #{roleName,jdbcType=VARCHAR}
          and delete_at = 0
        limit 1
    </select>

    <insert id="insert">
        insert into sys_role (role_name, description, created_id, updated_id)
        values (#{roleName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="update">
        update sys_role
        set role_name   = #{roleName,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            updated_id  = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByIdList">
        delete
        from sys_role
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </delete>
</mapper>
