<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EnvEnergyRecordMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EnvEnergyRecord">
        <!--@mbg.generated-->
        <!--@Table env_energy_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="device_id" jdbcType="BIGINT" property="deviceId"/>
        <result column="voltage" jdbcType="FLOAT" property="voltage"/>
        <result column="current" jdbcType="FLOAT" property="current"/>
        <result column="power" jdbcType="FLOAT" property="power"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_id,
        area_id,
        device_id,
        voltage,
        `current`,
        `power`,
        created_at
    </sql>

    <select id="selectListByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_energy_record
        where area_id is null
          and device_id is null
        <choose>
            <when test="warehouseId != null">
                and warehouse_id = #{warehouseId,jdbcType=BIGINT}
            </when>
            <otherwise>
                and warehouse_id is null
            </otherwise>
        </choose>

        <if test="beginCreatedAt != null">
            and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreatedAt != null">
            and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <select id="selectListByAreaId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_energy_record
        where area_id = #{areaId,jdbcType=BIGINT}
        <if test="beginCreatedAt != null">
            and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreatedAt != null">
            and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <select id="selectListByDeviceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_energy_record
        where device_id = #{deviceId,jdbcType=BIGINT}
        <if test="beginCreatedAt != null">
            and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreatedAt != null">
            and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.device.EnvEnergyRecord" useGeneratedKeys="true">
        insert into env_energy_record (warehouse_id, area_id, device_id, voltage,
                                       `current`, `power`)
        values (#{warehouseId,jdbcType=BIGINT}, #{areaId,jdbcType=BIGINT}, #{deviceId,jdbcType=BIGINT},
                #{voltage,jdbcType=FLOAT}, #{current,jdbcType=FLOAT}, #{power,jdbcType=FLOAT})
    </insert>
</mapper>