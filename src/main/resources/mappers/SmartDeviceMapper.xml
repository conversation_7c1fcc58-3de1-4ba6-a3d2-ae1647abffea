<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.SmartDeviceMapper">
  <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.SmartDevice">
    <!--@mbg.generated-->
    <!--@Table smart_device-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="device_status" jdbcType="VARCHAR" property="deviceStatus" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo" />
    <result column="device_ip" jdbcType="VARCHAR" property="deviceIp" />
    <result column="device_port" jdbcType="INTEGER" property="devicePort" />
    <result column="visual_config" jdbcType="VARCHAR" property="visualConfig" />
    <result column="created_id" jdbcType="BIGINT" property="createdId" />
    <result column="updated_id" jdbcType="BIGINT" property="updatedId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="delete_at" jdbcType="BIGINT" property="deleteAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, warehouse_id, device_type, device_status, device_name, device_no, device_ip, 
    device_port, visual_config, created_id, updated_id, created_at, updated_at, delete_at
  </sql>
</mapper>