<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.inventory.InventoryRecordMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.inventory.InventoryRecord">
        <!--@mbg.generated-->
        <!--@Table inventory_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="inventory_task_id" jdbcType="BIGINT" property="inventoryTaskId"/>
        <result column="device_id" jdbcType="BIGINT" property="deviceId"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="inventor_type" jdbcType="VARCHAR" property="inventorType"/>
        <result column="on_shelf_count" jdbcType="INTEGER" property="onShelfCount"/>
        <result column="off_shelf_count" jdbcType="INTEGER" property="offShelfCount"/>
        <result column="misplaced_count" jdbcType="INTEGER" property="misplacedCount"/>
        <result column="borrow_count" jdbcType="INTEGER" property="borrowCount"/>
        <result column="unbind_count" jdbcType="INTEGER" property="unbindCount"/>
        <result column="rack_group_no" jdbcType="INTEGER" property="rackGroupNo"/>
        <result column="rack_column_no" jdbcType="INTEGER" property="rackColumnNo"/>
        <result column="rack_panel_no" jdbcType="INTEGER" property="rackPanelNo"/>
        <result column="rack_section_no" jdbcType="INTEGER" property="rackSectionNo"/>
        <result column="rack_layer_no" jdbcType="INTEGER" property="rackLayerNo"/>
        <result column="hr_cabinet_group_no" jdbcType="INTEGER" property="hrCabinetGroupNo"/>
        <result column="hr_cabinet_no" jdbcType="INTEGER" property="hrCabinetNo"/>
        <result column="hr_cabinet_layer_no" jdbcType="INTEGER" property="hrCabinetLayerNo"/>
        <result column="cabinet_group_no" jdbcType="INTEGER" property="cabinetGroupNo"/>
        <result column="cabinet_no" jdbcType="INTEGER" property="cabinetNo"/>
        <result column="cabinet_grid_no" jdbcType="INTEGER" property="cabinetGridNo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        inventory_task_id,
        device_id,
        warehouse_id,
        inventor_type,
        on_shelf_count,
        off_shelf_count,
        misplaced_count,
        borrow_count,
        unbind_count,
        rack_group_no,
        rack_column_no,
        rack_panel_no,
        rack_section_no,
        rack_layer_no,
        hr_cabinet_group_no,
        hr_cabinet_no,
        hr_cabinet_layer_no,
        cabinet_group_no,
        cabinet_no,
        cabinet_grid_no,
        remark,
        created_id,
        created_at,
        updated_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from inventory_record
        <where>
            <if test="organizationId != null">
                and organization_id = #{organizationId,jdbcType=BIGINT}
            </if>
            <if test="rackGroupNo != null">
                and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
            </if>
            <if test="hrCabinetGroupNo != null">
                and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
            </if>
            <if test="cabinetGroupNo != null">
                and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
            </if>
            <if test="beginCreateAt != null">
                and created_at &gt;= #{beginCreateAt,jdbcType=TIMESTAMP}
            </if>
            <if test="endCreateAt != null">
                and created_at &lt;= #{endCreateAt,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from inventory_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.inventory.InventoryRecord" useGeneratedKeys="true">
        insert into inventory_record (organization_id, inventory_task_id, device_id,
                                      warehouse_id, inventor_type, on_shelf_count,
                                      off_shelf_count, misplaced_count, borrow_count,
                                      unbind_count, rack_group_no, rack_column_no,
                                      rack_panel_no, rack_section_no, rack_layer_no,
                                      hr_cabinet_group_no, hr_cabinet_no, hr_cabinet_layer_no,
                                      cabinet_group_no, cabinet_no, cabinet_grid_no,
                                      remark, created_id)
        values (#{organizationId,jdbcType=BIGINT}, #{inventoryTaskId,jdbcType=BIGINT}, #{deviceId,jdbcType=BIGINT},
                #{warehouseId,jdbcType=BIGINT}, #{inventorType,jdbcType=VARCHAR}, #{onShelfCount,jdbcType=INTEGER},
                #{offShelfCount,jdbcType=INTEGER}, #{misplacedCount,jdbcType=INTEGER}, #{borrowCount,jdbcType=INTEGER},
                #{unbindCount,jdbcType=INTEGER}, #{rackGroupNo,jdbcType=INTEGER}, #{rackColumnNo,jdbcType=INTEGER},
                #{rackPanelNo,jdbcType=INTEGER}, #{rackSectionNo,jdbcType=INTEGER}, #{rackLayerNo,jdbcType=INTEGER},
                #{hrCabinetGroupNo,jdbcType=INTEGER}, #{hrCabinetNo,jdbcType=INTEGER},
                #{hrCabinetLayerNo,jdbcType=INTEGER}, #{cabinetGroupNo,jdbcType=INTEGER},
                #{cabinetNo,jdbcType=INTEGER}, #{cabinetGridNo,jdbcType=INTEGER},
                #{remark,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT})
    </insert>
</mapper>