<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EnvAirQualityRecordMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EnvAirQualityRecord">
        <!--@mbg.generated-->
        <!--@Table env_air_quality_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="device_id" jdbcType="BIGINT" property="deviceId"/>
        <result column="temperature" jdbcType="FLOAT" property="temperature"/>
        <result column="humidity" jdbcType="FLOAT" property="humidity"/>
        <result column="pm2_5" jdbcType="FLOAT" property="pm25"/>
        <result column="pm10" jdbcType="FLOAT" property="pm10"/>
        <result column="tvoc" jdbcType="FLOAT" property="tvoc"/>
        <result column="co2" jdbcType="FLOAT" property="co2"/>
        <result column="hcho" jdbcType="FLOAT" property="hcho"/>
        <result column="o3" jdbcType="FLOAT" property="o3"/>
        <result column="light" jdbcType="FLOAT" property="light"/>
        <result column="no2" jdbcType="FLOAT" property="no2"/>
        <result column="so2" jdbcType="FLOAT" property="so2"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_id,
        area_id,
        device_id,
        temperature,
        humidity,
        pm2_5,
        pm10,
        tvoc,
        co2,
        hcho,
        o3,
        light,
        no2,
        so2,
        created_at
    </sql>

    <select id="selectListByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_air_quality_record
        where area_id is null
          and device_id is null
        <choose>
            <when test="warehouseId != null">
                and warehouse_id = #{warehouseId,jdbcType=BIGINT}
            </when>
            <otherwise>
                and warehouse_id is null
            </otherwise>
        </choose>

        <if test="beginCreatedAt != null">
            and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreatedAt != null">
            and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <select id="selectListByAreaId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_air_quality_record
        where area_id = #{areaId,jdbcType=BIGINT}
        <if test="beginCreatedAt != null">
            and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreatedAt != null">
            and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <select id="selectListByDeviceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_air_quality_record
        where device_id = #{deviceId,jdbcType=BIGINT}
        <if test="beginCreatedAt != null">
            and created_at &gt;= #{beginCreatedAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreatedAt != null">
            and created_at &lt;= #{endCreatedAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.device.EnvAirQualityRecord" useGeneratedKeys="true">
        insert into env_air_quality_record (warehouse_id, area_id, device_id, temperature,
                                            humidity, pm2_5, pm10,
                                            tvoc, co2, hcho, o3,
                                            light, no2, so2)
        values (#{warehouseId,jdbcType=BIGINT}, #{areaId,jdbcType=BIGINT}, #{deviceId,jdbcType=BIGINT},
                #{temperature,jdbcType=DOUBLE}, #{humidity,jdbcType=DOUBLE}, #{pm25,jdbcType=DOUBLE},
                #{pm10,jdbcType=DOUBLE}, #{tvoc,jdbcType=DOUBLE}, #{co2,jdbcType=DOUBLE},
                #{hcho,jdbcType=DOUBLE}, #{o3,jdbcType=DOUBLE}, #{light,jdbcType=DOUBLE},
                #{no2,jdbcType=DOUBLE}, #{so2,jdbcType=DOUBLE})
    </insert>
</mapper>