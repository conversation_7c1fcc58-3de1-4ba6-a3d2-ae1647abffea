<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.inventory.RobotInventoryRecordDetailMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.inventory.RobotInventoryRecordDetail">
        <!--@mbg.generated-->
        <!--@Table robot_inventory_record_detail-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="robot_inventory_record_id" jdbcType="BIGINT" property="robotInventoryRecordId"/>
        <result column="rack_group_id" jdbcType="BIGINT" property="rackGroupId"/>
        <result column="rack_colum_no" jdbcType="BIGINT" property="rackColumNo"/>
        <result column="rack_panel_no" jdbcType="BIGINT" property="rackPanelNo"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="archive_no" jdbcType="VARCHAR" property="archiveNo"/>
        <result column="archive_name" jdbcType="VARCHAR" property="archiveName"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, robot_inventory_record_id, rack_group_id, rack_colum_no, rack_panel_no, archive_id,
        archive_no, archive_name, created_at, updated_at
    </sql>
</mapper>