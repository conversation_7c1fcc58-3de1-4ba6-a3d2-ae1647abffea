<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.cabinet.CabinetGridMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.cabinet.CabinetGrid">
        <!--@mbg.generated-->
        <!--@Table cabinet_grid-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cabinet_group_id" jdbcType="BIGINT" property="cabinetGroupId"/>
        <result column="cabinet_id" jdbcType="BIGINT" property="cabinetId"/>
        <result column="capacity" jdbcType="INTEGER" property="capacity"/>
        <result column="used_capacity" jdbcType="INTEGER" property="usedCapacity"/>
        <result column="grid_status" jdbcType="VARCHAR" property="gridStatus"/>
        <result column="grid_no" jdbcType="INTEGER" property="gridNo"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        cabinet_group_id,
        cabinet_id,
        capacity,
        used_capacity,
        grid_status,
        grid_no,
        created_id,
        updated_id,
        created_at,
        updated_at
    </sql>

    <select id="selectListByCabinetGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cabinet_grid
        where cabinet_group_id = #{cabinetGroupId}
    </select>

    <select id="selectListByCabinetIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cabinet_grid
        where cabinet_id in
        <foreach close=")" collection="cabinetIdList" item="item" open="(" separator=", ">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectLocationList" resultType="org.zxwl.smart.mybatis.ext.cabinet.CabinetGridLocationExt">
        select cabinet_group.organization_id,
               cabinet_group.group_no,
               cabinet.cabinet_no,
               cabinet_grid.grid_no,
               cabinet_grid.id as grid_id,
               cabinet_grid.capacity,
               cabinet_grid.used_capacity,
               cabinet_grid.grid_status
        from cabinet_group
                 inner join cabinet on cabinet_group.id = cabinet.cabinet_group_id
                 inner join cabinet_grid on cabinet.id = cabinet_grid.cabinet_id
        where cabinet_group.delete_at = 0
          and (cabinet_group.group_no, cabinet.cabinet_no, cabinet_grid.grid_no) in
        <foreach close=")" collection="wrapperList" item="item" open="(" separator=",">
            (#{item.groupNo,jdbcType=INTEGER}, #{item.cabinetNo,jdbcType=INTEGER},
             #{item.gridNo,jdbcType=INTEGER})
        </foreach>
    </select>

    <select id="selectListByCabinetGroupIdForStatTask" resultMap="BaseResultMap">
        select id,
               cabinet_group_id,
               cabinet_id,
               grid_no
        from cabinet_grid
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </select>

    <select id="selectLocation" resultType="org.zxwl.smart.mybatis.ext.cabinet.CabinetGridLocationExt">
        select cabinet_group.organization_id,
               cabinet_group.group_no,
               cabinet.cabinet_no,
               cabinet_grid.grid_no,
               cabinet_grid.id as grid_id,
               cabinet_grid.capacity,
               cabinet_grid.used_capacity,
               cabinet_grid.grid_status
        from cabinet_group
                 inner join cabinet on cabinet_group.id = cabinet.cabinet_group_id
                 inner join cabinet_grid on cabinet.id = cabinet_grid.cabinet_id
        where cabinet_group.delete_at = 0
          and cabinet_group.group_no = #{groupNo,jdbcType=INTEGER}
          and cabinet.cabinet_no = #{cabinetNo,jdbcType=INTEGER}
          and cabinet_grid.grid_no = #{gridNo,jdbcType=INTEGER}
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into cabinet_grid
        (cabinet_group_id, cabinet_id, capacity, used_capacity, grid_status, grid_no, created_id,
         updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.cabinetGroupId,jdbcType=BIGINT}, #{item.cabinetId,jdbcType=BIGINT},
             #{item.capacity,jdbcType=INTEGER}, #{item.usedCapacity,jdbcType=INTEGER},
             #{item.gridStatus,jdbcType=VARCHAR}, #{item.gridNo,jdbcType=INTEGER}, #{item.createdId,jdbcType=BIGINT},
             #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateUsedCapacityForStatTask" parameterType="java.util.List">
        update cabinet_grid
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                        then #{item.usedCapacity,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteByCabinetGroupId">
        delete
        from cabinet_grid
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </delete>
</mapper>