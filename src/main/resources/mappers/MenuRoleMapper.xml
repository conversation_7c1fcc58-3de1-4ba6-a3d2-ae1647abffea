<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.MenuRoleMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.MenuRole">
        <!--@mbg.generated-->
        <!--@Table sys_menu_role-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="menu_id" jdbcType="BIGINT" property="menuId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        menu_id,
        role_id
    </sql>

    <select id="selectMenuIdListByRoleId" resultType="java.lang.Long">
        select menu_id
        from sys_menu_role
        where role_id = #{roleId,jdbcType=BIGINT}
    </select>

    <select id="selectMenuIdListByRoleIdList" resultType="java.lang.Long">
        select menu_id
        from sys_menu_role
        where role_id in
        <foreach collection="roleIdList" item="roleId" open="(" separator="," close=")">
            #{roleId,jdbcType=BIGINT}
        </foreach>
    </select>

    <insert id="insertBatch">
        insert into sys_menu_role (menu_id, role_id)
        values
        <foreach collection="menuRoleList" item="item" separator=",">
            (#{item.menuId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByRoleId">
        delete
        from sys_menu_role
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>
</mapper>