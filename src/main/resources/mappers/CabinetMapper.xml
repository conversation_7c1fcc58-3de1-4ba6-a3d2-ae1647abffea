<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.cabinet.CabinetMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.cabinet.Cabinet">
        <!--@mbg.generated-->
        <!--@Table cabinet-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cabinet_group_id" jdbcType="BIGINT" property="cabinetGroupId"/>
        <result column="cabinet_no" jdbcType="INTEGER" property="cabinetNo"/>
        <result column="is_main_cabinet" jdbcType="TINYINT" property="mainCabinet"/>
        <result column="cabinet_grid_num" jdbcType="INTEGER" property="cabinetGridNum"/>
        <result column="capacity" jdbcType="INTEGER" property="capacity"/>
        <result column="used_capacity" jdbcType="INTEGER" property="usedCapacity"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        cabinet_group_id,
        cabinet_no,
        is_main_cabinet,
        cabinet_grid_num,
        capacity,
        used_capacity,
        created_id,
        updated_id,
        created_at,
        updated_at
    </sql>

    <select id="selectListByCabinetGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cabinet
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </select>

    <select id="selectByCabinetGroupIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cabinet
        where cabinet_group_id in
        <foreach close=")" collection="cabinetGroupIdList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectListByCabinetGroupIdForStatTask" resultMap="BaseResultMap">
        select id,
               cabinet_group_id,
               cabinet_no
        from cabinet
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into cabinet
        (cabinet_group_id, cabinet_no, is_main_cabinet, cabinet_grid_num, capacity, used_capacity, created_id,
         updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.cabinetGroupId,jdbcType=BIGINT}, #{item.cabinetNo,jdbcType=INTEGER},
             #{item.mainCabinet,jdbcType=TINYINT}, #{item.cabinetGridNum,jdbcType=INTEGER},
             #{item.capacity,jdbcType=INTEGER}, #{item.usedCapacity,jdbcType=INTEGER},
             #{item.createdId,jdbcType=BIGINT}, #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateUsedCapacityForStatTask" parameterType="java.util.List">
        update cabinet
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                        then #{item.usedCapacity,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteByCabinetGroupId">
        delete
        from cabinet
        where cabinet_group_id = #{cabinetGroupId,jdbcType=BIGINT}
    </delete>
</mapper>