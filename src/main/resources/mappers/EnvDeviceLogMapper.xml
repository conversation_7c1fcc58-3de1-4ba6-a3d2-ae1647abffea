<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EnvDeviceLogMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EnvDeviceLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="device_id" jdbcType="BIGINT" property="deviceId"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="operate_context" jdbcType="VARCHAR" property="operateContext"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        warehouse_id,
        device_id,
        device_type,
        device_name,
        operate_context,
        created_id,
        created_at
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device_log
        where id = #{logId,jdbcType=BIGINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from env_device_log
        <where>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId}
            </if>
            <if test="deviceIdList != null and deviceIdList.size() != 0">
                and device_id in
                <foreach collection="deviceIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="createdId != null">
                and created_id = #{createdId}
            </if>
            <if test="createdAtBegin != null">
                and created_at &gt;= #{createdAtBegin}
            </if>
            <if test="createdAtEnd != null">
                and created_at &lt;= #{createdAtEnd}
            </if>
        </where>
        order by created_at desc
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.device.EnvDeviceLog" useGeneratedKeys="true">
        insert into env_device_log (warehouse_id, device_id, device_type, device_name, operate_context,
                                    created_id)
        values (#{warehouseId,jdbcType=BIGINT}, #{deviceId,jdbcType=BIGINT}, #{deviceType,jdbcType=VARCHAR},
                #{deviceName,jdbcType=VARCHAR}, #{operateContext,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT})
    </insert>

    <insert id="insertList" keyColumn="id" keyProperty="id"
            parameterType="java.util.List" useGeneratedKeys="true">
        insert into env_device_log (warehouse_id, device_id, device_type, device_name, operate_context,
                                    created_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseId,jdbcType=BIGINT}, #{item.deviceId,jdbcType=BIGINT},
             #{item.deviceType,jdbcType=VARCHAR}, #{item.deviceName,jdbcType=VARCHAR},
             #{item.operateContext,jdbcType=VARCHAR}, #{item.createdId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByIdList">
        delete
        from env_device_log
        where id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
</mapper>