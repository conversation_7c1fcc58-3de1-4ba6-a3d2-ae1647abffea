<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.Archive">
        <!--@mbg.generated-->
        <!--@Table archive-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="archive_type_id" jdbcType="BIGINT" property="archiveTypeId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="stock_status" jdbcType="VARCHAR" property="stockStatus"/>
        <result column="shelving_status" jdbcType="VARCHAR" property="shelvingStatus"/>
        <result column="archive_name" jdbcType="VARCHAR" property="archiveName"/>
        <result column="archive_no" jdbcType="VARCHAR" property="archiveNo"/>
        <result column="tid" jdbcType="VARCHAR" property="tid"/>
        <result column="rack_group_no" jdbcType="INTEGER" property="rackGroupNo"/>
        <result column="rack_column_no" jdbcType="INTEGER" property="rackColumnNo"/>
        <result column="rack_panel_No" jdbcType="INTEGER" property="rackPanelNo"/>
        <result column="rack_section_no" jdbcType="INTEGER" property="rackSectionNo"/>
        <result column="rack_layer_no" jdbcType="INTEGER" property="rackLayerNo"/>
        <result column="rack_grid_no" jdbcType="INTEGER" property="rackGridNo"/>
        <result column="hr_cabinet_group_no" jdbcType="INTEGER" property="hrCabinetGroupNo"/>
        <result column="hr_cabinet_no" jdbcType="INTEGER" property="hrCabinetNo"/>
        <result column="hr_cabinet_layer_no" jdbcType="INTEGER" property="hrCabinetLayerNo"/>
        <result column="hr_cabinet_grid_no" jdbcType="INTEGER" property="hrCabinetGridNo"/>
        <result column="cabinet_group_no" jdbcType="INTEGER" property="cabinetGroupNo"/>
        <result column="cabinet_no" jdbcType="INTEGER" property="cabinetNo"/>
        <result column="cabinet_grid_no" jdbcType="INTEGER" property="cabinetGridNo"/>
        <result column="shelving_id" jdbcType="BIGINT" property="shelvingId"/>
        <result column="shelving_at" jdbcType="TIMESTAMP" property="shelvingAt"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        warehouse_id,
        archive_type_id,
        parent_id,
        `type`,
        stock_status,
        shelving_status,
        archive_name,
        archive_no,
        tid,
        rack_group_no,
        rack_column_no,
        rack_panel_No,
        rack_section_no,
        rack_layer_no,
        rack_grid_no,
        hr_cabinet_group_no,
        hr_cabinet_no,
        hr_cabinet_layer_no,
        hr_cabinet_grid_no,
        cabinet_group_no,
        cabinet_no,
        cabinet_grid_no,
        shelving_id,
        shelving_at,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>
    <sql id="Base_Column_List_With_Archive">
        <!--@mbg.generated-->
        archive.id,
        archive.organization_id,
        archive.warehouse_id,
        archive.archive_type_id,
        archive.parent_id,
        archive.`type`,
        archive.stock_status,
        archive.shelving_status,
        archive.archive_name,
        archive.archive_no,
        archive.tid,
        archive.rack_group_no,
        archive.rack_column_no,
        archive.rack_panel_No,
        archive.rack_section_no,
        archive.rack_layer_no,
        archive.rack_grid_no,
        archive.hr_cabinet_group_no,
        archive.hr_cabinet_no,
        archive.hr_cabinet_layer_no,
        archive.hr_cabinet_grid_no,
        archive.cabinet_group_no,
        archive.cabinet_no,
        archive.cabinet_grid_no,
        archive.shelving_id,
        archive.shelving_at,
        archive.created_id,
        archive.updated_id,
        archive.created_at,
        archive.updated_at,
        archive.delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where delete_at = 0
          and type = #{type,jdbcType=VARCHAR}
        <if test="organizationId != null">
            and organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="archiveTypeId != null">
            and archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId,jdbcType=BIGINT}
        </if>
        <if test="type != null">
            and type = #{type,jdbcType=VARCHAR}
        </if>
        <if test="stockStatus != null">
            and stock_status = #{stockStatus,jdbcType=VARCHAR}
        </if>
        <if test="shelvingStatusList != null and shelvingStatusList.size() != 0">
            and shelving_status in
            <foreach collection="shelvingStatusList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="archiveName != null">
            and archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null">
            and archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        <if test="tidList != null and tidList.size() != 0">
            and tid in
            <foreach close=")" collection="tidList" item="tid" open="(" separator=",">
                #{tid,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="bindTid != null">
            <choose>
                <when test="bindTid == true">
                    and tid is not null
                </when>
                <otherwise>
                    and tid is null
                </otherwise>
            </choose>
        </if>
        <if test="rackGroupNo != null">
            and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="rackColumnNo != null">
            and rack_column_no = #{rackColumnNo,jdbcType=INTEGER}
        </if>
        <if test="rackPanelNo != null">
            and rack_panel_no = #{rackPanelNo,jdbcType=INTEGER}
        </if>
        <if test="rackSectionNo != null">
            and rack_section_no = #{rackSectionNo,jdbcType=INTEGER}
        </if>
        <if test="rackLayerNo != null">
            and rack_layer_no = #{rackLayerNo,jdbcType=INTEGER}
        </if>
        <if test="rackGridNo != null">
            and rack_grid_no = #{rackGridNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetNo != null">
            and hr_cabinet_no = #{hrCabinetNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetLayerNo != null">
            and hr_cabinet_layer_no = #{hrCabinetLayerNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetNo != null">
            and cabinet_no = #{cabinetNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGridNo != null">
            and cabinet_grid_no = #{cabinetGridNo,jdbcType=INTEGER}
        </if>
        order by id desc
    </select>

    <select id="selectPendingOutListByAccountLocal"
            resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveIdWithApplicantIdExt">
        select archive.id as archive_id, max(archive_applicant.id) as applicant_id
        from archive
                 inner join archive_applicant on archive.id = archive_applicant.archive_id
                 inner join archive_approval on archive_applicant.id = archive_approval.archive_applicant_id
                 inner join archive_task on archive_approval.id = archive_task.archive_approval_id
        where archive.shelving_status = 'pending_out'
          and archive_applicant.status = 'approved'
          and archive_task.task_type = 'out'
          and archive_task.status != 'completed'
        <if test="organizationId != null">
            and archive.organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="userId != null">
            and archive_applicant.created_id = #{userId,jdbcType=BIGINT}
        </if>
        <if test="rackGroupNo != null">
            and archive.rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and archive.hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and archive.cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
        group by archive.id
    </select>

    <select id="selectListForDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where delete_at != 0
        <if test="type != null and type != ''">
            and type = #{type,jdbcType=VARCHAR}
        </if>
        <if test="organizationId != null and organizationId != ''">
            and organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="archiveTypeId != null and archiveTypeId != ''">
            and archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
        </if>
        <if test="archiveName != null and archiveName != ''">
            and archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        order by id desc
    </select>

    <select id="selectListByCabinetGroupNo" resultMap="BaseResultMap">
        select id,
               `type`,
               shelving_status,
               archive_name,
               archive_no,
               tid,
               cabinet_group_no,
               cabinet_no,
               cabinet_grid_no
        from archive
        where delete_at = 0
          and shelving_status in
        <foreach item="item" collection="shelvingStatus" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
    </select>

    <select id="selectListByHrCabinetGroupNo" resultMap="BaseResultMap">
        select id,
               `type`,
               shelving_status,
               archive_name,
               archive_no,
               tid,
               hr_cabinet_group_no,
               hr_cabinet_no,
               hr_cabinet_layer_no,
               hr_cabinet_grid_no
        from archive
        where delete_at = 0
          and shelving_status in
        <foreach item="item" collection="shelvingStatus" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
    </select>

    <select id="selectListByRackGroupNo" resultMap="BaseResultMap">
        select id,
               `type`,
               shelving_status,
               archive_name,
               archive_no,
               tid,
               rack_group_no,
               rack_column_no,
               rack_panel_No,
               rack_section_no,
               rack_layer_no,
               rack_grid_no
        from archive
        where delete_at = 0
          and shelving_status in
        <foreach item="item" collection="shelvingStatus" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        <if test="rackColumnNo != null">
            and rack_column_no = #{rackColumnNo,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectNameAndNoListByIdList" resultMap="BaseResultMap">
        select id,
               archive_type_id,
               type,
               archive_name,
               archive_no
        from archive
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectListByParentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where parent_id = #{parentId,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="selectListByParentIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where parent_id in
        <foreach close=")" collection="parentIdList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectListByTidList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where tid in
        <foreach collection="tidList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectIdListByParentIdList" resultMap="BaseResultMap">
        select id, parent_id
        from archive
        where delete_at = 0
          and parent_id in
        <foreach close=")" collection="parentIdList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>


    <select id="selectSubArchiveCountList" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveSubCountExt">
        select parent_id, count(*) as subArchiveCount
        from archive
        where delete_at = 0
          and parent_id in
        <foreach close=")" collection="parentIdList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        group by parent_id
    </select>

    <select id="selectArchiveTypeStat" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveTypeStat">
        select archive_type_id,
               count(*) as count
        from archive
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
        <if test="rackGroupNo != null">
            and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
        group by archive_type_id
    </select>

    <select id="selectStockStatusStat" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveStockStatusStat">
        select stock_status,
               count(*) as count
        from archive
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
        <if test="rackGroupNo != null">
            and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
        group by stock_status
    </select>

    <select id="selectShelvingStatusStat" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveShelvingStatusStat">
        select shelving_status,
               count(*) as count
        from archive
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
        <if test="rackGroupNo != null">
            and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
        group by shelving_status
    </select>

    <select id="selectStat" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveStatExt">
        select count(*)                                                         as total_count,
               count(tid is not null)                                           as bind_tid_count,
               count(*) - count(tid)                                            as unbind_tid_count,
               sum(case when shelving_status = 'pending_in' then 1 else 0 end)  as shelving_pending_in_count,
               sum(case when shelving_status = 'in' then 1 else 0 end)          as in_shelving_count,
               sum(case when shelving_status = 'pending_out' then 1 else 0 end) as shelving_pending_out_count,
               sum(case when shelving_status = 'out' then 1 else 0 end)         as out_shelving_count
        from archive
        where delete_at = 0
        <if test="rackGroupNo != null">
            and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByIdOrTid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        <where>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT}
            </if>
            <if test="tid != null">
                and tid = #{tid,jdbcType=VARCHAR}
            </if>
            <if test="containDelete == false">
                and delete_at = 0
            </if>
        </where>
    </select>

    <select id="selectListByGroupNoForStatTask" resultMap="BaseResultMap">
        select id,
               rack_group_no,
               rack_column_no,
               rack_panel_no,
               rack_section_no,
               rack_layer_no,
               rack_grid_no,
               hr_cabinet_group_no,
               hr_cabinet_no,
               hr_cabinet_layer_no,
               hr_cabinet_grid_no,
               cabinet_group_no,
               cabinet_no,
               cabinet_grid_no
        from archive
        where delete_at = 0
          and shelving_status in
        <foreach item="item" collection="shelvingStatusList" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="rackGroupNo != null">
            and rack_group_no = #{rackGroupNo,jdbcType=INTEGER}
        </if>
        <if test="hrCabinetGroupNo != null">
            and hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER}
        </if>
        <if test="cabinetGroupNo != null">
            and cabinet_group_no = #{cabinetGroupNo,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectArchivePendingStorageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive
        where stock_status = 'out'
          and delete_at = 0
        <if test="archiveName != null and archiveName != ''">
            and archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive.archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive.archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive.archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        <if test="beginCreateAt != null">
            and (select archive_operator_record.created_at
                 from archive_operator_record
                 where archive_id = archive.id
                   and operator_type = 'stock_out'
                 order by id desc
                 limit 1) &gt;= #{beginCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateAt != null">
            and (select archive_operator_record.created_at
                 from archive_operator_record
                 where archive_id = archive.id
                   and operator_type = 'stock_out'
                 order by id desc
                 limit 1) &lt;= #{endCreateAt,jdbcType=TIMESTAMP}
        </if>
        order by id desc
    </select>

    <select id="selectArchiveLastCompletedOutTaskList"
            resultType="org.zxwl.smart.mybatis.entity.archive.ArchiveTask">
        select max(archive_task.id) as id, archive_applicant.archive_id
        from archive_applicant
                 inner join archive_approval on archive_applicant.id = archive_approval.archive_applicant_id
                 inner join archive_task on archive_approval.id = archive_task.archive_approval_id
        where archive_applicant.archive_id in
        <foreach item="item" collection="archivePendingStorageIdList" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and archive_task.task_type = 'out'
        and archive_task.status = 'completed'
        group by archive_applicant.archive_id;
    </select>

    <select id="selectArchivePendingStorageExtList"
            resultType="org.zxwl.smart.mybatis.ext.archive.ArchivePendingStorageExt">
        select archive_applicant.archive_id as archive_id,
               archive_task.created_id      as pickup_created_id,
               archive_task.operator_id     as pickup_operator_id,
               archive_task.completion_time as pickup_time,
               archive_applicant.id         as applicant_id
        from archive_applicant
                 inner join archive_approval on archive_applicant.id = archive_approval.archive_applicant_id
                 inner join archive_task on archive_approval.id = archive_task.archive_approval_id
        where archive_task.id in
        <foreach item="item" collection="archiveTaskIdList" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="existArchiveTypeIdAndArchiveNo" resultType="int">
        select count(*)
        from archive
        where delete_at = 0
          and archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
          and archive_no = #{archiveNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="existByArchiveTypeId" resultType="int">
        select count(*)
        from archive
        where delete_at = 0
          and archive_type_id = #{archiveTypeId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="existByOrganizationId" resultType="int">
        select count(*)
        from archive
        where delete_at = 0
          and organization_id = #{organizationId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="countByWarehouseId" resultType="int">
        select count(*)
        from archive
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="countByParentId" resultType="int">
        select count(*)
        from archive
        where delete_at = 0
          and parent_id = #{parentId,jdbcType=BIGINT}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="org.zxwl.smart.mybatis.entity.archive.Archive"
            useGeneratedKeys="true">
        insert into archive (organization_id, warehouse_id, archive_type_id, parent_id, `type`,
                             stock_status, shelving_status, archive_name,
                             archive_no, tid, rack_group_no,
                             rack_column_no, rack_panel_No, rack_section_no,
                             rack_layer_no, rack_grid_no, hr_cabinet_group_no, hr_cabinet_no, hr_cabinet_layer_no,
                             hr_cabinet_grid_no, cabinet_group_no,
                             cabinet_no, cabinet_grid_no, shelving_id,
                             shelving_at, created_id, updated_id)
        values (#{organizationId,jdbcType=BIGINT}, #{warehouseId,jdbcType=BIGINT}, #{archiveTypeId,jdbcType=BIGINT},
                #{parentId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR},
                #{stockStatus,jdbcType=VARCHAR}, #{shelvingStatus,jdbcType=VARCHAR}, #{archiveName,jdbcType=VARCHAR},
                #{archiveNo,jdbcType=VARCHAR}, #{tid,jdbcType=VARCHAR}, #{rackGroupNo,jdbcType=INTEGER},
                #{rackColumnNo,jdbcType=INTEGER}, #{rackPanelNo,jdbcType=INTEGER}, #{rackSectionNo,jdbcType=INTEGER},
                #{rackLayerNo,jdbcType=INTEGER}, #{rackGridNo,jdbcType=INTEGER}, #{hrCabinetGroupNo,jdbcType=INTEGER},
                #{hrCabinetNo,jdbcType=INTEGER}, #{hrCabinetLayerNo,jdbcType=INTEGER},
                #{hrCabinetGridNo,jdbcType=INTEGER}, #{cabinetGroupNo,jdbcType=INTEGER},
                #{cabinetNo,jdbcType=INTEGER}, #{cabinetGridNo,jdbcType=INTEGER}, #{shelvingId,jdbcType=BIGINT},
                #{shelvingAt,jdbcType=TIMESTAMP}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.archive.Archive">
        update archive
        set warehouse_id        = #{warehouseId,jdbcType=BIGINT},
            archive_type_id     = #{archiveTypeId,jdbcType=BIGINT},
            parent_id           = #{parentId,jdbcType=BIGINT},
            archive_name        = #{archiveName,jdbcType=VARCHAR},
            archive_no          = #{archiveNo,jdbcType=VARCHAR},
            rack_group_no       = #{rackGroupNo,jdbcType=INTEGER},
            rack_column_no      = #{rackColumnNo,jdbcType=INTEGER},
            rack_panel_No       = #{rackPanelNo,jdbcType=INTEGER},
            rack_section_no     = #{rackSectionNo,jdbcType=INTEGER},
            rack_layer_no       = #{rackLayerNo,jdbcType=INTEGER},
            rack_grid_no        = #{rackGridNo,jdbcType=INTEGER},
            hr_cabinet_group_no = #{hrCabinetGroupNo,jdbcType=INTEGER},
            hr_cabinet_no       = #{hrCabinetNo,jdbcType=INTEGER},
            hr_cabinet_layer_no = #{hrCabinetLayerNo,jdbcType=INTEGER},
            hr_cabinet_grid_no  = #{hrCabinetGridNo,jdbcType=INTEGER},
            cabinet_group_no    = #{cabinetGroupNo,jdbcType=INTEGER},
            cabinet_no          = #{cabinetNo,jdbcType=INTEGER},
            cabinet_grid_no     = #{cabinetGridNo,jdbcType=INTEGER},
            updated_id          = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateTidById">
        update archive
        set tid = #{tid,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateTidByIdList">
        update archive
        set tid = #{tid,jdbcType=VARCHAR}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateStockStatusByIdList">
        update archive
        set warehouse_id = #{warehouseId,jdbcType=BIGINT},
            stock_status = #{stockStatus,jdbcType=VARCHAR},
            updated_id   = #{updatedId,jdbcType=BIGINT}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateStatusByIdList">
        update archive
        <set>
            <if test="stockStatus != null">
                stock_status = #{stockStatus,jdbcType=VARCHAR},
            </if>
            <if test="shelvingStatus != null">
                shelving_status = #{shelvingStatus,jdbcType=VARCHAR},
            </if>
            <if test="updatedId != null">
                updated_id = #{updatedId,jdbcType=BIGINT},
            </if>
        </set>
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateListForLocation" parameterType="java.util.List">
        update archive
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="shelving_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.shelvingStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rack_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_column_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackColumnNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_panel_No = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackPanelNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_section_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackSectionNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_layer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackLayerNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_layer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetLayerNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="updated_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updatedId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateListForShelving" parameterType="java.util.List">
        update archive
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="warehouse_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="stock_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.stockStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shelving_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.shelvingStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rack_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_column_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackColumnNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_panel_No = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackPanelNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_section_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackSectionNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_layer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackLayerNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_layer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetLayerNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="shelving_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.shelvingId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="shelving_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.shelvingAt,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updated_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updatedId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateListForBoxing">
        update archive
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="warehouse_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="rack_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_column_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackColumnNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_panel_No = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackPanelNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_section_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackSectionNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_layer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackLayerNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rack_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rackGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_layer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetLayerNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_cabinet_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrCabinetGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_group_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetGroupNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cabinet_grid_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cabinetGridNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="updated_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updatedId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateParentIdByIdList">
        update archive
        set parent_id  = #{parentId,jdbcType=BIGINT},
            updated_id = #{updatedId,jdbcType=BIGINT}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateDeleteAtByIdList">
        update archive
        set delete_at = #{deleteAt,jdbcType=TIMESTAMP}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deletePhysicallyByIdList">
        delete
        from archive
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at != 0
    </delete>
</mapper>