<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.SysMenuMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.SysMenu">
        <!--@mbg.generated-->
        <!--@Table sys_menu-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="menu_code" jdbcType="VARCHAR" property="menuCode"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="menu_icon" jdbcType="VARCHAR" property="menuIcon"/>
        <result column="menu_status" jdbcType="INTEGER" property="menuStatus"/>
        <result column="menu_path" jdbcType="VARCHAR" property="menuPath"/>
        <result column="menu_path_pattern" jdbcType="VARCHAR" property="menuPathPattern"/>
        <result column="ordinal" jdbcType="INTEGER" property="ordinal"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        menu_name,
        menu_code,
        parent_id,
        menu_icon,
        menu_status,
        menu_path,
        menu_path_pattern,
        ordinal,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        where delete_at = 0
    </select>

    <select id="selectByIdList" resultType="java.lang.Long">
        select id
        from sys_menu
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="selectByParentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        where parent_id = #{parentId,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <insert id="insert">
        insert into sys_menu (menu_name, menu_code, parent_id, menu_icon, menu_status, menu_path, menu_path_pattern,
                              ordinal, created_id, updated_id)
        values (#{menuName,jdbcType=VARCHAR}, #{menuCode,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},
                #{menuIcon,jdbcType=VARCHAR}, #{menuStatus,jdbcType=INTEGER}, #{menuPath,jdbcType=VARCHAR},
                #{menuPathPattern,jdbcType=VARCHAR}, #{ordinal,jdbcType=INTEGER}, #{createdId,jdbcType=BIGINT},
                #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="update">
        update sys_menu
        set menu_name   = #{menuName,jdbcType=VARCHAR},
            menu_code   = #{menuCode,jdbcType=VARCHAR},
            parent_id   = #{parentId,jdbcType=BIGINT},
            menu_icon   = #{menuIcon,jdbcType=VARCHAR},
            menu_status = #{menuStatus,jdbcType=INTEGER},
            menu_path   = #{menuPath,jdbcType=VARCHAR},
            menu_path_pattern = #{menuPathPattern,jdbcType=VARCHAR},
            ordinal     = #{ordinal,jdbcType=INTEGER},
            updated_id  = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update sys_menu
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>