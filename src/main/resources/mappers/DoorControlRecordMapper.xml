<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.DoorControlRecordMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.DoorControlRecord">
        <!--@mbg.generated-->
        <!--@Table door_control_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="control_method" jdbcType="VARCHAR" property="controlMethod"/>
        <result column="device_user_id" jdbcType="BIGINT" property="deviceUserId"/>
        <result column="device_user_name" jdbcType="VARCHAR" property="deviceUserName"/>
        <result column="user_id_card" jdbcType="VARCHAR" property="userIdCard"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        device_no,
        warehouse_id,
        device_name,
        control_method,
        device_user_id,
        device_user_name,
        user_id_card,
        created_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from door_control_record
        <where>
            <if test="warehouseId != null">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="deviceNoList != null and deviceNoList.size() > 0">
                and device_no in
                <foreach collection="deviceNoList" item="deviceNo" open="(" separator="," close=")">
                    #{deviceNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="createdAtBegin != null">
                and created_at &gt;= #{createdAtBegin}
            </if>
            <if test="createdAtEnd != null">
                and created_at &lt;= #{createdAtEnd}
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select count(*)
        from door_control_record
        <where>
            <if test="warehouseId != null">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="createdAtBegin != null">
                and created_at &gt;= #{createdAtBegin}
            </if>
            <if test="createdAtEnd != null">
                and created_at &lt;= #{createdAtEnd}
            </if>
        </where>
    </select>

    <select id="selectCountByDeviceNo" resultType="long">
        select count(1)
        from door_control_record
        where device_no = #{deviceNo, jdbcType=VARCHAR}
        <if test="createdAtBegin != null">
            and created_at &gt;= #{createdAtBegin}
        </if>
        <if test="createdAtEnd != null">
            and created_at &lt;= #{createdAtEnd}
        </if>
    </select>

    <select id="selectBatchLastTimeByUsers" resultType="org.zxwl.smart.mybatis.ext.env.DoorControlPassTimeExt">
        select user_id_card    as idCard,
               max(created_at) as lastPassTime
        from door_control_record
        where device_no = #{deviceNo, jdbcType=VARCHAR}
          and user_id_card in
        <foreach collection="idCardList" item="idCard" open="(" separator="," close=")">
            #{idCard, jdbcType=VARCHAR}
        </foreach>
        group by user_id_card

        union all

        select u.id_card         as idCard,
               max(r.created_at) as lastPassTime
        from door_control_record r
                 join
             door_control_user u on r.device_user_name = u.user_name and r.device_no = u.device_no
        where r.device_no = #{deviceNo, jdbcType=VARCHAR}
          and u.id_card in
        <foreach collection="idCardList" item="idCard" open="(" separator="," close=")">
            #{idCard, jdbcType=VARCHAR}
        </foreach>
        and u.delete_at = 0
        and r.user_id_card is null
        group by u.id_card
    </select>

    <insert id="insert">
        insert into door_control_record (device_no, warehouse_id, device_name,
                                         control_method, device_user_id, device_user_name, user_id_card)
        values (#{deviceNo,jdbcType=VARCHAR}, #{warehouseId,jdbcType=BIGINT},
                #{deviceName,jdbcType=VARCHAR}, #{controlMethod,jdbcType=VARCHAR}, #{deviceUserId,jdbcType=BIGINT},
                #{deviceUserName,jdbcType=VARCHAR}, #{userIdCard,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteByIdList">
        delete
        from door_control_record
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>