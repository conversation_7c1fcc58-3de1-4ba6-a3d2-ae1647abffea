<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGroupMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup">
        <!--@mbg.generated-->
        <!--@Table hr_cabinet_group-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="group_no" jdbcType="INTEGER" property="groupNo"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="cabinet_num" jdbcType="INTEGER" property="cabinetNum"/>
        <result column="layer_num" jdbcType="INTEGER" property="layerNum"/>
        <result column="layer_grid_num" jdbcType="INTEGER" property="layerGridNum"/>
        <result column="control_ip" jdbcType="VARCHAR" property="controlIp"/>
        <result column="control_port" jdbcType="INTEGER" property="controlPort"/>
        <result column="capacity" jdbcType="INTEGER" property="capacity"/>
        <result column="used_capacity" jdbcType="INTEGER" property="usedCapacity"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        warehouse_id,
        group_no,
        group_name,
        cabinet_num,
        layer_num,
        layer_grid_num,
        control_ip,
        control_port,
        capacity,
        used_capacity,
        remark,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_cabinet_group
        where delete_at = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
        order by id desc
    </select>

    <select id="selectListForStatTask" resultMap="BaseResultMap">
        select id, group_no
        from hr_cabinet_group
        where delete_at = 0
        <if test="groupNo != null">
            and group_no = #{groupNo,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectListByGroupNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_cabinet_group
        where group_no in
        <foreach close=")" collection="groupNoList" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_cabinet_group
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="selectByGroupNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_cabinet_group
        where group_no = #{cabinetGroupNo,jdbcType=INTEGER}
          and delete_at = 0
    </select>

    <select id="existByGroupNo" resultType="int">
        select count(*)
        from hr_cabinet_group
        where group_no = #{groupNo,jdbcType=INTEGER}
          and delete_at = 0
        limit 1
    </select>

    <select id="maxGroupNo" resultType="int">
        select ifnull(max(group_no), 0)
        from hr_cabinet_group
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup" useGeneratedKeys="true">
        insert into hr_cabinet_group (organization_id, warehouse_id, group_no,
                                      group_name, cabinet_num, layer_num,
                                      layer_grid_num, control_ip, control_port, capacity, used_capacity,
                                      remark, visual_config, created_id, updated_id)
        values (#{organizationId,jdbcType=BIGINT}, #{warehouseId,jdbcType=BIGINT}, #{groupNo,jdbcType=INTEGER},
                #{groupName,jdbcType=VARCHAR}, #{cabinetNum,jdbcType=INTEGER}, #{layerNum,jdbcType=INTEGER},
                #{layerGridNum,jdbcType=INTEGER}, #{controlIp,jdbcType=VARCHAR}, #{controlPort,jdbcType=INTEGER},
                #{capacity,jdbcType=INTEGER}, #{usedCapacity,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
                #{visualConfig,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup">
        update hr_cabinet_group
        set group_name    = #{groupName,jdbcType=VARCHAR},
            control_ip    = #{controlIp,jdbcType=VARCHAR},
            control_port  = #{controlPort,jdbcType=INTEGER},
            remark        = #{remark,jdbcType=VARCHAR},
            updated_id    = #{updatedId,jdbcType=BIGINT},
            visual_config = #{visualConfig,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateUsedCapacityForStatTask" parameterType="java.util.List">
        update hr_cabinet_group
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                        then #{item.usedCapacity,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteById">
        update hr_cabinet_group
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>