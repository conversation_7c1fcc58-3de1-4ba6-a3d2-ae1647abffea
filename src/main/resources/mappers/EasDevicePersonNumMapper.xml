<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EasDevicePersonNumMapper">
  <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EasDevicePersonNum">
    <!--@mbg.generated-->
    <!--@Table eas_device_person_num-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="device_ip" jdbcType="VARCHAR" property="deviceIp" />
    <result column="total_in_num" jdbcType="INTEGER" property="totalInNum" />
    <result column="total_out_num" jdbcType="INTEGER" property="totalOutNum" />
    <result column="date_at" jdbcType="DATE" property="dateAt" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_ip, total_in_num, total_out_num, date_at, created_at, updated_at
  </sql>
</mapper>