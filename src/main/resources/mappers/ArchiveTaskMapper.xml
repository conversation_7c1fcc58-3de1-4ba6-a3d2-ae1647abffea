<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveTaskMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveTask">
        <!--@mbg.generated-->
        <!--@Table archive_task-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_approval_id" jdbcType="BIGINT" property="archiveApprovalId"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="execution_type" jdbcType="VARCHAR" property="executionType"/>
        <result column="task_source" jdbcType="VARCHAR" property="taskSource"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        archive_approval_id,
        operator_id,
        archive_id,
        task_type,
        `status`,
        execution_type,
        task_source,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>
    <sql id="Base_Column_List_With_ArchiveTask">
        <!--@mbg.generated-->
        archive_task.id,
        archive_task.archive_approval_id,
        archive_task.operator_id,
        archive_task.archive_id,
        archive_task.task_type,
        archive_task.`status`,
        archive_task.execution_type,
        archive_task.task_source,
        archive_task.created_id,
        archive_task.updated_id,
        archive_task.created_at,
        archive_task.updated_at,
        archive_task.delete_at
    </sql>

    <select id="selectList" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveTaskExt">
        select
        <include refid="Base_Column_List_With_ArchiveTask"/>
        from archive_task
                 left join archive on archive.id = archive_task.archive_id
        where archive_task.delete_at = 0
        <if test="archiveName != null and archiveName != ''">
            and archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive.archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive.archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive.archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        <if test="beginCreateAt != null">
            and archive_task.created_at &gt;= #{beginCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateAt != null">
            and archive_task.created_at &lt;= #{endCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="statusList != null and statusList.size() != 0">
            and `status` in
            <foreach close=")" collection="statusList" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="taskType != null and taskType != ''">
            and task_type = #{taskType,jdbcType=VARCHAR}
        </if>
        order by archive_task.id desc
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_task
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_task
        (archive_approval_id, operator_id, archive_id, task_type, `status`, execution_type,
         task_source, created_id, updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.archiveApprovalId,jdbcType=BIGINT}, #{item.operatorId,jdbcType=BIGINT},
             #{item.archiveId,jdbcType=BIGINT}, #{item.taskType,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR},
             #{item.executionType,jdbcType=VARCHAR}, #{item.taskSource,jdbcType=VARCHAR},
             #{item.createdId,jdbcType=BIGINT},
             #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateStatusByArchiveIdListIfExists">
        update archive_task
        set `status` = #{status,jdbcType=VARCHAR}
        where archive_id in
        <foreach close=")" collection="archiveIdList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateCompleteInfoByIdList">
        update archive_task
        set `status`        = #{status,jdbcType=VARCHAR},
            completion_time = #{completionTime,jdbcType=TIMESTAMP},
            updated_id      = #{updatedId,jdbcType=BIGINT}
        where id in
        <foreach close=")" collection="taskIdList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>