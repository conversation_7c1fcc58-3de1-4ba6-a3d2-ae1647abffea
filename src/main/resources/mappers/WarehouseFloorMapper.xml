<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.warehouse.WarehouseFloorMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.warehouse.WarehouseFloor">
        <!--@mbg.generated-->
        <!--@Table warehouse_floor-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_building_id" jdbcType="BIGINT" property="warehouseBuildingId"/>
        <result column="floor_no" jdbcType="INTEGER" property="floorNo"/>
        <result column="floor_name" jdbcType="VARCHAR" property="floorName"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_building_id,
        floor_no,
        floor_name,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_floor where delete_at = 0
        <if test="warehouseBuildingId != null">
            and warehouse_building_id = #{warehouseBuildingId,jdbcType=BIGINT}
        </if>
        order by id desc
    </select>

    <select id="selectNoAndNameList" resultMap="BaseResultMap">
        select id, warehouse_building_id, floor_no, floor_name
        from warehouse_floor where id in
        <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_floor
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="existByBuildingId" resultType="int">
        select count(*)
        from warehouse_floor
        where delete_at = 0
          and warehouse_building_id = #{warehouseBuildingId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="existByBuildingIdAndFloorNo" resultType="int">
        select count(*)
        from warehouse_floor
        where delete_at = 0
          and warehouse_building_id = #{warehouseBuildingId,jdbcType=BIGINT}
          and floor_no = #{floorNo,jdbcType=INTEGER}
        limit 1
    </select>

    <select id="existById" resultType="int">
        select count(*)
        from warehouse_floor
        where delete_at = 0
          and id = #{id,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="count" resultType="int">
        select count(*)
        from warehouse_floor
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.warehouse.WarehouseFloor" useGeneratedKeys="true">
        insert into warehouse_floor (warehouse_building_id, floor_no, floor_name,
                                     visual_config, created_id, updated_id)
        values (#{warehouseBuildingId,jdbcType=BIGINT}, #{floorNo,jdbcType=INTEGER}, #{floorName,jdbcType=VARCHAR},
                #{visualConfig,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.warehouse.WarehouseFloor">
        update warehouse_floor
        set floor_no      = #{floorNo,jdbcType=INTEGER},
            floor_name    = #{floorName,jdbcType=VARCHAR},
            visual_config = #{visualConfig,jdbcType=VARCHAR},
            updated_id    = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update warehouse_floor
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </update>
</mapper>