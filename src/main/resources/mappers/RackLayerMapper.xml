<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.rack.RackLayerMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.rack.RackLayer">
        <!--@mbg.generated-->
        <!--@Table rack_layer-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="rack_group_id" jdbcType="BIGINT" property="rackGroupId"/>
        <result column="archive_type_id" jdbcType="BIGINT" property="archiveTypeId"/>
        <result column="column_no" jdbcType="INTEGER" property="columnNo"/>
        <result column="panel_no" jdbcType="INTEGER" property="panelNo"/>
        <result column="section_no" jdbcType="INTEGER" property="sectionNo"/>
        <result column="layer_no" jdbcType="INTEGER" property="layerNo"/>
        <result column="layer_tid" jdbcType="VARCHAR" property="layerTid"/>
        <result column="capacity" jdbcType="INTEGER" property="capacity"/>
        <result column="used_capacity" jdbcType="INTEGER" property="usedCapacity"/>
        <result column="on_count" jdbcType="INTEGER" property="onCount"/>
        <result column="wrong_count" jdbcType="INTEGER" property="wrongCount"/>
        <result column="inventory_at" jdbcType="TIMESTAMP" property="inventoryAt"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        rack_group_id,
        archive_type_id,
        column_no,
        panel_no,
        section_no,
        layer_no,
        layer_tid,
        capacity,
        used_capacity,
        on_count,
        wrong_count,
        inventory_at,
        created_id,
        created_at,
        updated_at
    </sql>

    <select id="selectList" resultType="org.zxwl.smart.mybatis.ext.rack.RackLayerExt">
        select rack_layer.*,
               rack_group.warehouse_id,
               rack_group.group_no
        from rack_layer
                 inner join rack_group on rack_layer.rack_group_id = rack_group.id
        where rack_group.id = #{rackGroupId,jdbcType=BIGINT}
        <if test="columnNo != null">
            and column_no = #{columnNo,jdbcType=INTEGER}
        </if>
        <if test="panelNo != null">
            and panel_no = #{panelNo,jdbcType=INTEGER}
        </if>
        <if test="sectionNo != null">
            and section_no = #{sectionNo,jdbcType=INTEGER}
        </if>
        order by id desc
    </select>

    <select id="selectListByGroupIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rack_layer
        where rack_group_id in
        <foreach collection="rackGroupIdList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectListByRackGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rack_layer
        where rack_group_id = #{rackGroupId,jdbcType=BIGINT}
        <if test="columnNo != null">
            and column_no = #{columnNo,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByIdOrLayerTid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rack_layer
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="layerTid != null">
                and layer_tid = #{layerTid,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectLocationList" resultType="org.zxwl.smart.mybatis.ext.rack.RackGridLocationExt">
        select rack_group.organization_id,
               rack_group.rack_type,
               rack_group.group_no,
               rack_layer.column_no,
               rack_layer.panel_no,
               rack_layer.section_no,
               rack_layer.layer_no,
               rack_layer.capacity,
               rack_layer.used_capacity,
               rack_layer.id as layer_id,
               rack_grid.grid_no
        from rack_group
                 inner join rack_layer on rack_group.id = rack_layer.rack_group_id
                 left join rack_grid on rack_layer.id = rack_grid.rack_layer_id
        where rack_group.delete_at = 0
          and
        <foreach collection="wrapperList" item="item" open="(" separator="or" close=")">
            (rack_group.group_no = #{item.groupNo,jdbcType=INTEGER}
                and rack_layer.column_no = #{item.columnNo,jdbcType=INTEGER}
                and rack_layer.panel_no = #{item.panelNo,jdbcType=INTEGER}
                and rack_layer.section_no = #{item.sectionNo,jdbcType=INTEGER}
                and rack_layer.layer_no = #{item.layerNo,jdbcType=INTEGER}
            <choose>
                <when test="item.gridNo == null">
                    and rack_grid.grid_no is null
                </when>
                <otherwise>
                    and rack_grid.grid_no = #{item.gridNo,jdbcType=INTEGER}
                </otherwise>
            </choose>
            )
        </foreach>
    </select>

    <select id="selectLocation" resultType="org.zxwl.smart.mybatis.ext.rack.RackGridLocationExt">
        select rack_group.organization_id,
               rack_group.rack_type,
               rack_group.group_no,
               rack_layer.column_no,
               rack_layer.panel_no,
               rack_layer.section_no,
               rack_layer.layer_no,
               rack_layer.capacity,
               rack_layer.used_capacity,
               rack_layer.id as layer_id,
               rack_grid.grid_no
        from rack_group
                 inner join rack_layer on rack_group.id = rack_layer.rack_group_id
                 left join rack_grid on rack_layer.id = rack_grid.rack_layer_id
        where rack_group.delete_at = 0
          and rack_group.group_no = #{groupNo,jdbcType=INTEGER}
          and rack_layer.column_no = #{columnNo,jdbcType=INTEGER}
          and rack_layer.panel_no = #{panelNo,jdbcType=INTEGER}
          and rack_layer.section_no = #{sectionNo,jdbcType=INTEGER}
          and rack_layer.layer_no = #{layerNo,jdbcType=INTEGER}
        <choose>
            <when test="gridNo == null">
                and rack_grid.grid_no is null
            </when>
            <otherwise>
                and rack_grid.grid_no = #{gridNo,jdbcType=INTEGER}
            </otherwise>
        </choose>
    </select>

    <select id="selectListByRackGroupIdForStatTask" resultMap="BaseResultMap">
        select id,
               rack_group_id,
               column_no,
               panel_no,
               section_no,
               layer_no
        from rack_layer
        where rack_group_id = #{rackGroupId,jdbcType=BIGINT}
    </select>

    <select id="existByTid" resultType="int">
        select count(*)
        from rack_layer
        where layer_tid = #{tid,jdbcType=VARCHAR}
        limit 1
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into rack_layer
        (rack_group_id, archive_type_id, column_no, panel_no, section_no, layer_no, layer_tid,
         capacity, used_capacity, created_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.rackGroupId,jdbcType=BIGINT}, #{item.archiveTypeId,jdbcType=BIGINT},
             #{item.columnNo,jdbcType=INTEGER},
             #{item.panelNo,jdbcType=INTEGER}, #{item.sectionNo,jdbcType=INTEGER}, #{item.layerNo,jdbcType=INTEGER},
             #{item.layerTid,jdbcType=VARCHAR}, #{item.capacity,jdbcType=INTEGER},
             #{item.usedCapacity,jdbcType=INTEGER}, #{item.createdId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateByIdSelective" parameterType="org.zxwl.smart.mybatis.entity.rack.RackLayer">
        update rack_layer
        <set>
            <if test="archiveTypeId != null">
                archive_type_id = #{archiveTypeId,jdbcType=BIGINT},
            </if>
            <if test="layerTid != null">
                layer_tid = #{layerTid,jdbcType=VARCHAR},
            </if>
            <if test="inventoryAt != null">
                inventory_at = #{inventoryAt,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBatchArchiveTypeId">
        update rack_layer
        set archive_type_id = #{archivesTypeId,jdbcType=BIGINT}
        where id in
        <foreach close=")" collection="idList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateUsedCapacityForStatTask" parameterType="java.util.List">
        update rack_layer
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                        then #{item.usedCapacity,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteByRackGroupId">
        delete
        from rack_layer
        where rack_group_id = #{rackGroupId,jdbcType=BIGINT}
    </delete>
</mapper>