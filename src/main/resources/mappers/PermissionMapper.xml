<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.PermissionMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.Permission">
        <!--@mbg.generated-->
        <!--@Table `sys_permission`-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="permission_name" jdbcType="VARCHAR" property="permissionName"/>
        <result column="permission_code" jdbcType="VARCHAR" property="permissionCode"/>
        <result column="ordinal" jdbcType="INTEGER" property="ordinal"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <result column="parent_ordinal" jdbcType="INTEGER" property="parentOrdinal"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        permission_name,
        permission_code,
        ordinal,
        parent_name,
        parent_ordinal,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectByPermissionCode" resultType="int">
        select count(*)
        from sys_permission
        where permission_code = #{permissionCode,jdbcType=VARCHAR}
          and delete_at = 0
        limit 1
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_permission
        where delete_at = 0
    </select>

    <select id="selectByIdList" resultType="java.lang.Long">
        select id
        from sys_permission
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>


    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_permission
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectPermissionList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_permission
        where delete_at = 0
        <if test="userId != null">
            and id in
                (select permission_id
                 from sys_role_permission
                          inner join sys_user_role on sys_role_permission.role_id = sys_user_role.role_id
                 where sys_user_role.user_id = #{userId,jdbcType=BIGINT}
                   and sys_user_role.organization_id = #{organizationId,jdbcType=BIGINT})
        </if>
    </select>

    <select id="selectPermissionCodeList" resultType="java.lang.String">
        select distinct permission_code
        from (select sys_permission.permission_code
              from sys_permission
                       inner join sys_role_permission on sys_permission.id = sys_role_permission.permission_id
                       inner join sys_user_role on sys_role_permission.role_id = sys_user_role.role_id
              where sys_user_role.user_id = #{userId,jdbcType=BIGINT}
                and sys_user_role.organization_id = #{organizationId,jdbcType=BIGINT}
                and sys_permission.delete_at = 0

              union all

              select sys_permission_api.permission_code
              from sys_permission_api
                       inner join sys_permission on sys_permission_api.permission_code = sys_permission.permission_code
                       inner join sys_role_permission on sys_permission.id = sys_role_permission.permission_id
                       inner join sys_user_role on sys_role_permission.role_id = sys_user_role.role_id
              where sys_user_role.user_id = #{userId,jdbcType=BIGINT}
                and sys_user_role.organization_id = #{organizationId,jdbcType=BIGINT}
                and sys_permission.delete_at = 0
                and sys_permission_api.delete_at = 0) as all_permissions
    </select>

    <insert id="insert">
        insert into sys_permission (permission_name, permission_code, ordinal, parent_name, parent_ordinal,
                                    created_id, updated_id)
        values (#{permissionName,jdbcType=VARCHAR}, #{permissionCode,jdbcType=VARCHAR},
                #{ordinal,jdbcType=INTEGER}, #{parentName,jdbcType=VARCHAR},
                #{parentOrdinal,jdbcType=INTEGER}, #{createdId,jdbcType=BIGINT},
                #{updatedId,jdbcType=BIGINT})
    </insert>
</mapper>