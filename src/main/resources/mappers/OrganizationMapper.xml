<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.user.OrganizationMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.user.Organization">
        <!--@mbg.generated-->
        <!--@Table organization-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="organization_no" jdbcType="VARCHAR" property="organizationNo"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        parent_id,
        organization_no,
        organization_name,
        `level`,
        address,
        remark,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>
    <sql id="Base_With_Table_Name_Column_List">
        organization.id,
        organization.parent_id,
        organization.organization_no,
        organization.organization_name,
        organization.`level`,
        organization.address,
        organization.remark,
        organization.created_id,
        organization.updated_id,
        organization.created_at,
        organization.updated_at,
        organization.delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from organization
        where delete_at = 0
        order by id desc
    </select>

    <select id="selectFirst" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from organization
        where delete_at = 0
        limit 1
    </select>

    <select id="selectListByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_With_Table_Name_Column_List"/>
        from organization
                 left join organization_user on organization_user.organization_id = organization.id
        where delete_at = 0
        <if test="userId != null">
            and organization_user.user_id = #{userId,jdbcType=BIGINT}
        </if>
        group by organization.id
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from organization
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from organization
        where id = #{id}
          and delete_at = 0
    </select>

    <select id="existByOrganizationNo" resultType="java.lang.Integer">
        select count(*)
        from organization
        where delete_at = 0
          and organization_no = #{organizationNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="existByOrganizationName" resultType="java.lang.Integer">
        select count(*)
        from organization
        where delete_at = 0
          and organization_name = #{organizationName,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="exitByParentId" resultType="java.lang.Integer">
        select count(*)
        from organization
        where delete_at = 0
          and parent_id = #{parentId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="count" resultType="int">
        select count(*)
        from organization
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="org.zxwl.smart.mybatis.entity.user.Organization"
            useGeneratedKeys="true">
        insert into organization (parent_id, organization_no, organization_name,
                                  `level`, address,
                                  remark, created_id, updated_id)
        values (#{parentId,jdbcType=BIGINT}, #{organizationNo,jdbcType=VARCHAR}, #{organizationName,jdbcType=VARCHAR},
                #{level,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.user.Organization">
        update organization
        set organization_no   = #{organizationNo,jdbcType=VARCHAR},
            organization_name = #{organizationName,jdbcType=VARCHAR},
            address           = #{address,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            updated_id        = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update organization
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>