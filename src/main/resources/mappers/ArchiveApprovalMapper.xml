<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveApprovalMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveApproval">
        <!--@mbg.generated-->
        <!--@Table archive_approval-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_applicant_id" jdbcType="BIGINT" property="archiveApplicantId"/>
        <result column="approval_status" jdbcType="VARCHAR" property="approvalStatus"/>
        <result column="approver_id" jdbcType="BIGINT" property="approverId"/>
        <result column="approval_at" jdbcType="TIMESTAMP" property="approvalAt"/>
        <result column="approval_comment" jdbcType="VARCHAR" property="approvalComment"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        archive_applicant_id,
        approval_status,
        approver_id,
        approval_at,
        approval_comment,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveApprovalExt">
        select archive_approval.*,
               archive_applicant.archive_id,
               archive_applicant.applicant_name,
               archive_applicant.operator_id,
               user.user_name               as operator_name,
               archive_applicant.created_at as applicant_created_at,
               archive_applicant.remark,
               archive.archive_type_id,
               archive.type,
               archive.archive_name,
               archive.archive_no,
               archive.rack_group_no,
               archive.rack_column_no,
               archive.rack_panel_no,
               archive.rack_section_no,
               archive.rack_layer_no,
               archive.rack_grid_no,
               archive.cabinet_group_no,
               archive.cabinet_no,
               archive.cabinet_grid_no,
               archive.archive_type_id
        from archive_approval
                 inner join archive_applicant on archive_applicant.id = archive_approval.archive_applicant_id
                 inner join archive on archive.id = archive_applicant.archive_id
                 left join user on user.id = archive_applicant.operator_id
        where archive_approval.delete_at = 0
        <if test="archiveName != null and archiveName != ''">
            and archive.archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive.archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive.archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive.archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        <if test="applicantName != null and applicantName != ''">
            and archive_applicant.applicant_name = #{applicantName,jdbcType=VARCHAR}
        </if>
        <if test="applicantNameLike != null and applicantNameLike != ''">
            <bind name="_applicantNameLike" value="'%' + applicantNameLike + '%'"/>
            and archive_applicant.applicant_name like #{_applicantNameLike,jdbcType=VARCHAR}
        </if>
        <if test="operatorName != null and operatorName != ''">
            and user.user_name = #{operatorName,jdbcType=VARCHAR}
        </if>
        <if test="operatorNameLike != null and operatorNameLike != ''">
            <bind name="_operatorNameLike" value="'%' + operatorNameLike + '%'"/>
            and user.user_name like #{_operatorNameLike,jdbcType=VARCHAR}
        </if>
        <if test="applicantBeginCreateAt != null">
            and archive_approval.created_at &gt;= #{applicantBeginCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="applicantEndCreateAt != null">
            and archive_approval.created_at &lt;= #{applicantEndCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="approverId != null">
            and archive_approval.approver_id = #{approverId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectByArchiveApplicantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_approval
        where delete_at = 0
          and archive_applicant_id = #{archiveApplicantId}
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_approval
        where delete_at = 0
          and id = #{id}
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_approval
        (archive_applicant_id, approval_status, approver_id, approval_at, approval_comment)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.archiveApplicantId,jdbcType=BIGINT}, #{item.approvalStatus,jdbcType=VARCHAR},
             #{item.approverId,jdbcType=BIGINT}, #{item.approvalAt,jdbcType=TIMESTAMP},
             #{item.approvalComment,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>