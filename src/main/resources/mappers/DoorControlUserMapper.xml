<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.DoorControlUserMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.DoorControlUser">
        <!--@mbg.generated-->
        <!--@Table door_control_user-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="user_status" jdbcType="VARCHAR" property="userStatus"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="pri" jdbcType="INTEGER" property="pri"/>
        <result column="user_pwd" jdbcType="VARCHAR" property="userPwd"/>
        <result column="card" jdbcType="VARCHAR" property="card"/>
        <result column="user_group" jdbcType="INTEGER" property="userGroup"/>
        <result column="tz" jdbcType="VARCHAR" property="tz"/>
        <result column="verify_type" jdbcType="INTEGER" property="verifyType"/>
        <result column="vice_card" jdbcType="VARCHAR" property="viceCard"/>
        <result column="start_date_time" jdbcType="VARCHAR" property="startDateTime"/>
        <result column="end_date_time" jdbcType="VARCHAR" property="endDateTime"/>
        <result column="finger_id" jdbcType="VARCHAR" property="fingerId"/>
        <result column="face_id" jdbcType="VARCHAR" property="faceId"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        device_no,
        user_status,
        user_type,
        img_url,
        id_card,
        user_name,
        pri,
        user_pwd,
        card,
        user_group,
        tz,
        verify_type,
        vice_card,
        start_date_time,
        end_date_time,
        finger_id,
        face_id,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from door_control_user
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="selectIdBySnAndIdCard" resultType="java.lang.Long">
        select id
        from door_control_user
        where device_no = #{deviceNo,jdbcType=VARCHAR}
          and id_card = #{idCard,jdbcType=VARCHAR}
    </select>

    <select id="selectByDeviceNoAndIdCard" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from door_control_user
        where device_no = #{deviceNo,jdbcType=VARCHAR}
          and id_card = #{idCard,jdbcType=VARCHAR}
          and delete_at = 0
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from door_control_user
        where delete_at = 0
        <if test="deviceNo != null">
            and device_no = #{deviceNo,jdbcType=VARCHAR}
        </if>
        <if test="userName != null">
            and user_name = #{userName,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from door_control_user
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="existByIdCard" resultType="int">
        select count(*)
        from door_control_user
        where id_card = #{idCard,jdbcType=VARCHAR}
          and delete_at = 0
        limit 1
    </select>

    <insert id="insert" parameterType="org.zxwl.smart.mybatis.entity.device.DoorControlUser" useGeneratedKeys="true"
            keyProperty="id">
        insert into door_control_user (device_no, user_status, user_type, img_url, id_card, user_name, pri, user_pwd,
                                       card,
                                       user_group, tz, verify_type, vice_card, start_date_time,
                                       end_date_time, created_id, updated_id)
        values (#{deviceNo,jdbcType=VARCHAR}, #{userStatus,jdbcType=VARCHAR}, #{userType,jdbcType=INTEGER},
                #{imgUrl,jdbcType=VARCHAR},
                #{idCard,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{pri,jdbcType=INTEGER},
                #{userPwd,jdbcType=VARCHAR}, #{card,jdbcType=VARCHAR}, #{userGroup,jdbcType=INTEGER},
                #{tz,jdbcType=VARCHAR}, #{verifyType,jdbcType=INTEGER}, #{viceCard,jdbcType=VARCHAR},
                #{startDateTime,jdbcType=VARCHAR}, #{endDateTime,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateUser">
        update door_control_user
        <set>
            <if test="idCard != null">
                id_card = #{idCard,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                user_status = #{userStatus,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userPwd != null">
                user_pwd = #{userPwd,jdbcType=VARCHAR},
            </if>
            <if test="card != null">
                card = #{card,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateUserInfo">
        update door_control_user
        set user_name       = #{userName,jdbcType=VARCHAR},
            pri             = #{pri,jdbcType=INTEGER},
            user_pwd        = #{userPwd,jdbcType=VARCHAR},
            card            = #{card,jdbcType=VARCHAR},
            user_group      = #{userGroup,jdbcType=INTEGER},
            tz              = #{tz,jdbcType=VARCHAR},
            verify_type     = #{verifyType,jdbcType=INTEGER},
            vice_card       = #{viceCard,jdbcType=VARCHAR},
            start_date_time = #{startDateTime,jdbcType=VARCHAR},
            end_date_time   = #{endDateTime,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateUserFingerIdById" parameterType="org.zxwl.smart.mybatis.entity.device.DoorControlUser">
        update door_control_user
        set finger_id = #{fingerId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateUserFaceIdById" parameterType="org.zxwl.smart.mybatis.entity.device.DoorControlUser">
        update door_control_user
        set face_id = #{faceId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById" parameterType="org.zxwl.smart.mybatis.entity.device.DoorControlUser">
        update door_control_user
        set user_status= #{userStatus,jdbcType=VARCHAR},
            delete_at  = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="deleteByIdList">
        update door_control_user
        set user_status = #{userStatus,jdbcType=VARCHAR},
            delete_at   = #{deleteAt,jdbcType=BIGINT}
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </update>
</mapper>