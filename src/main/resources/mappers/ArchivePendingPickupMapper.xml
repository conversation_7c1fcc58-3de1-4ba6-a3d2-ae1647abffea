<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchivePendingPickupMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchivePendingPickup">
        <!--@mbg.generated-->
        <!--@Table archive_pending_pickup-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, archive_id, user_id, created_id, created_at, updated_at
    </sql>

    <select id="selectList" resultType="org.zxwl.smart.mybatis.ext.archive.ArchivePendingPickupExt">
        select archive_pending_pickup.id,
               archive_pending_pickup.archive_id,
               archive_pending_pickup.user_id,
               archive_pending_pickup.created_id,
               archive_pending_pickup.created_at,
               archive_pending_pickup.updated_at,
               archive.id as archive_id,
               archive.archive_type_id,
               archive.type,
               archive.archive_name,
               archive.archive_no,
               archive.rack_group_no,
               archive.rack_column_no,
               archive.rack_panel_no,
               archive.rack_section_no,
               archive.rack_layer_no,
               archive.rack_grid_no,
               archive.cabinet_group_no,
               archive.cabinet_no,
               archive.cabinet_grid_no
        from archive_pending_pickup
                 inner join archive
                            on archive_pending_pickup.archive_id = archive.id
        where archive.created_id = #{createdId,jdbcType=BIGINT}
        <if test="archiveName != null and archiveName != ''">
            and archive.archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive.archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive.archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive.archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        order by archive_pending_pickup.id desc
    </select>

    <select id="selectListByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_pending_pickup
        where user_id = #{userId}
    </select>

    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_pending_pickup
        where id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="countByUserId" resultType="int">
        select count(*)
        from archive_pending_pickup
        where user_id = #{userId}
    </select>

    <select id="existByArchiveIdAndUserId" resultType="int">
        select count(*)
        from archive_pending_pickup
        where archive_id = #{archiveId}
          and user_id = #{userId}
        limit 1
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_pending_pickup
            (archive_id, user_id, created_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.archiveId,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT}, #{item.createdId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByArchiveIdListAndUserId">
        delete
        from archive_pending_pickup
        where archive_id in
        <foreach collection="archiveIdList" item="archiveId" open="(" close=")" separator=",">
            #{archiveId,jdbcType=BIGINT}
        </foreach>
        and user_id = #{userId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByIdList">
        delete
        from archive_pending_pickup
        where id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
</mapper>