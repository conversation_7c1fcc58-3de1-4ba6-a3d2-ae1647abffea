<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveApplicantMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveApplicant">
        <!--@mbg.generated-->
        <!--@Table archive_applicant-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="borrow_intent" jdbcType="VARCHAR" property="borrowIntent"/>
        <result column="applicant_name" jdbcType="VARCHAR" property="applicantName"/>
        <result column="applicant_phone" jdbcType="VARCHAR" property="applicantPhone"/>
        <result column="applicant_email" jdbcType="VARCHAR" property="applicantEmail"/>
        <result column="borrow_days" jdbcType="INTEGER" property="borrowDays"/>
        <result column="borrow_at" jdbcType="TIMESTAMP" property="borrowAt"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="approval_at" jdbcType="TIMESTAMP" property="approvalAt"/>
        <result column="need_back_at" jdbcType="TIMESTAMP" property="needBackAt"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        archive_id,
        `status`,
        borrow_intent,
        applicant_name,
        applicant_phone,
        applicant_email,
        borrow_days,
        borrow_at,
        operator_id,
        remark,
        approval_at,
        need_back_at,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultType="org.zxwl.smart.mybatis.ext.archive.ArchiveApplicantExt">
        select archive_applicant.*,
               archive.archive_type_id,
               archive.type,
               archive.archive_name,
               archive.archive_no,
               archive.rack_group_no,
               archive.rack_column_no,
               archive.rack_panel_no,
               archive.rack_section_no,
               archive.rack_layer_no,
               archive.rack_grid_no,
               archive.cabinet_group_no,
               archive.cabinet_no,
               archive.cabinet_grid_no
        from archive_applicant
                 inner join archive on archive_applicant.archive_id = archive.id
        where archive_applicant.delete_at = 0
        <if test="archiveName != null and archiveName != ''">
            and archive.archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive.archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive.archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive.archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        <if test="statusList != null and statusList.size() != 0">
            and archive_applicant.status in
            <foreach item="item" index="index" collection="statusList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="beginCreateAt != null">
            and archive_applicant.created_at &gt;= #{beginCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateAt != null">
            and archive_applicant.created_at &lt;= #{endCreateAt,jdbcType=TIMESTAMP}
        </if>
        order by archive_applicant.id desc
    </select>

    <select id="selectPendingApprovalList" resultType="org.zxwl.smart.mybatis.ext.archive.PendingApprovalExt">
        select archive_applicant.*,
               user.user_name as operator_name,
               archive.archive_type_id,
               archive.type,
               archive.archive_name,
               archive.archive_no,
               archive.rack_group_no,
               archive.rack_column_no,
               archive.rack_panel_no,
               archive.rack_section_no,
               archive.rack_layer_no,
               archive.rack_grid_no,
               archive.cabinet_group_no,
               archive.cabinet_no,
               archive.cabinet_grid_no
        from archive_applicant
                 inner join archive on archive_applicant.archive_id = archive.id
                 left join user on user.id = archive_applicant.operator_id
        where archive_applicant.delete_at = 0
        <if test="archiveName != null and archiveName != ''">
            and archive.archive_name = #{archiveName,jdbcType=VARCHAR}
        </if>
        <if test="archiveNameLike != null and archiveNameLike != ''">
            <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
            and archive.archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
        </if>
        <if test="archiveNo != null and archiveNo != ''">
            and archive.archive_no = #{archiveNo,jdbcType=VARCHAR}
        </if>
        <if test="archiveNoLike != null and archiveNoLike != ''">
            <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
            and archive.archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
        </if>
        <if test="applicantName != null and applicantName != ''">
            and archive_applicant.applicant_name = #{applicantName,jdbcType=VARCHAR}
        </if>
        <if test="applicantNameLike != null and applicantNameLike != ''">
            <bind name="_applicantNameLike" value="'%' + applicantNameLike + '%'"/>
            and archive_applicant.applicant_name like #{_applicantNameLike,jdbcType=VARCHAR}
        </if>
        <if test="operatorName != null and operatorName != ''">
            and user.user_name = #{operatorName,jdbcType=VARCHAR}
        </if>
        <if test="operatorNameLike != null and operatorNameLike != ''">
            <bind name="_operatorNameLike" value="'%' + operatorNameLike + '%'"/>
            and user.user_name like #{_operatorNameLike,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="beginCreateAt != null">
            and archive_applicant.created_at &gt;= #{beginCreateAt,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateAt != null">
            and archive_applicant.created_at &lt;= #{endCreateAt,jdbcType=TIMESTAMP}
        </if>
        order by archive_applicant.id desc
    </select>

    <select id="selectListByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_applicant
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_applicant
        where id = #{id}
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <select id="selectListByArchiveIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_applicant
        where archive_id in
        <foreach close=")" collection="archiveIdList" item="archiveId" open="(" separator=",">
            #{archiveId,jdbcType=BIGINT}
        </foreach>
        <if test="containDelete == false">
            and delete_at = 0
        </if>
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_applicant
        (archive_id, `status`, borrow_intent, applicant_name, applicant_phone, applicant_email,
         borrow_days, borrow_at, operator_id, remark, approval_at, need_back_at, created_id,
         updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.archiveId,jdbcType=BIGINT}, #{item.status,jdbcType=VARCHAR}, #{item.borrowIntent,jdbcType=VARCHAR},
             #{item.applicantName,jdbcType=VARCHAR}, #{item.applicantPhone,jdbcType=VARCHAR},
             #{item.applicantEmail,jdbcType=VARCHAR}, #{item.borrowDays,jdbcType=INTEGER},
             #{item.borrowAt,jdbcType=TIMESTAMP},
             #{item.operatorId,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR}, #{item.approvalAt,jdbcType=TIMESTAMP},
             #{item.needBackAt,jdbcType=TIMESTAMP}, #{item.createdId,jdbcType=BIGINT},
             #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateStatusByIdList">
        update archive_applicant
        set `status` = #{status}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </update>

    <update id="updateListForApproval" parameterType="java.util.List">
        update archive_applicant
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="need_back_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.needBackAt,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="approval_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalAt,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteByIdList">
        update archive_applicant
        set delete_at = #{deleteAt}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </update>
</mapper>