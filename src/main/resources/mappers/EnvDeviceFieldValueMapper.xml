<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EnvDeviceFieldValueMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EnvDeviceFieldValue">
        <!--@mbg.generated-->
        <!--@Table env_device_field_value-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="device_id" jdbcType="BIGINT" property="deviceId"/>
        <result column="filed_name" jdbcType="VARCHAR" property="filedName"/>
        <result column="filed_value" jdbcType="VARCHAR" property="filedValue"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, device_id, filed_name, filed_value, created_at, updated_at
    </sql>
</mapper>