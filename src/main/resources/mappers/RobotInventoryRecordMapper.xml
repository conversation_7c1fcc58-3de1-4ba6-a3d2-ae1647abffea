<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.inventory.RobotInventoryRecordMapper">
  <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.inventory.RobotInventoryRecord">
    <!--@mbg.generated-->
    <!--@Table robot_inventory_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="robot_inventory_task_id" jdbcType="BIGINT" property="robotInventoryTaskId" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="robot_id" jdbcType="BIGINT" property="robotId" />
    <result column="robot_inventory_task_name" jdbcType="VARCHAR" property="robotInventoryTaskName" />
    <result column="inventory_status" jdbcType="VARCHAR" property="inventoryStatus" />
    <result column="created_id" jdbcType="BIGINT" property="createdId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, robot_inventory_task_id, warehouse_id, robot_id, robot_inventory_task_name, inventory_status, 
    created_id, created_at, updated_at
  </sql>
</mapper>