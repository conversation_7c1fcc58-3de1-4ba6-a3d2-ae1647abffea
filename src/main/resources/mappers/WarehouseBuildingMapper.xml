<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.warehouse.WarehouseBuildingMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.warehouse.WarehouseBuilding">
        <!--@mbg.generated-->
        <!--@Table warehouse_building-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="building_no" jdbcType="INTEGER" property="buildingNo"/>
        <result column="building_name" jdbcType="VARCHAR" property="buildingName"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        building_no,
        building_name,
        `location`,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_building
        where delete_at = 0
        order by id desc
    </select>

    <select id="selectNoAndNameList" resultMap="BaseResultMap">
        select id, building_no, building_name
        from warehouse_building where id in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and delete_at = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_building
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <select id="existByIdOrBuildingNo" resultType="int">
        select count(*)
        from warehouse_building
        where delete_at = 0
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="buildingNo != null">
            and building_no = #{buildingNo,jdbcType=INTEGER}
        </if>
        limit 1
    </select>

    <select id="count" resultType="int">
        select count(*)
        from warehouse_building
        where delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.warehouse.WarehouseBuilding" useGeneratedKeys="true">
        insert into warehouse_building (building_no, building_name, `location`,
                                        visual_config, created_id, updated_id)
        values (#{buildingNo,jdbcType=INTEGER}, #{buildingName,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR},
                #{visualConfig,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.warehouse.WarehouseBuilding">
        update warehouse_building
        set building_no   = #{buildingNo,jdbcType=INTEGER},
            building_name = #{buildingName,jdbcType=VARCHAR},
            `location`    = #{location,jdbcType=VARCHAR},
            visual_config = #{visualConfig,jdbcType=VARCHAR},
            updated_id    = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update warehouse_building
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </update>
</mapper>