<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.warehouse.AreaDeviceMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.warehouse.AreaDevice">
        <!--@mbg.generated-->
        <!--@Table area_device-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="env_device_id" jdbcType="BIGINT" property="envDeviceId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, area_id, env_device_id, created_at
    </sql>

    <select id="selectListByAreaIdList" resultMap="BaseResultMap">
        select area_id, env_device_id
        from area_device
        where area_id in
        <foreach collection="areaIdList" item="areaId" open="(" close=")" separator=",">
            #{areaId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectEnvDeviceIdListByAreaId" resultType="java.lang.Long">
        select env_device_id
        from area_device
        where area_id = #{areaId,jdbcType=BIGINT}
    </select>

    <select id="selectEnvDeviceListByAreaId" resultType="org.zxwl.smart.mybatis.entity.device.EnvDevice">
        select env_device.*
        from area_device
                 inner join env_device on area_device.env_device_id = env_device.id
        where area_id = #{areaId,jdbcType=BIGINT}
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into area_device (area_id, env_device_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.areaId,jdbcType=BIGINT}, #{item.envDeviceId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByAreaId">
        delete
        from area_device
        where area_id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>