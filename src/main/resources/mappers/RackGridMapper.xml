<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.rack.RackGridMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.rack.RackGrid">
        <!--@mbg.generated-->
        <!--@Table rack_grid-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="rack_group_id" jdbcType="BIGINT" property="rackGroupId"/>
        <result column="rack_layer_id" jdbcType="BIGINT" property="rackLayerId"/>
        <result column="grid_no" jdbcType="INTEGER" property="gridNo"/>
        <result column="capacity" jdbcType="INTEGER" property="capacity"/>
        <result column="used_capacity" jdbcType="INTEGER" property="usedCapacity"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        rack_group_id,
        rack_layer_id,
        grid_no,
        capacity,
        used_capacity,
        created_id,
        created_at,
        updated_at
    </sql>

    <select id="selectListByRackLayerIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rack_grid
        where rack_layer_id in
        <foreach close=")" collection="rackLayerIdList" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectListByRackLayerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rack_grid
        where rack_layer_id = #{rackLayerId,jdbcType=BIGINT}
    </select>

    <select id="selectListByRackGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rack_grid
        where rack_group_id = #{rackGroupId,jdbcType=BIGINT}
    </select>

    <select id="selectListByRackGroupIdForStatTask" resultMap="BaseResultMap">
        select id,
               rack_group_id,
               rack_layer_id,
               grid_no
        from rack_grid
        where rack_group_id = #{rackGroupId,jdbcType=BIGINT}
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into rack_grid
        (rack_group_id, rack_layer_id, grid_no, capacity, used_capacity, created_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.rackGroupId,jdbcType=BIGINT}, #{item.rackLayerId,jdbcType=BIGINT},
             #{item.gridNo,jdbcType=INTEGER}, #{item.capacity,jdbcType=INTEGER},
             #{item.usedCapacity,jdbcType=INTEGER}, #{item.createdId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateUsedCapacityForStatTask" parameterType="java.util.List">
        update rack_grid
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                        then #{item.usedCapacity,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteByRackGroupId">
        delete
        from rack_grid
        where rack_group_id = #{rackGroupId,jdbcType=BIGINT}
    </delete>
</mapper>