<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.env.WebcamMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.env.Webcam">
        <!--@mbg.generated-->
        <!--@Table webcam-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="stream_url" jdbcType="VARCHAR" property="streamUrl"/>
        <result column="visual_config" jdbcType="VARCHAR" property="visualConfig"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_id,
        device_name,
        stream_url,
        visual_config,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from webcam
        where delete_at = 0
        <choose>
            <!-- 情况1：在库设备 -->
            <when test="warehouseId == null and includePublic == false">
                AND warehouse_id is not null
            </when>
            <!-- 情况2：指定库房设备 -->
            <when test="warehouseId != null and includePublic == false">
                AND warehouse_id = #{warehouseId}
            </when>
            <!-- 情况3：库房设备 + 公共设备 -->
            <when test="warehouseId != null and includePublic == true">
                AND (warehouse_id = #{warehouseId} OR warehouse_id IS NULL)
            </when>
            <!-- 情况4：所有设备（默认情况） -->
            <otherwise>
                <!-- 不添加warehouse条件 -->
            </otherwise>
        </choose>
        <if test="deviceNameLike != null">
            <bind name="_deviceNameLike" value="'%' + deviceNameLike + '%'"/>
            and device_name like #{_deviceNameLike,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from webcam
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="org.zxwl.smart.mybatis.entity.env.Webcam"
            useGeneratedKeys="true">
        insert into webcam (warehouse_id, device_name, stream_url,
                            visual_config, created_id, updated_id)
        values (#{warehouseId,jdbcType=BIGINT}, #{deviceName,jdbcType=VARCHAR}, #{streamUrl,jdbcType=VARCHAR},
                #{visualConfig,jdbcType=VARCHAR}, #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.env.Webcam">
        update webcam
        set device_name   = #{deviceName,jdbcType=VARCHAR},
            stream_url    = #{streamUrl,jdbcType=VARCHAR},
            visual_config = #{visualConfig,jdbcType=VARCHAR},
            updated_id    = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update webcam
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>