<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.device.EasDeviceAlarmRecordMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.device.EasDeviceAlarmRecord">
        <!--@mbg.generated-->
        <!--@Table eas_device_alarm_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="device_id" jdbcType="BIGINT" property="deviceId"/>
        <result column="device_ip" jdbcType="VARCHAR" property="deviceIp"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="archive_name" jdbcType="VARCHAR" property="archiveName"/>
        <result column="archive_no" jdbcType="VARCHAR" property="archiveNo"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, warehouse_id, device_id, device_ip, device_name, device_no, archive_id, archive_name,
        archive_no, created_at, updated_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from eas_device_alarm_record
        <where>
            <if test="archiveNameLike != null and archiveNameLike != ''">
                <bind name="_archiveNameLike" value="'%' + archiveNameLike + '%'"/>
                and archive_name like #{_archiveNameLike,jdbcType=VARCHAR}
            </if>
            <if test="archiveNoLike != null and archiveNoLike != ''">
                <bind name="_archiveNoLike" value="'%' + archiveNoLike + '%'"/>
                and archive_no like #{_archiveNoLike,jdbcType=VARCHAR}
            </if>
            <if test="deviceNameLike != null and deviceNameLike != ''">
                <bind name="_deviceNameLike" value="'%' + deviceNameLike + '%'"/>
                and device_name like #{_deviceNameLike,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="insert">
        insert into eas_device_alarm_record (warehouse_id, device_id, device_ip, device_name, device_no, archive_id,
                                             archive_name, archive_no)
        values (#{warehouseId,jdbcType=BIGINT}, #{deviceId,jdbcType=BIGINT}, #{deviceIp,jdbcType=VARCHAR},
                #{deviceName,jdbcType=VARCHAR}, #{deviceNo,jdbcType=VARCHAR}, #{archiveId,jdbcType=BIGINT},
                #{archiveName,jdbcType=VARCHAR}, #{archiveNo,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteById">
        delete
        from eas_device_alarm_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>