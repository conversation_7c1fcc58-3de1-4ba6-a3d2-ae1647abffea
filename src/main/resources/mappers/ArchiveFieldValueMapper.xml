<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.archive.ArchiveFieldValueMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.archive.ArchiveFieldValue">
        <!--@mbg.generated-->
        <!--@Table archive_field_value-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="archive_id" jdbcType="BIGINT" property="archiveId"/>
        <result column="archives_field_id" jdbcType="BIGINT" property="archivesFieldId"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="field_value" jdbcType="VARCHAR" property="fieldValue"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        archive_id,
        archives_field_id,
        field_name,
        field_value,
        created_at
    </sql>

    <select id="selectListByArchiveIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_field_value
        where  archive_id in
        <foreach collection="archiveIdList" item="archiveId" open="(" separator="," close=")">
            #{archiveId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectListByArchiveId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from archive_field_value
        where archive_id = #{archiveId,jdbcType=BIGINT}
    </select>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into archive_field_value
            (archive_id, archives_field_id, field_name, field_value)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.archiveId,jdbcType=BIGINT}, #{item.archivesFieldId,jdbcType=BIGINT},
             #{item.fieldName,jdbcType=VARCHAR},
             #{item.fieldValue,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteByArchiveId">
        delete
        from archive_field_value
        where archive_id = #{archiveId,jdbcType=BIGINT}
    </delete>
</mapper>