<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.policy.PolicyMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.policy.Policy">
        <!--@mbg.generated-->
        <!--@Table policy-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="is_system" jdbcType="TINYINT" property="system"/>
        <result column="is_enabled" jdbcType="TINYINT" property="enabled"/>
        <result column="policy_type" jdbcType="VARCHAR" property="policyType"/>
        <result column="policy_name" jdbcType="VARCHAR" property="policyName"/>
        <result column="policy_description" jdbcType="VARCHAR" property="policyDescription"/>
        <result column="policy_condition" jdbcType="VARCHAR" property="policyCondition"/>
        <result column="policy_period" jdbcType="VARCHAR" property="policyPeriod"/>
        <result column="policy_action" jdbcType="VARCHAR" property="policyAction"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="updated_id" jdbcType="BIGINT" property="updatedId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        warehouse_id,
        area_id,
        is_system,
        is_enabled,
        policy_type,
        policy_name,
        policy_description,
        policy_condition,
        policy_period,
        policy_action,
        created_id,
        updated_id,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from policy
        where delete_at = 0
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId,jdbcType=BIGINT}
        </if>
        <if test="areaId != null">
            and area_id = #{areaId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectListByAreaId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from policy
        where delete_at = 0
          and area_id = #{areaId,jdbcType=BIGINT}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from policy
        where id = #{id,jdbcType=BIGINT}
          and delete_at = 0
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="org.zxwl.smart.mybatis.entity.policy.Policy"
            useGeneratedKeys="true">
        insert
        into policy (warehouse_id, area_id, is_system, policy_type, policy_name, policy_description, policy_condition,
                     policy_period, policy_action,
                     created_id, updated_id)
        values (#{warehouseId,jdbcType=BIGINT}, #{areaId,jdbcType=BIGINT}, #{system,jdbcType=TINYINT},
                #{policyType,jdbcType=VARCHAR}, #{policyName,jdbcType=VARCHAR}, #{policyDescription,jdbcType=VARCHAR},
                #{policyCondition,jdbcType=VARCHAR}, #{policyPeriod,jdbcType=VARCHAR}, #{policyAction,jdbcType=VARCHAR},
                #{createdId,jdbcType=BIGINT}, #{updatedId,jdbcType=BIGINT})
    </insert>

    <insert id="insertList" keyColumn="id" keyProperty="id" parameterType="org.zxwl.smart.mybatis.entity.policy.Policy"
            useGeneratedKeys="true">
        insert
        into policy (warehouse_id, area_id, is_system, policy_type, policy_name, policy_description, policy_condition,
                     policy_period, policy_action,
                     created_id, updated_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseId,jdbcType=BIGINT}, #{item.areaId,jdbcType=BIGINT}, #{item.system,jdbcType=TINYINT},
             #{item.policyType,jdbcType=VARCHAR}, #{item.policyName,jdbcType=VARCHAR},
             #{item.policyDescription,jdbcType=VARCHAR}, #{item.policyCondition,jdbcType=VARCHAR},
             #{item.policyPeriod,jdbcType=VARCHAR}, #{item.policyAction,jdbcType=VARCHAR},
             #{item.createdId,jdbcType=BIGINT}, #{item.updatedId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateById" parameterType="org.zxwl.smart.mybatis.entity.policy.Policy">
        update policy
        set policy_name        = #{policyName,jdbcType=VARCHAR},
            policy_description = #{policyDescription,jdbcType=VARCHAR},
            policy_condition   = #{policyCondition,jdbcType=VARCHAR},
            policy_period      = #{policyPeriod,jdbcType=VARCHAR},
            policy_action      = #{policyAction,jdbcType=VARCHAR},
            updated_id         = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateEnabledById">
        update policy
        set is_enabled = #{enabled,jdbcType=TINYINT},
            updated_id = #{updatedId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateList" parameterType="java.util.List">
        update policy
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="policy_action = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.policyAction,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteById">
        update policy
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteByIdList" parameterType="java.util.List">
        update policy
        set delete_at = #{deleteAt,jdbcType=BIGINT}
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>