<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.user.OrganizationUserMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.user.OrganizationUser">
        <!--@mbg.generated-->
        <!--@Table organization_user-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        organization_id,
        user_id,
        created_at
    </sql>

    <select id="selectOrganizationIdSetByUserId" resultType="java.lang.Long">
        select organization_id
        from organization_user
        where user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="existByOrganizationIdAndUserId" resultType="int">
        select count(*)
        from organization_user
        where organization_id = #{organizationId,jdbcType=BIGINT}
          and user_id = #{userId,jdbcType=BIGINT}
        limit 1
    </select>

    <delete id="deleteByUserId">
        delete
        from organization_user
        where user_id = #{userId,jdbcType=BIGINT}
    </delete>

    <insert id="insertBatch">
        insert into organization_user (organization_id, user_id)
        values
        <foreach collection="organizationUserList" item="item" separator=",">
            (#{item.organizationId,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>