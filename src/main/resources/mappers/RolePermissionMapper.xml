<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.RolePermissionMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.RolePermission">
        <!--@mbg.generated-->
        <!--@Table `sys_role_permission`-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="permission_id" jdbcType="BIGINT" property="permissionId"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        role_id,
        permission_id,
        created_id,
        created_at
    </sql>

    <select id="selectPermissionIdListByRoleId" resultType="java.lang.Long">
        select permission_id
        from sys_role_permission
        where role_id = #{roleId,jdbcType=BIGINT}
    </select>

    <insert id="insertBatch">
        insert into sys_role_permission (role_id, permission_id, created_id)
        values
        <foreach collection="rolePermissionList" item="item" separator=",">
            (#{item.roleId,jdbcType=BIGINT}, #{item.permissionId,jdbcType=BIGINT}, #{item.createdId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByRoleId">
        delete
        from sys_role_permission
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByRoleIdList">
        delete
        from sys_role_permission
        where role_id in
        <foreach collection="roleIdList" item="roleId" open="(" separator="," close=")">
            #{roleId,jdbcType=BIGINT}
        </foreach>
    </delete>
</mapper>