<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zxwl.smart.mybatis.mapper.sys.PermissionApiMapper">
    <resultMap id="BaseResultMap" type="org.zxwl.smart.mybatis.entity.sys.PermissionApi">
        <!--@mbg.generated-->
        <!--@Table `sys_permission_api`-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="permission_code" jdbcType="VARCHAR" property="permissionCode"/>
        <result column="uri" jdbcType="VARCHAR" property="uri"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_at" jdbcType="BIGINT" property="deleteAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        permission_code,
        uri,
        created_at,
        updated_at,
        delete_at
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_permission_api
        where delete_at = 0
    </select>
</mapper>