<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <title>Hello WebSocket</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"
          integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous"/>
    <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>

    <style>
        body {
            background-color: #f5f5f5;
        }

        #main-content {
            max-width: 940px;
            margin: 0 auto 20px;
            padding: 2em 3em;

            border: 1px solid #e5e5e5;
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            border-radius: 5px;
            background-color: #fff;
        }
    </style>
</head>

<body>
<noscript>
    <h2 style="color: #ff0000">
        Seems your browser doesn't support Javascript! Websocket relies on
        Javascript being enabled. Please enable Javascript and reload this page!
    </h2>
</noscript>
<div id="main-content" class="container">
    <div class="row">
        <div class="col-md-6">
            <form class="form-inline">
                <div class="form-group">
                    <label for="connect">WebSocket connection:</label>
                    <button id="connect" class="btn btn-default" type="submit">
                        Connect
                    </button>
                    <button id="disconnect" class="btn btn-default" type="submit" disabled="disabled">
                        Disconnect
                    </button>
                </div>
            </form>
        </div>
        <div class="col-md-6">
            <form class="form-inline">
                <div class="form-group">
                    <label for="name">What is your name?</label>
                    <input type="text" id="name" class="form-control" placeholder="Your name here..."/>
                </div>
                <button id="send" class="btn btn-default" type="submit">
                    Send
                </button>
            </form>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <table id="conversation" class="table table-striped">
                <thead>
                <tr>
                    <th>Greetings</th>
                </tr>
                </thead>
                <tbody id="greetings"></tbody>
            </table>
        </div>
    </div>
</div>
<script>
    const stompClient = new StompJs.Client({
        brokerURL: "ws://localhost:18084/ws?Terminal=web&Authorization=Bearer_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOnsidXNlcklkIjoxLCJvcmdhbml6YXRpb25JZCI6MSwidGVybWluYWwiOiJ3ZWIifSwiaXNzIjoiWlhXTC1KV1QtSVNTVUVSIiwiZXhwIjoxNzUwMDg1Njg4LCJpYXQiOjE3NDk0ODA4ODgsImp0aSI6Ijg5NDkzZTM5NDEzNTRiNTU4YWYzOTZjMDcxYjIyODNlIn0.TeVY6BM9ovcFc4tCh8e7BXefhVY9BQVoYBAwYPohzjLvgZqF8hQ9HfTn6R9QAe7FmSOLekztVF8SmDmMyMIXHA",
    });

    stompClient.onConnect = (frame) => {
        setConnected(true);
        console.log("Connected: " + frame);
        stompClient.subscribe("/topic/deviceDataChanged", (greeting) => {
            showGreeting("deviceDataChanged: " + JSON.stringify(greeting.body));
        });
        stompClient.subscribe("/topic/areaDataChanged", (greeting) => {
            showGreeting("areaDataChanged: " + JSON.stringify(greeting.body));
        });
        stompClient.subscribe("/topic/warehouseDataChanged", (greeting) => {
            showGreeting("warehouseDataChanged: " + JSON.stringify(greeting.body));
        });
    };

    stompClient.onWebSocketError = (error) => {
        console.error("Error with websocket", error);
    };

    stompClient.onStompError = (frame) => {
        console.error("Broker reported error: " + frame.headers["message"]);
        console.error("Additional details: " + frame.body);
    };

    function setConnected(connected) {
        $("#connect").prop("disabled", connected);
        $("#disconnect").prop("disabled", !connected);
        if (connected) {
            $("#conversation").show();
        } else {
            $("#conversation").hide();
        }
        $("#greetings").html("");
    }

    function connect() {
        stompClient.activate();
    }

    function disconnect() {
        stompClient.deactivate();
        setConnected(false);
        console.log("Disconnected");
    }

    function sendName() {
        stompClient.publish({
            destination: "/app/getWSEnvDeviceAreaList",
            body: JSON.stringify({warehouseId: 13}),
        });
    }

    function showGreeting(message) {
        $("#greetings").append("<tr><td>" + message + "</td></tr>");
    }

    $(function () {
        $("form").on("submit", (e) => e.preventDefault());
        $("#connect").click(() => connect());
        $("#disconnect").click(() => disconnect());
        $("#send").click(() => sendName());
    });
</script>
</body>

</html>