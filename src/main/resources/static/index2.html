<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>简化WebRTC播放器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .config {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .config input {
            margin: 5px;
            padding: 8px;
            width: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #video {
            width: 100%;
            max-width: 640px;
            height: auto;
            background-color: #000;
            border-radius: 8px;
        }

        .video-container {
            text-align: center;
            margin: 20px 0;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>简化WebRTC播放器</h1>

    <div class="config">
        <h3>连接配置</h3>
        <input type="text" id="webrtcUrl" value="http://*************:8889/proxied/whep" placeholder="WebRTC URL (例如: http://*************:8889/proxied/whep)" style="width: 400px;">
    </div>

    <div class="controls">
        <button id="startBtn" class="btn btn-success">开始播放</button>
        <button id="stopBtn" class="btn btn-danger" disabled>停止播放</button>
    </div>

    <div id="status" class="status" style="display: none;"></div>

    <div class="video-container">
        <video id="video" controls autoplay muted playsinline></video>
    </div>
</div>

<script>
    class SimpleWebRTCPlayer {
        constructor() {
            this.pc = null;
            this.video = document.getElementById('video');
            this.startBtn = document.getElementById('startBtn');
            this.stopBtn = document.getElementById('stopBtn');
            this.status = document.getElementById('status');

            this.startBtn.onclick = () => this.start();
            this.stopBtn.onclick = () => this.stop();
        }

        showStatus(message, type = 'info') {
            this.status.textContent = message;
            this.status.className = `status ${type}`;
            this.status.style.display = 'block';
            console.log(message);
        }

        async start() {
            try {
                this.showStatus('正在连接...', 'info');
                this.startBtn.disabled = true;

                // 创建RTCPeerConnection
                this.pc = new RTCPeerConnection({});

                // 关键事件监听器
                this.pc.ontrack = (event) => {
                    console.log('收到轨道:', event.track.kind);
                    if (event.track.kind === 'video') {
                        this.video.srcObject = event.streams[0];
                        this.showStatus('视频流已连接', 'success');
                        this.stopBtn.disabled = false;

                        // 尝试播放
                        this.video.play().catch(e => {
                            console.error('播放失败:', e);
                            this.createPlayButton();
                        });
                    }
                };

                this.pc.oniceconnectionstatechange = () => {
                    const state = this.pc.iceConnectionState;
                    console.log('ICE状态:', state);

                    if (state === 'failed' || state === 'disconnected') {
                        this.showStatus('连接失败', 'error');
                    } else if (state === 'connected' || state === 'completed') {
                        this.showStatus('连接成功', 'success');
                    }
                };

                // 添加接收器
                this.pc.addTransceiver('video', {direction: 'recvonly'});
                this.pc.addTransceiver('audio', {direction: 'recvonly'});

                // 创建offer
                const offer = await this.pc.createOffer();
                await this.pc.setLocalDescription(offer);

                // 发送到MediaMTX
                const whepUrl = document.getElementById('webrtcUrl').value;
                const response = await fetch(whepUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/sdp',
                    },
                    body: offer.sdp
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const answer = await response.text();
                await this.pc.setRemoteDescription({
                    type: 'answer',
                    sdp: answer
                });

                console.log('WebRTC连接建立成功');

            } catch (error) {
                console.error('连接错误:', error);
                this.showStatus(`连接失败: ${error.message}`, 'error');
                this.startBtn.disabled = false;
            }
        }

        stop() {
            if (this.pc) {
                this.pc.close();
                this.pc = null;
            }

            if (this.video.srcObject) {
                this.video.srcObject.getTracks().forEach(track => track.stop());
                this.video.srcObject = null;
            }

            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.status.style.display = 'none';

            // 移除播放按钮
            const playBtn = document.getElementById('manualPlayBtn');
            if (playBtn) playBtn.remove();

            console.log('连接已停止');
        }

        createPlayButton() {
            if (document.getElementById('manualPlayBtn')) return;

            const playBtn = document.createElement('button');
            playBtn.id = 'manualPlayBtn';
            playBtn.textContent = '点击播放视频';
            playBtn.className = 'btn btn-success';
            playBtn.style.margin = '10px';

            playBtn.onclick = () => {
                this.video.play().then(() => {
                    this.showStatus('视频正在播放', 'success');
                    playBtn.remove();
                }).catch(e => {
                    console.error('手动播放失败:', e);
                });
            };

            this.video.parentNode.appendChild(playBtn);
        }
    }

    // 初始化播放器
    document.addEventListener('DOMContentLoaded', () => {
        new SimpleWebRTCPlayer();
    });
</script>
</body>
</html>
