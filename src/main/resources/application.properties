spring.application.name=smart
spring.profiles.active=dev
# jackson配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
#spring.jackson.default-property-inclusion=non_null
# 数据源配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.auto-commit=true
# mybatis配置
mybatis.mapper-locations=classpath:mappers/*.xml
mybatis.type-aliases-package=org.zxwl.smart.mybatis.entity.**
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl
# 日志配置
logging.pattern.dateformat=yyyy-MM-dd'T'HH:mm:ss.SSSXXX
logging.charset.console=UTF-8
logging.file.name=log/smart.log
logging.logback.rollingpolicy.file-name-pattern=${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.total-size-cap=1GB
logging.logback.rollingpolicy.max-history=7
logging.logback.rollingpolicy.clean-history-on-start=false
# 文件存储配置
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=110MB
file.upload.root-path=files
file.upload.url-prefix=/files