server.port=18084
project.env-server-port=18083
project.mediamtx-ip=*************
project.mediamtx-api-port=9997
project.mediamtx-api-user=admin
project.mediamtx-api-pass=ZXWL2025
project.mediamtx-web-rtc-port=8889
project.deviceCodeRsaPublicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqLDnJidW+yGovIso2d+kUjBPTVeHmvyUlgyezHY24j8vatA8PA6+jx6h3kRor52UYhRyrz6T1f2iQ2qNwHX08yw236/NqQDRXAwuI8lLnKJof/Oo8evl/kNnPrjpk7fpkU16gfO/53qR8ruNaqQ7sBOaXEL5E99eJgFufIC0U+QIDAQAB
project.deviceCodeRsaPrivateKey=MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOosOcmJ1b7Iai8iyjZ36RSME9NV4ea/JSWDJ7MdjbiPy9q0Dw8Dr6PHqHeRGivnZRiFHKvPpPV/aJDao3AdfTzLDbfr82pANFcDC4jyUucomh/86jx6+X+Q2c+uOmTt+mRTXqB87/nepHyu41qpDuwE5pcQvkT314mAW58gLRT5AgMBAAECgYAuA5Q0Va0GQM/apMGqy8ulQbQomAzcYE1ujlPH9qPxMvJJ7o0x9FW4P0SLgBvuKhkbpRZh7Vqa8dbsdcXdQzInFnYFwdJ/d2Ckqc2Rig9HaLdSMzLZhthCKathDzee6tdqnmqUjhvvv1kNABF3WNojr9FVncE57AOQICZWlprwPQJBAO4/DsDx9suDWt/ipdWqIHeXTEWeCG31pvRzW3Yu7Z46vfnnPf/ywEVsmrBcgB1AGLEsvqpaPR5jT1vqI3jl4RUCQQD7n3VKjIkP5DU8kNV7b+1U81zVLh8dN0YK7Evhy4qqKvBbt/uf0BO84DBBqN+tt5sTGRzqv/qGiv21zlljQDVVAkEA6csHd1/6h4vHSZzoj9v7MGBQgKCsNPuzcn8hacBzfaq3ctzxgpF9DKOeySJ8tjeIA5WdJR6Yi5F49wG0/ZbYYQJBAMMFvVfxgs4RV33A1+XbQ3yjPuFv5Oe1DpGqwcmZA7mOnErKV7lvAc1TibNqWyt+cjAkN7hu4ZLkKW5/J+L4Qh0CQQCX0ksnyMjS0HNBTH5crZUPst4bjHWuC915e8rd0rVJk62dPVA0w/8mZC2ERp88J3q0occJiF3j2JmsD5dZcmz8
project.activeCodeRsaPublicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqLDnJidW+yGovIso2d+kUjBPTVeHmvyUlgyezHY24j8vatA8PA6+jx6h3kRor52UYhRyrz6T1f2iQ2qNwHX08yw236/NqQDRXAwuI8lLnKJof/Oo8evl/kNnPrjpk7fpkU16gfO/53qR8ruNaqQ7sBOaXEL5E99eJgFufIC0U+QIDAQAB
project.activeCodeRsaPrivateKey=MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOosOcmJ1b7Iai8iyjZ36RSME9NV4ea/JSWDJ7MdjbiPy9q0Dw8Dr6PHqHeRGivnZRiFHKvPpPV/aJDao3AdfTzLDbfr82pANFcDC4jyUucomh/86jx6+X+Q2c+uOmTt+mRTXqB87/nepHyu41qpDuwE5pcQvkT314mAW58gLRT5AgMBAAECgYAuA5Q0Va0GQM/apMGqy8ulQbQomAzcYE1ujlPH9qPxMvJJ7o0x9FW4P0SLgBvuKhkbpRZh7Vqa8dbsdcXdQzInFnYFwdJ/d2Ckqc2Rig9HaLdSMzLZhthCKathDzee6tdqnmqUjhvvv1kNABF3WNojr9FVncE57AOQICZWlprwPQJBAO4/DsDx9suDWt/ipdWqIHeXTEWeCG31pvRzW3Yu7Z46vfnnPf/ywEVsmrBcgB1AGLEsvqpaPR5jT1vqI3jl4RUCQQD7n3VKjIkP5DU8kNV7b+1U81zVLh8dN0YK7Evhy4qqKvBbt/uf0BO84DBBqN+tt5sTGRzqv/qGiv21zlljQDVVAkEA6csHd1/6h4vHSZzoj9v7MGBQgKCsNPuzcn8hacBzfaq3ctzxgpF9DKOeySJ8tjeIA5WdJR6Yi5F49wG0/ZbYYQJBAMMFvVfxgs4RV33A1+XbQ3yjPuFv5Oe1DpGqwcmZA7mOnErKV7lvAc1TibNqWyt+cjAkN7hu4ZLkKW5/J+L4Qh0CQQCX0ksnyMjS0HNBTH5crZUPst4bjHWuC915e8rd0rVJk62dPVA0w/8mZC2ERp88J3q0occJiF3j2JmsD5dZcmz8
project.activeCodeFilePath=active
project.activeCodeFileName=active-code.txt
# 日志配置
logging.level.org.zxwl.smart=debug
logging.level.sql=debug
# 数据库配置
spring.datasource.url=*************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
# doc文档配置
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.packages-to-scan=org.zxwl.smart.controller
# jackson配置
spring.jackson.parser.allow-comments=true