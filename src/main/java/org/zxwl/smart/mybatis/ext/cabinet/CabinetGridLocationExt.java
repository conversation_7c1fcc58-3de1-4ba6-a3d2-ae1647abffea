package org.zxwl.smart.mybatis.ext.cabinet;

import lombok.Data;

@Data
public class CabinetGridLocationExt {

  /** 机构id */
  private Long organizationId;

  /** 柜组编号 */
  private Integer groupNo;

  /** 柜体编号 */
  private Integer cabinetNo;

  /** 格口编号 */
  private Integer gridNo;

  /** 格口id */
  private Long gridId;

  /** 容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 格口状态，normal正常，open打开，lock锁定 */
  private String gridStatus;
}
