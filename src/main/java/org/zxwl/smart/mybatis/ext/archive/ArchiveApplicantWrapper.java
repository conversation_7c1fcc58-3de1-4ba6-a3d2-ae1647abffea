package org.zxwl.smart.mybatis.ext.archive;

import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ArchiveApplicantWrapper {

  /** 档案名称精确查询 */
  private String archiveName;

  /** 档案名称模糊查询 */
  private String archiveNameLike;

  /** 档案编号精确查询 */
  private String archiveNo;

  /** 档案编号模糊查询 */
  private String archiveNoLike;

  /** 状态，pending待审批，approved审批通过，reject审批拒绝，revoke已撤销 */
  private List<String> statusList;

  /** 创建人id */
  private Long createdId;

  /** 开始创建时间 */
  private Date beginCreateAt;

  /** 截止创建时间 */
  private Date endCreateAt;
}
