package org.zxwl.smart.mybatis.ext.policy;

import java.util.List;
import lombok.Data;

@Data
public class PolicyPeriod {

  /** 重复周期，不重复，每天，每周、每月 */
  private String repeatCycle;

  /** 执行周期日 */
  private List<Integer> repeatDayList;

  /** 执行日期，格式为 yyyy-MM-dd */
  private String repeatDate;

  /** 开始时间，格式为 HH:mm:ss */
  private String startTime;

  /** 结束时间，格式为 HH:mm:ss */
  private String endTime;
}
