package org.zxwl.smart.mybatis.ext.archive;

import lombok.Data;

@Data
public class ArchiveStatExt {

  /** 资料总数 */
  private Integer totalCount;

  /** 资料已注册数 */
  private Integer bindTidCount;

  /** 资料未注册数 */
  private Integer unbindTidCount;

  /** 资料待上架数 */
  private Integer shelvingPendingInCount;

  /** 资料在架数 */
  private Integer inShelvingCount;

  /** 资料待下架数 */
  private Integer shelvingPendingOutCount;

  /** 资料已下架数 */
  private Integer outShelvingCount;
}
