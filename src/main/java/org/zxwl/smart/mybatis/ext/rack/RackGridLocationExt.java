package org.zxwl.smart.mybatis.ext.rack;

import lombok.Data;

@Data
public class RackGridLocationExt {

  /** 机构id */
  private Long organizationId;

  /** 密集架类型，普通密集架，智能密集架，盒定位密集架，发光密集架 */
  private String rackType;

  /** 组编号 */
  private Integer groupNo;

  /** 列号 */
  private Integer columnNo;

  /** 面号 */
  private Integer panelNo;

  /** 节号 */
  private Integer sectionNo;

  /** 层号 */
  private Integer layerNo;

  /** 层id */
  private Long layerId;

  /** 层容量 */
  private Integer capacity;

  /** 层已用容量 */
  private Integer usedCapacity;

  /** 格子号/格子编号 */
  private Integer gridNo;
}
