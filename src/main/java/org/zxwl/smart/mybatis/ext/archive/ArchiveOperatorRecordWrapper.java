package org.zxwl.smart.mybatis.ext.archive;

import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ArchiveOperatorRecordWrapper {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /**
   * 操作类型，create-新建，update-更新，delete-删除，bind_tid-绑定TID，update_tid-更新TID，unbind_tid-解绑TID，boxing-装盒，unboxing-拆盒，stock_in-入库，stock_out-出库，shelve-上架，unshelve-下架
   */
  private String operatorType;

  /** 档案门类id */
  private Long archiveTypeId;

  /** 档案id */
  private Long archiveId;

  /** 档案idList */
  private List<Long> archiveIdList;

  /** 类型，box档案盒，archive档案，volume卷内档案 */
  private String archiveType;

  /** 名字 */
  private String archiveName;

  /** 编号 */
  private String archiveNo;

  /** 档案所在密集架组号 */
  private Integer rackGroupNo;

  /** 档案所在密集架列号 */
  private Integer rackColumnNo;

  /** 档案所在密集架面号 */
  private Integer rackPanelNo;

  /** 档案所在密集架节号 */
  private Integer rackSectionNo;

  /** 档案所在密集架层号 */
  private Integer rackLayerNo;

  /** 档案所在密集架格号 */
  private Integer rackGridNo;

  /** 档案所在人事柜组号 */
  private Integer hrCabinetGroupNo;

  /** 档案所在人事柜号 */
  private Integer hrCabinetNo;

  /** 档案所在人事柜层号 */
  private Integer hrCabinetLayerNo;

  /** 档案所在人事柜格号 */
  private Integer hrCabinetGridNo;

  /** 档案所在柜组号 */
  private Integer cabinetGroupNo;

  /** 档案所在柜号 */
  private Integer cabinetNo;

  /** 档案所在柜格号 */
  private Integer cabinetGridNo;

  /** 操作人id */
  private Long operatorId;

  /** 创建时间 */
  private Date createdAt;
}
