package org.zxwl.smart.mybatis.ext.archive;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ArchiveTaskListWrapper {

  /** 档案名称精确查询 */
  private String archiveName;

  /** 档案名称模糊查询 */
  private String archiveNameLike;

  /** 档案编号精确查询 */
  private String archiveNo;

  /** 档案编号模糊查询 */
  private String archiveNoLike;

  /** 开始创建时间 */
  private Date beginCreateAt;

  /** 截止创建时间 */
  private Date endCreateAt;

  /** 状态，pending待执行，executing执行中，pending_pickup待取出，completed完成 */
  private List<String> statusList;

  /** 任务类型，in存入，out取出 */
  private String taskType;
}
