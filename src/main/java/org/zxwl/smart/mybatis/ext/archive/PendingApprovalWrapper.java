package org.zxwl.smart.mybatis.ext.archive;

import lombok.Data;

import java.util.Date;

@Data
public class PendingApprovalWrapper {

  /** 档案名称精确查询 */
  private String archiveName;

  /** 档案名称模糊查询 */
  private String archiveNameLike;

  /** 档案编号精确查询 */
  private String archiveNo;

  /** 档案编号模糊查询 */
  private String archiveNoLike;

  /** 申请人姓名精确查询 */
  private String applicantName;

  /** 申请人姓名模糊查询 */
  private String applicantNameLike;

  /** 申请人姓名精确查询 */
  private String operatorName;

  /** 申请人姓名模糊查询 */
  private String operatorNameLike;

  /** 状态，pending待审批，approved审批通过，reject审批拒绝，revoke已撤销 */
  private String status;

  /** 开始创建时间 */
  private Date beginCreateAt;

  /** 截止创建时间 */
  private Date endCreateAt;
}
