package org.zxwl.smart.mybatis.ext.archive;

/** v3.oas.annotations.media.Schema */
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ArchivePendingStorageListWrapper {

  /** "档案名称精确查询" */
  private String archiveName;

  /** "档案名称模糊查询" */
  private String archiveNameLike;

  /** "档案编号精确查询" */
  private String archiveNo;

  /** "档案编号模糊查询" */
  private String archiveNoLike;

  /** "取出时间范围，例如：['2022-06-11 12:00:00', '2022-06-13 12:00:00']" */
  private List<Date> pickupAtRange;

  /** 开始创建时间 */
  private Date beginCreateAt;

  /** 截止创建时间 */
  private Date endCreateAt;
}
