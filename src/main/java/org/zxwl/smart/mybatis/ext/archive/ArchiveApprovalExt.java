package org.zxwl.smart.mybatis.ext.archive;

import java.util.Date;
import lombok.Data;
import org.zxwl.smart.mybatis.entity.archive.ArchiveApproval;

@Data
public class ArchiveApprovalExt extends ArchiveApproval {

  /** 档案id */
  private Long archiveId;

  /** 申请人姓名 */
  private String applicantName;

  /** 经办人id */
  private Long operatorId;

  /** 经办人名字 */
  private String operatorName;

  /** 申请备注 */
  private String remark;

  /** 申请创建时间 */
  private Date applicantCreatedAt;

  /** 档案门类id */
  private Long archiveTypeId;

  /** 类型，box档案盒，archive档案，volume卷内档案 */
  private String type;

  /** 文件名字 */
  private String archiveName;

  /** 文件编号 */
  private String archiveNo;

  /** 档案所在密集架组号 */
  private Integer rackGroupNo;

  /** 档案所在密集架列号 */
  private Integer rackColumnNo;

  /** 档案所在密集架面号 */
  private Integer rackPanelNo;

  /** 档案所在密集架节号 */
  private Integer rackSectionNo;

  /** 档案所在密集架层号 */
  private Integer rackLayerNo;

  /** 档案所在密集架格号 */
  private Integer rackGridNo;

  /** 档案所在柜组号 */
  private Integer cabinetGroupNo;

  /** 档案所在柜号 */
  private Integer cabinetNo;

  /** 档案所在柜格号 */
  private Integer cabinetGridNo;
}
