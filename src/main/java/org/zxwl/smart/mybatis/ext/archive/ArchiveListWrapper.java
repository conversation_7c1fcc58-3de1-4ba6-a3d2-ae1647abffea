package org.zxwl.smart.mybatis.ext.archive;

import java.util.List;
import lombok.Data;

@Data
public class ArchiveListWrapper {

  /** 机构id */
  private Long organizationId;

  /** 档案门类id */
  private Long archiveTypeId;

  /** 上层id（盒id或档案id） */
  private Long parentId;

  /** 类型，box档案盒，archive档案，volume卷内档案 */
  private String type;

  /** 在库状态，in在库，out不在库 */
  private String stockStatus;

  /** 在架状态，pending_in待上架，in在架，pending_out待下架，out不在架 */
  private List<String> shelvingStatusList;

  /** 名字 */
  private String archiveName;

  /** 名字模糊匹配 */
  private String archiveNameLike;

  /** 编号 */
  private String archiveNo;

  /** 编号模糊匹配 */
  private String archiveNoLike;

  /** 标签TID集合 */
  private List<String> tidList;

  /** 是否绑定TID */
  private Boolean bindTid;

  /** 档案所在密集架组号 */
  private Integer rackGroupNo;

  /** 档案所在密集架列号 */
  private Integer rackColumnNo;

  /** 档案所在密集架面号 */
  private Integer rackPanelNo;

  /** 档案所在密集架节号 */
  private Integer rackSectionNo;

  /** 档案所在密集架层号 */
  private Integer rackLayerNo;

  /** 档案所在密集架格号 */
  private Integer rackGridNo;

  /** 档案所在人事柜组号 */
  private Integer hrCabinetGroupNo;

  /** 档案所在人事柜号 */
  private Integer hrCabinetNo;

  /** 档案所在人事柜层号 */
  private Integer hrCabinetLayerNo;

  /** 档案所在柜组号 */
  private Integer cabinetGroupNo;

  /** 档案所在柜号 */
  private Integer cabinetNo;

  /** 档案所在柜格号 */
  private Integer cabinetGridNo;
}
