package org.zxwl.smart.mybatis.entity.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 配置信息表
 */
@Schema(description="配置信息表")
@Data
public class SysConfiguration implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    @Schema(description="主键id")
    private Long id;

    /**
    * 终端码
    */
    @Schema(description="终端码")
    private String terminalCode;

    /**
    * 版本号
    */
    @Schema(description="版本号")
    private String version;

    /**
    * 配置类型，sys系统配置，app应用配置，page页面配置
    */
    @Schema(description="配置类型，sys系统配置，app应用配置，page页面配置")
    private String configType;

    /**
    * 配置内容，JSON字符串
    */
    @Schema(description="配置内容，JSON字符串")
    private String configContext;

    /**
    * 模板内容，JSON字符串
    */
    @Schema(description="模板内容，JSON字符串")
    private String configTemplate;

    /**
    * 配置状态，in_use在用，history历史
    */
    @Schema(description="配置状态，in_use在用，history历史")
    private String configStatus;

    /**
    * 创建人id
    */
    @Schema(description="创建人id")
    private Long createdId;

    /**
    * 更新人id
    */
    @Schema(description="更新人id")
    private Long updatedId;

    /**
    * 创建时间
    */
    @Schema(description="创建时间")
    private Date createdAt;

    /**
    * 更新时间
    */
    @Schema(description="更新时间")
    private Date updatedAt;

    /**
    * 删除时间戳，标记是否被逻辑删除
    */
    @Schema(description="删除时间戳，标记是否被逻辑删除")
    private Long deleteAt;
}