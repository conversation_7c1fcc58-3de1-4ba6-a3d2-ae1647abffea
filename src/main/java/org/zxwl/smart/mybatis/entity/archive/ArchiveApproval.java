package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案取档审批表 */
@Data
public class ArchiveApproval implements Serializable {
  /** 主键id */
  private Long id;

  /** 取档申请id */
  private Long archiveApplicantId;

  /** 审批状态，approved审批通过，reject审批拒绝 */
  private String approvalStatus;

  /** 审批人id */
  private Long approverId;

  /** 审批时间 */
  private Date approvalAt;

  /** 审批意见 */
  private String approvalComment;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
