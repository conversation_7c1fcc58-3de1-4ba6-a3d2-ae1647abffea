package org.zxwl.smart.mybatis.entity.cabinet;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 柜组 */
@Data
public class CabinetGroup implements Serializable {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /** 所属库房id */
  private Long warehouseId;

  /** 柜体类型 */
  private String cabinetType;

  /** 柜组编号 */
  private Integer groupNo;

  /** 柜组名 */
  private String groupName;

  /** 主柜编号 */
  private Integer mainCabinetNo;

  /** 副柜数量 */
  private Integer subCabinetCount;

  /** 主柜格数 */
  private Integer mainCabinetGridCount;

  /** 副柜格数 */
  private Integer subCabinetGridCount;

  /** 格口容量 */
  private Integer cabinetGridCapacity;

  /** 通信ip */
  private String controlIp;

  /** 通信端口 */
  private Integer controlPort;

  /** 容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 备注 */
  private String remark;

  /** 地图配置信息 */
  private String visualConfig;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
