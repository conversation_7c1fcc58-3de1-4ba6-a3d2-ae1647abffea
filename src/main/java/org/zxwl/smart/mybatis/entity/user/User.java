package org.zxwl.smart.mybatis.entity.user;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 用户表 */
@Data
public class User implements Serializable {

  /** 主键id */
  private Long id;

  /** 部门id */
  private Long deptId;

  /** 超管标识，0普通员工，1超管 */
  private Boolean adminFlag;

  /** 用户状态，normal正常，disable禁用 */
  private String userStatus;

  /** 性别，male男，female女 */
  private String userSex;

  /** 电话号码 */
  private String mobile;

  /** 邮箱 */
  private String email;

  /** 用户名 */
  private String userName;

  /** 用户账号 */
  private String userAccount;

  /** 用户密码 */
  private String userPwd;

  /** 卡号 */
  private String idCard;

  /** 指纹id */
  private String fingerId;

  /** 人脸地址 */
  private String faceId;

  /** 手势 */
  private String gesture;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
