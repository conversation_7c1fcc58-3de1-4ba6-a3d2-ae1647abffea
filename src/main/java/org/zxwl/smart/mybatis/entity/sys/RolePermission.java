package org.zxwl.smart.mybatis.entity.sys;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 角色权限关联实体类 */
@Data
public class RolePermission implements Serializable {

  /** 主键id */
  private Long id;

  /** 角色id */
  private Long roleId;

  /** 权限id */
  private Long permissionId;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  private static final long serialVersionUID = 1L;
}
