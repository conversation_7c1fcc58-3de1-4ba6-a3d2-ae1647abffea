package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案字段值表 */
@Data
public class ArchiveFieldValue implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属档案id */
  private Long archiveId;

  /** 档案字段id */
  private Long archivesFieldId;

  /** 字段名 */
  private String fieldName;

  /** 字段值 */
  private String fieldValue;

  /** 创建时间 */
  private Date createdAt;

  private static final long serialVersionUID = 1L;
}
