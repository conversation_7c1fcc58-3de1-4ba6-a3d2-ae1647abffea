package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 环控设备调控日志记录表 */
@Data
public class EnvDeviceLog implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 主键id */
  private Long id;

  /** 所属库房id */
  private Long warehouseId;

  /** 设备类型 */
  private String deviceType;

  /** 设备id */
  private Long deviceId;

  /** 设备名称 */
  private String deviceName;

  /** 操作描述 */
  private String operateContext;

  /** 操作人id */
  private Long createdId;

  /** 操作时间 */
  private Date createdAt;
}