package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 防盗门设备告警记录表 */
@Data
public class EasDeviceAlarmRecord implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属库房id */
  private Long warehouseId;

  /** 防盗门id */
  private Long deviceId;

  /** 防盗门ip */
  private String deviceIp;

  /** 防盗门名称 */
  private String deviceName;

  /** 防盗门编号 */
  private String deviceNo;

  /** 档案id */
  private Long archiveId;

  /** 档案名称 */
  private String archiveName;

  /** 档案编号 */
  private String archiveNo;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
