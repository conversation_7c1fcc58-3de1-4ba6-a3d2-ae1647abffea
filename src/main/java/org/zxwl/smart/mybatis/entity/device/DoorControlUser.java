package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 门禁用户表 */
@Data
public class DoorControlUser implements Serializable {
  /** 主键id */
  private Long id;

  /** 设备编号 */
  private String deviceNo;

  /** 用户通行状态，normal可通行，disable不可通行 */
  private String userStatus;

  /** 用户通行类型，长期为0，单次为1 */
  private Integer userType;

  /** 图片地址 */
  private String imgUrl;

  /** 用户工号 */
  private String idCard;

  /** 用户名 */
  private String userName;

  /** 用户权限值 */
  private Integer pri;

  /** 用户密码 */
  private String userPwd;

  /** 用户卡号 */
  private String card;

  /** 用户所属组 */
  private Integer userGroup;

  /** 用户使用的时间段编号信息 */
  private String tz;

  /** 用户验证方式 */
  private Integer verifyType;

  /** 用户副卡号 */
  private String viceCard;

  /** 有效期开始日期 */
  private String startDateTime;

  /** 有效期结束日期 */
  private String endDateTime;

  /** 指纹id */
  private String fingerId;

  /** 人脸地址 */
  private String faceId;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
