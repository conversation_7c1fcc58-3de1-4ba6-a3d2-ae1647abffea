package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 门禁设备历史记录表 */
@Data
public class DoorControlRecord implements Serializable {
  /** 主键id */
  private Long id;

  /** 设备编号 */
  private String deviceNo;

  /** 库房id */
  private Long warehouseId;

  /** 设备名字 */
  private String deviceName;

  /** 开门方式 */
  private String controlMethod;

  /** 设备关联用户id */
  private Long deviceUserId;

  /** 设备关联用户名 */
  private String deviceUserName;

  /** 设备关联用户工号 */
  private String userIdCard;

  /** 创建时间 */
  private Date createdAt;

  private static final long serialVersionUID = 1L;
}
