package org.zxwl.smart.mybatis.entity.inventory;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 机器人盘点任务表 */
@Data
public class RobotInventoryTask implements Serializable {
  /** 主键id */
  private Long id;

  /** 盘点库房id */
  private Long warehouseId;

  /** 任务名字 */
  private String taskName;

  /** 任务状态，unstarted未开启，started已开始，finished完成 */
  private String taskStatus;

  /** 盘点机器人id */
  private Long robotId;

  /** 任务周期，single单次，weekly每周，monthly每月，yearly每年 */
  private String taskCycle;

  /** 任务执行周期天数 */
  private Integer taskCycleDay;

  /** 任务执行时间 */
  private Date taskTime;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
