package org.zxwl.smart.mybatis.entity.cabinet;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 人事柜层信息表 */
@Data
public class HrCabinetLayer implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属柜组id */
  private Long cabinetGroupId;

  /** 柜体编号 */
  private Integer cabinetNo;

  /** 柜层编号 */
  private Integer layerNo;

  /** 柜层总格数 */
  private Integer cabinetGridNum;

  /** 容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
