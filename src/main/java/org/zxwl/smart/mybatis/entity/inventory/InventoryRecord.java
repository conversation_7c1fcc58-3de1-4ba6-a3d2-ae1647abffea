package org.zxwl.smart.mybatis.entity.inventory;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 盘点记录表 */
@Data
public class InventoryRecord implements Serializable {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /** 盘点任务id */
  private Long inventoryTaskId;

  /** 设备id */
  private Long deviceId;

  /** 库房id */
  private Long warehouseId;

  /** 盘点类型，handheld-手持机，inventory_car-盘点车，cabinet-智能柜，rack-密集架 */
  private String inventorType;

  /** 在架数 */
  private Integer onShelfCount;

  /** 不在架数 */
  private Integer offShelfCount;

  /** 错架数 */
  private Integer misplacedCount;

  /** 在借数 */
  private Integer borrowCount;

  /** 未注册数 */
  private Integer unbindCount;

  /** 密集架组号 */
  private Integer rackGroupNo;

  /** 密集架列号 */
  private Integer rackColumnNo;

  /** 密集架面号 */
  private Integer rackPanelNo;

  /** 密集架节号 */
  private Integer rackSectionNo;

  /** 密集架层号 */
  private Integer rackLayerNo;

  /** 档案所在人事柜组号 */
  private Integer hrCabinetGroupNo;

  /** 档案所在人事柜号 */
  private Integer hrCabinetNo;

  /** 档案所在人事柜层号 */
  private Integer hrCabinetLayerNo;

  /** 柜组号 */
  private Integer cabinetGroupNo;

  /** 柜体号 */
  private Integer cabinetNo;

  /** 柜格号 */
  private Integer cabinetGridNo;

  /** 备注 */
  private String remark;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
