package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案待取表 */
@Data
public class ArchivePendingPickup implements Serializable {
  /** 主键id */
  private Long id;

  /** 档案id */
  private Long archiveId;

  /** 用户id */
  private Long userId;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
