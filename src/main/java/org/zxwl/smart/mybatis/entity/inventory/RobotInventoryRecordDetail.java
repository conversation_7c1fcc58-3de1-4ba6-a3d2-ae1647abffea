package org.zxwl.smart.mybatis.entity.inventory;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 机器人盘点记录详情表 */
@Data
public class RobotInventoryRecordDetail implements Serializable {
  /** 主键id */
  private Long id;

  /** 机器人盘点记录id */
  private Long robotInventoryRecordId;

  /** 密集架组id */
  private Long rackGroupId;

  /** 密集架列编号 */
  private Long rackColumNo;

  /** 密集架面编号 */
  private Long rackPanelNo;

  /** 档案id */
  private Long archiveId;

  /** 档案号 */
  private String archiveNo;

  /** 档案名 */
  private String archiveName;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
