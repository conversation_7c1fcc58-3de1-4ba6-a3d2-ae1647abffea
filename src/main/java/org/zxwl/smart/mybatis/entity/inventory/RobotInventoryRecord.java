package org.zxwl.smart.mybatis.entity.inventory;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 机器人盘点记录表 */
@Data
public class RobotInventoryRecord implements Serializable {
  /** 主键id */
  private Long id;

  /** 盘点任务id */
  private Long robotInventoryTaskId;

  /** 盘点库房id */
  private Long warehouseId;

  /** 盘点机器人id */
  private Long robotId;

  /** 盘点任务名称 */
  private String robotInventoryTaskName;

  /** 盘点状态 */
  private String inventoryStatus;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
