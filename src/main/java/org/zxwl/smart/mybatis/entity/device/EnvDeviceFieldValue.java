package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 环控设备扩展属性表 */
@Data
public class EnvDeviceFieldValue implements Serializable {
  /** 主键id */
  private Long id;

  /** 设备id */
  private Long deviceId;

  /** 属性名 */
  private String filedName;

  /** 属性值 */
  private String filedValue;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
