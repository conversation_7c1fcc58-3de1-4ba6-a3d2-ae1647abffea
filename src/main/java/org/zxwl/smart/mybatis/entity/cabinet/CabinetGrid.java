package org.zxwl.smart.mybatis.entity.cabinet;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 柜子格口表 */
@Data
public class CabinetGrid implements Serializable {
  /** 主键id */
  private Long id;

  /** 柜组id */
  private Long cabinetGroupId;

  /** 柜子id */
  private Long cabinetId;

  /** 格子容量，容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 格口状态，normal正常，open打开，lock锁定 */
  private String gridStatus;

  /** 格子号/格子编号 */
  private Integer gridNo;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
