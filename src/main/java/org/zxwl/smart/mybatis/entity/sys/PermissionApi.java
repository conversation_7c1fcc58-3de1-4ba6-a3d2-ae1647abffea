package org.zxwl.smart.mybatis.entity.sys;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/** 权限接口关联实体类 */
@Data
public class PermissionApi implements Serializable {

  /** 主键id */
  private Long id;

  /** 权限编码 */
  private String permissionCode;

  /** 接口请求URI */
  private String uri;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
