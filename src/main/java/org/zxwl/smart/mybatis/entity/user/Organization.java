package org.zxwl.smart.mybatis.entity.user;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 机构表 */
@Data
public class Organization implements Serializable {
  /** 主键id */
  private Long id;

  /** 上级机构id */
  private Long parentId;

  /** 机构编号 */
  private String organizationNo;

  /** 机构名称 */
  private String organizationName;

  /** 机构层级 */
  private Integer level;

  /** 机构路径 */
  private String address;

  /** 备注 */
  private String remark;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
