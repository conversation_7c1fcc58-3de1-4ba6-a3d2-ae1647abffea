package org.zxwl.smart.mybatis.entity.warehouse;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 库房表 */
@Data
public class Warehouse implements Serializable {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /** 所属楼层id */
  private Long warehouseFloorId;

  /** 库房编号 */
  private Integer warehouseNo;

  /** 库房名 */
  private String warehouseName;

  /** 地图配置信息 */
  private String visualConfig;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
