package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案存取任务表 */
@Data
public class ArchiveTask implements Serializable {
  /** 主键id */
  private Long id;

  /** 审批id */
  private Long archiveApprovalId;

  /** 经办人id */
  private Long operatorId;

  /** 档案id */
  private Long archiveId;

  /** 任务类型，in存入，out取出 */
  private String taskType;

  /** 状态，pending待执行，executing执行中，pending_pickup待取出，completed完成 */
  private String status;

  /** 执行方式，桁架，AGV */
  private String executionType;

  /** 任务来源 */
  private String taskSource;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
