package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 环控能耗记录表 */
@Data
public class EnvEnergyRecord implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属库房id */
  private Long warehouseId;

  /** 所属区域id */
  private Long areaId;

  /** 设备id */
  private Long deviceId;

  /** 电压 */
  private Float voltage;

  /** 电流 */
  private Float current;

  /** 功率 */
  private Float power;

  /** 创建时间 */
  private Date createdAt;

  private static final long serialVersionUID = 1L;
}
