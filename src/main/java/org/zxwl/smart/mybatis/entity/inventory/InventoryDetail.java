package org.zxwl.smart.mybatis.entity.inventory;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 盘点详情表 */
@Data
public class InventoryDetail implements Serializable {
  /** 主键id */
  private Long id;

  /** 盘点记录id */
  private Long inventoryRecordId;

  /** 密集架组号 */
  private Integer rackGroupNo;

  /** 密集架列号 */
  private Integer rackColumnNo;

  /** 密集架面号 */
  private Integer rackPanelNo;

  /** 密集架节号 */
  private Integer rackSectionNo;

  /** 密集架层号 */
  private Integer rackLayerNo;

  /** 密集架格号 */
  private Integer rackGridNo;

  /** 档案所在人事柜组号 */
  private Integer hrCabinetGroupNo;

  /** 档案所在人事柜号 */
  private Integer hrCabinetNo;

  /** 档案所在人事柜层号 */
  private Integer hrCabinetLayerNo;

  /** 档案所在人事柜格号 */
  private Integer hrCabinetGridNo;

  /** 柜组号 */
  private Integer cabinetGroupNo;

  /** 柜体号 */
  private Integer cabinetNo;

  /** 柜格号 */
  private Integer cabinetGridNo;

  /** 档案id */
  private Long archiveId;

  /** 档案编号 */
  private String archiveNo;

  /** 档案名字 */
  private String archiveName;

  /** 标签tid */
  private String tid;

  /** 结果状态，on_shelf-在架，off_shelf-不在架，misplaced-错架，borrow-外借，unbind-未注册 */
  private String status;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
