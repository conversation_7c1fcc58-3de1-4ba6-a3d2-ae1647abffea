package org.zxwl.smart.mybatis.entity.sys;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 用户角色关联实体类 */
@Data
public class UserRole implements Serializable {

  /** 主键id */
  private Long id;

  /** 用户id */
  private Long userId;

  /** 角色id */
  private Long roleId;

  /** 机构id */
  private Long organizationId;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  private static final long serialVersionUID = 1L;
}
