package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案附件表 */
@Data
public class ArchiveAttachment implements Serializable {
  /** 主键id */
  private Long id;

  /** 档案id */
  private Long archiveId;

  /** 附件名字 */
  private String attachmentName;

  /** 附件路径 */
  private String attachmentPath;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
