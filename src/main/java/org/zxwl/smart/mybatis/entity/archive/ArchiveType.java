package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案门类表 */
@Data
public class ArchiveType implements Serializable {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /** 父类id */
  private Long pid;

  /** 门类类型，archive档案级，box盒级 */
  private String type;

  /** 门类名称 */
  private String typeName;

  /** 门类别名 */
  private String otherName;

  /** 是否支持手动上架，0不支持，1支持 */
  private Boolean supportManual;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
