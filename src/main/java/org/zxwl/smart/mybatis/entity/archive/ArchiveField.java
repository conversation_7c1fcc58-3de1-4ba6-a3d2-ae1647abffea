package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案字段表 */
@Data
public class ArchiveField implements Serializable {
  /** 主键id */
  private Long id;

  /** 档案门类id */
  private Long archiveTypeId;

  /** 字段类型，string字符串，image图片，file文件 */
  private String fieldType;

  /** 固定列名，archiveName档案名字，archiveNo档案编号 */
  private String fixedFieldName;

  /** 字段名字 */
  private String fieldName;

  /** 字段位置，越小越靠前 */
  private Integer fieldOrdinal;

  /** 字段列宽度 */
  private Integer fieldWidth;

  /** 是否列表展示，0不展示，1展示 */
  private Boolean showList;

  /** 是否支持录入，0不支持，1支持 */
  private Boolean input;

  /** 是否是必填，0不必填，1必填 */
  private Boolean required;

  /** 是否支持搜索，0不支持，1支持 */
  private Boolean search;

  /** 是否支持高级搜索，0不支持，1支持 */
  private Boolean adSearch;

  /** 备注 */
  private String remark;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
