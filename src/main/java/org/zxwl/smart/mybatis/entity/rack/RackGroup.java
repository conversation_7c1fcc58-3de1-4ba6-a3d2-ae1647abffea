package org.zxwl.smart.mybatis.entity.rack;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 密集架组 */
@Data
public class RackGroup implements Serializable {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /** 所属库房id */
  private Long warehouseId;

  /** 密集架类型，普通密集架，智能密集架，盒定位密集架，发光密集架 */
  private String rackType;

  /** 密集架厂商，知行，天骄，方德... */
  private String rackVendor;

  /** 列数 */
  private Integer columnNum;

  /** 面数 */
  private Integer panelNum;

  /** 节数 */
  private Integer sectionNum;

  /** 层数 */
  private Integer layerNum;

  /** 格数 */
  private Integer gridNum;

  /** 组编号 */
  private Integer groupNo;

  /** 架组名 */
  private String groupName;

  /** 固定列号 */
  private Integer fixedColumnNo;

  /** 架体ip */
  private String controlIp;

  /** 架体端口 */
  private Integer controlPort;

  /** 监控ip */
  private String monitorIp;

  /** 监控端口 */
  private Integer monitorPort;

  /** 容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 备注 */
  private String remark;

  /** 地图配置信息 */
  private String visualConfig;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
