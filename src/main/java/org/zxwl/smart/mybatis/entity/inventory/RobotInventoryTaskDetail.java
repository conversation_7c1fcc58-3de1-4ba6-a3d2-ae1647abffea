package org.zxwl.smart.mybatis.entity.inventory;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 机器人盘点任务详情表 */
@Data
public class RobotInventoryTaskDetail implements Serializable {
  /** 主键id */
  private Long id;

  /** 机器人盘点任务id */
  private Long robotInventoryTaskId;

  /** 密集架组id */
  private Long rackGroupId;

  /** 密集架列编号 */
  private Long rackColumNo;

  /** 密集架面编号 */
  private Long rackPanelNo;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
