package org.zxwl.smart.mybatis.entity.sys;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class Role implements Serializable {

  /** 角色id */
  private Long id;

  /** 角色名称 */
  private String roleName;

  /** 角色描述 */
  private String description;

  /** 系统内置角色标识，0否，1是 */
  private Boolean systemFlag;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
