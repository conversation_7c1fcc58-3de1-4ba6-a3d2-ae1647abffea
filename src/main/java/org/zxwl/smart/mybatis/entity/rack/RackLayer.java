package org.zxwl.smart.mybatis.entity.rack;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 密集架层位表 */
@Data
public class RackLayer implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属架组id */
  private Long rackGroupId;

  /** 层位关联门类id */
  private Long archiveTypeId;

  /** 列号 */
  private Integer columnNo;

  /** 面号 */
  private Integer panelNo;

  /** 节号 */
  private Integer sectionNo;

  /** 层号 */
  private Integer layerNo;

  /** 层标TID */
  private String layerTid;

  /** 容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 在架数 */
  private Integer onCount;

  /** 错架数 */
  private Integer wrongCount;

  /** 盘点时间 */
  private Date inventoryAt;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
