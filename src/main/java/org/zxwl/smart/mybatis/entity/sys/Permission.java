package org.zxwl.smart.mybatis.entity.sys;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 权限实体类 */
@Data
public class Permission implements Serializable {

  /** 权限ID */
  private Long id;

  /** 权限名称 */
  private String permissionName;

  /** 权限编码 */
  private String permissionCode;

  /** 序号 */
  private Integer ordinal;

  /** 父节点名称 */
  private String parentName;

  /** 父节点序号 */
  private Integer parentOrdinal;

  /** 创建人ID */
  private Long createdId;

  /** 更新人ID */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
