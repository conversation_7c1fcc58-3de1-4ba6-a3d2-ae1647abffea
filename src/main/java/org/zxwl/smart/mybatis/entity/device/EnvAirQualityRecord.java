package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 环控空气质量历史记录表 */
@Data
public class EnvAirQualityRecord implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属库房id */
  private Long warehouseId;

  /** 所属区域id */
  private Long areaId;

  /** 设备id */
  private Long deviceId;

  /** 温度 */
  private Float temperature;

  /** 湿度 */
  private Float humidity;

  /** PM2.5 */
  private Float pm25;

  /** PM10 */
  private Float pm10;

  /** TVOC */
  private Float tvoc;

  /** 二氧化碳 */
  private Float co2;

  /** 甲醛 */
  private Float hcho;

  /** 臭氧 */
  private Float o3;

  /** 光照度 (单位：lux) */
  private Float light;

  /** 二氧化氮 (单位：ppm) */
  private Float no2;

  /** 二氧化硫 (单位：ppm) */
  private Float so2;

  /** 创建时间 */
  private Date createdAt;

  private static final long serialVersionUID = 1L;
}
