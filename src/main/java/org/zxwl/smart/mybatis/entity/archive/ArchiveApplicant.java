package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案取档申请表 */
@Data
public class ArchiveApplicant implements Serializable {
  /** 主键id */
  private Long id;

  /** 档案id */
  private Long archiveId;

  /** 状态，pending待审批，approved审批通过，reject审批拒绝，revoke已撤销 */
  private String status;

  /** 借阅目的，internal内部查阅，external外部查阅，transfer移交，outbound出库 */
  private String borrowIntent;

  /** 申请人姓名 */
  private String applicantName;

  /** 申请人手机号，用于催还 */
  private String applicantPhone;

  /** 申请人邮箱 */
  private String applicantEmail;

  /** 借阅天数 */
  private Integer borrowDays;

  /** 借阅时间 */
  private Date borrowAt;

  /** 经办人id */
  private Long operatorId;

  /** 备注 */
  private String remark;

  /** 审批时间 */
  private Date approvalAt;

  /** 应归还时间，审批通过后该字段才会有值 */
  private Date needBackAt;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
