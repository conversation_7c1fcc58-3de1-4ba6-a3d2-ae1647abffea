package org.zxwl.smart.mybatis.entity.rack;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 密集架格口表 */
@Data
public class RackGrid implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属架组id */
  private Long rackGroupId;

  /** 密集架层id */
  private Long rackLayerId;

  /** 格子号/格子编号 */
  private Integer gridNo;

  /** 容量 */
  private Integer capacity;

  /** 已用容量 */
  private Integer usedCapacity;

  /** 创建人id */
  private Long createdId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
