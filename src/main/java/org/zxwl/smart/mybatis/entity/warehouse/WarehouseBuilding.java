package org.zxwl.smart.mybatis.entity.warehouse;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 库房建筑表 */
@Data
public class WarehouseBuilding implements Serializable {
  /** 主键id */
  private Long id;

  /** 建筑编号 */
  private Integer buildingNo;

  /** 建筑名 */
  private String buildingName;

  /** 省、市、区的完整地址描述（如：省-市-区） */
  private String location;

  /** 地图配置信息 */
  private String visualConfig;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
