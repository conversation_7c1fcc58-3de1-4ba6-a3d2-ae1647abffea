package org.zxwl.smart.mybatis.entity.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 防盗门人数统计表 */
@Data
public class EasDevicePersonNum implements Serializable {
  /** 主键id */
  private Long id;

  /** 防盗门ip */
  private String deviceIp;

  /** 总进入人数 */
  private Integer totalInNum;

  /** 总离开人数 */
  private Integer totalOutNum;

  /** 统计日期 */
  private Date dateAt;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  private static final long serialVersionUID = 1L;
}
