package org.zxwl.smart.mybatis.entity.policy;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 智能策略表 */
@Data
public class Policy implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属库房id */
  private Long warehouseId;

  /** 所属区域id */
  private Long areaId;

  /** 是否是系统自动生成，0否，1是 */
  private Boolean system;

  /** 是否启用，0否，1是 */
  private Boolean enabled;

  /** 策略类型 */
  private String policyType;

  /** 策略名称 */
  private String policyName;

  /** 策略描述 */
  private String policyDescription;

  /** 策略条件，JSON字符串 */
  private String policyCondition;

  /** 策略执行周期，JSON字符串 */
  private String policyPeriod;

  /** 策略动作，JSON字符串 */
  private String policyAction;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
