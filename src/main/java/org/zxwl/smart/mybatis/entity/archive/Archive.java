package org.zxwl.smart.mybatis.entity.archive;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 档案表 */
@Data
public class Archive implements Serializable {
  /** 主键id */
  private Long id;

  /** 机构id */
  private Long organizationId;

  /** 所属库房id */
  private Long warehouseId;

  /** 档案门类id */
  private Long archiveTypeId;

  /** 上层id（盒id或档案id） */
  private Long parentId;

  /** 类型，box档案盒，archive档案，volume卷内档案 */
  private String type;

  /** 在库状态，in在库，out不在库 */
  private String stockStatus;

  /** 在架状态，pending_in待上架，in在架，pending_out待下架，out不在架 */
  private String shelvingStatus;

  /** 名字 */
  private String archiveName;

  /** 编号 */
  private String archiveNo;

  /** 标签TID */
  private String tid;

  /** 档案所在密集架组号 */
  private Integer rackGroupNo;

  /** 档案所在密集架列号 */
  private Integer rackColumnNo;

  /** 档案所在密集架面号 */
  private Integer rackPanelNo;

  /** 档案所在密集架节号 */
  private Integer rackSectionNo;

  /** 档案所在密集架层号 */
  private Integer rackLayerNo;

  /** 档案所在密集架格号 */
  private Integer rackGridNo;

  /** 档案所在人事柜组号 */
  private Integer hrCabinetGroupNo;

  /** 档案所在人事柜号 */
  private Integer hrCabinetNo;

  /** 档案所在人事柜层号 */
  private Integer hrCabinetLayerNo;

  /** 档案所在人事柜格号 */
  private Integer hrCabinetGridNo;

  /** 档案所在柜组号 */
  private Integer cabinetGroupNo;

  /** 档案所在柜号 */
  private Integer cabinetNo;

  /** 档案所在柜格号 */
  private Integer cabinetGridNo;

  /** 上架人id */
  private Long shelvingId;

  /** 上架时间 */
  private Date shelvingAt;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
