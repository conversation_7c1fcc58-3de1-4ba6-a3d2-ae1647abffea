package org.zxwl.smart.mybatis.entity.sys;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 菜单 */
@Data
public class SysMenu implements Serializable {

  /** 主键id */
  private Long id;

  /** 菜单名 */
  private String menuName;

  /** 菜单编码，用来区分菜单的唯一标识 */
  private String menuCode;

  /** 父菜单id */
  private Long parentId;

  /** 菜单图标地址 */
  private String menuIcon;

  /** 菜单状态，0禁用，1启用 */
  private Boolean menuStatus;

  /** 菜单路径 */
  private String menuPath;

  /** 菜单路径匹配规则 */
  private String menuPathPattern;

  /** 序号，用来指定菜单的顺序 */
  private Integer ordinal;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
