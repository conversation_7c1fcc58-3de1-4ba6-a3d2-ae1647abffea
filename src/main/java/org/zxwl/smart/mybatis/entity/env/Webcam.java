package org.zxwl.smart.mybatis.entity.env;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/** 网络摄像头表 */
@Data
public class Webcam implements Serializable {
  /** 主键id */
  private Long id;

  /** 所属库房id */
  private Long warehouseId;

  /** 设备名称 */
  private String deviceName;

  /** 拉流地址 */
  private String streamUrl;

  /** 地图配置信息 */
  private String visualConfig;

  /** 创建人id */
  private Long createdId;

  /** 更新人id */
  private Long updatedId;

  /** 创建时间 */
  private Date createdAt;

  /** 更新时间 */
  private Date updatedAt;

  /** 删除时间戳，标记是否被逻辑删除 */
  private Long deleteAt;

  private static final long serialVersionUID = 1L;
}
