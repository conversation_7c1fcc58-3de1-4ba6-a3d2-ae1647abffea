package org.zxwl.smart.mybatis.mapper.archive;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveField;
import java.util.List;

@Mapper
public interface ArchiveFieldMapper {
  List<ArchiveField> selectListByArchiveTypeId(@Param("archiveTypeId") Long archiveTypeId);

  ArchiveField selectById(@Param("id") Long id);

  int countByArchiveTypeId(@Param("archiveTypeId") Long archiveTypeId);

  int insert(ArchiveField archiveField);

  int insertList(List<ArchiveField> list);

  int updateById(ArchiveField archiveField);

  int deleteById(@Param("id") Long id, @Param("deleteAt") Long deleteAt);

  int deleteByArchiveTypeId(
      @Param("archiveTypeId") Long archiveTypeId, @Param("deleteAt") Long deleteAt);
}
