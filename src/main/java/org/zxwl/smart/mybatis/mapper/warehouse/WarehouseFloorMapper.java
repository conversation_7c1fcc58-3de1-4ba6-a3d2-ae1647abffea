package org.zxwl.smart.mybatis.mapper.warehouse;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.warehouse.WarehouseFloor;
import java.util.List;

@Mapper
public interface WarehouseFloorMapper {

  List<WarehouseFloor> selectList(@Param("warehouseBuildingId") Long warehouseBuildingId);

  List<WarehouseFloor> selectNoAndNameList(@Param("idList") List<Long> idList);

  WarehouseFloor selectById(@Param("id") Long id);

  int existByBuildingId(@Param("warehouseBuildingId") Long warehouseBuildingId);

  int existByBuildingIdAndFloorNo(
      @Param("warehouseBuildingId") Long warehouseBuildingId, @Param("floorNo") Integer floorNo);

  int existById(@Param("id") Long id);

  int count();

  int insert(WarehouseFloor floor);

  int updateById(WarehouseFloor floor);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
