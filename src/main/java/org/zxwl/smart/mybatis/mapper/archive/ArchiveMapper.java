package org.zxwl.smart.mybatis.mapper.archive;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.archive.ArchiveTask;
import org.zxwl.smart.mybatis.ext.archive.*;

@Mapper
public interface ArchiveMapper {
  List<Archive> selectList(ArchiveListWrapper wrapper);

  List<ArchiveIdWithApplicantIdExt> selectPendingOutListByAccountLocal(
      ArchivePendingOutListWrapper wrapper);

  List<Archive> selectListForDeleted(ArchiveDeletedListWrapper wrapper);

  List<Archive> selectListByCabinetGroupNo(
      @Param("shelvingStatus") List<String> shelvingStatusList,
      @Param("cabinetGroupNo") Integer cabinetGroupNo);

  List<Archive> selectListByHrCabinetGroupNo(
      @Param("shelvingStatus") List<String> shelvingStatusList,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo);

  List<Archive> selectListByRackGroupNo(
      @Param("shelvingStatus") List<String> shelvingStatusList,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("rackColumnNo") Integer rackColumnNo);

  List<Archive> selectListByIdList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  List<Archive> selectNameAndNoListByIdList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  List<Archive> selectListByParentId(@Param("parentId") Long parentId);

  List<Archive> selectListByParentIdList(@Param("parentIdList") List<Long> parentIdList);

  List<Archive> selectListByTidList(@Param("tidList") List<String> tidList);

  List<Archive> selectIdListByParentIdList(@Param("parentIdList") List<Long> parentIdList);

  List<ArchiveSubCountExt> selectSubArchiveCountList(
      @Param("parentIdList") List<Long> parentIdList);

  List<ArchiveTypeStat> selectArchiveTypeStat(
      @Param("warehouseId") Long warehouseId,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo);

  List<ArchiveStockStatusStat> selectStockStatusStat(
      @Param("warehouseId") Long warehouseId,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo);

  List<ArchiveShelvingStatusStat> selectShelvingStatusStat(
      @Param("warehouseId") Long warehouseId,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo);

  ArchiveStatExt selectStat(
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo);

  Archive selectByIdOrTid(
      @Param("id") Long id,
      @Param("tid") String tid,
      @Param("containDelete") boolean containDelete);

  List<Archive> selectListByGroupNoForStatTask(
      @Param("shelvingStatusList") List<String> shelvingStatusList,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo);

  List<Archive> selectArchivePendingStorageList(ArchivePendingStorageListWrapper wrapper);

  List<ArchiveTask> selectArchiveLastCompletedOutTaskList(
      @Param("archivePendingStorageIdList") List<Long> archivePendingStorageIdList);

  List<ArchivePendingStorageExt> selectArchivePendingStorageExtList(
      @Param("archiveTaskIdList") List<Long> archiveTaskIdList);

  int existArchiveTypeIdAndArchiveNo(
      @Param("archiveTypeId") Long archiveTypeId, @Param("archiveNo") String archiveNo);

  int existByArchiveTypeId(@Param("archiveTypeId") Long archiveTypeId);

  int existByOrganizationId(Long organizationId);

  int countByWarehouseId(@Param("warehouseId") Long warehouseId);

  int countByParentId(@Param("parentId") Long parentId);

  int insert(Archive archive);

  int updateById(Archive archive);

  int updateTidById(@Param("id") Long id, @Param("tid") String tid);

  int updateTidByIdList(@Param("idList") List<Long> idList, @Param("tid") String tid);

  int updateStockStatusByIdList(
      @Param("idList") List<Long> idList,
      @Param("warehouseId") Long warehouseId,
      @Param("stockStatus") String stockStatus,
      @Param("updatedId") Long updatedId);

  int updateStatusByIdList(
      @Param("idList") List<Long> idList,
      @Param("stockStatus") String stockStatus,
      @Param("shelvingStatus") String shelvingStatus,
      @Param("updatedId") Long updatedId);

  int updateListForLocation(List<Archive> list);

  int updateListForShelving(List<Archive> list);

  int updateListForBoxing(List<Archive> list);

  int updateParentIdByIdList(
      @Param("idList") List<Long> idList,
      @Param("parentId") Long parentId,
      @Param("updatedId") Long updatedId);

  int updateDeleteAtByIdList(@Param("idList") List<Long> idList, @Param("deleteAt") Long deleteAt);

  void deletePhysicallyByIdList(@Param("idList") List<Long> idList);
}
