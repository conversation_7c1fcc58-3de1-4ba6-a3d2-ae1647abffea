package org.zxwl.smart.mybatis.mapper.warehouse;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.warehouse.WarehouseBuilding;

@Mapper
public interface WarehouseBuildingMapper {

  List<WarehouseBuilding> selectList();

  List<WarehouseBuilding> selectNoAndNameList(@Param("idList") List<Long> idList);

  WarehouseBuilding selectById(@Param("id") Long id);

  int existByIdOrBuildingNo(@Param("id") Long id, @Param("buildingNo") Integer buildingNo);

  int count();

  int insert(WarehouseBuilding building);

  int updateById(WarehouseBuilding building);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
