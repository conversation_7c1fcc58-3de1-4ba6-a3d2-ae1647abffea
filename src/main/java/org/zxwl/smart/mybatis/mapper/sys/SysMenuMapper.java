package org.zxwl.smart.mybatis.mapper.sys;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.SysMenu;

@Mapper
public interface SysMenuMapper {

  List<SysMenu> selectList();

  List<Long> selectByIdList(@Param("idList") List<Long> idList);

  List<SysMenu> selectListByIdList(@Param("idList") List<Long> idList);

  SysMenu selectById(@Param("id") Long id);

  List<SysMenu> selectByParentId(@Param("parentId") Long parentId);

  int insert(SysMenu menu);

  int update(SysMenu menu);

  int deleteById(@Param("id") Long id, @Param("deleteAt") Long deleteAt);
}
