package org.zxwl.smart.mybatis.mapper.cabinet;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.cabinet.HrCabinetLayer;

@Mapper
public interface HrCabinetLayerMapper {
  List<HrCabinetLayer> selectByCabinetGroupIdList(
      @Param("cabinetGroupIdList") List<Long> cabinetGroupIdList);

  List<HrCabinetLayer> selectListByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);

  List<HrCabinetLayer> selectListByCabinetGroupIdForStatTask(
      @Param("cabinetGroupId") Long cabinetGroupId);

  int insertList(List<HrCabinetLayer> cabinetLayerList);

  int updateUsedCapacityForStatTask(@Param("list") List<HrCabinetLayer> list);

  int deleteByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);
}
