package org.zxwl.smart.mybatis.mapper.user;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.user.Organization;

@Mapper
public interface OrganizationMapper {
  List<Organization> selectList();

  List<Organization> selectListByUserId(@Param("userId") Long userId);

  List<Organization> selectByIdList(@Param("idList") List<Long> idList);

  Organization selectFirst();

  Organization selectById(Long id);

  int existByOrganizationNo(@Param("organizationNo") String organizationNo);

  int existByOrganizationName(@Param("organizationName") String organizationName);

  int exitByParentId(@Param("parentId") Long parentId);

  int count();

  int insert(Organization organization);

  int updateById(Organization organization);

  int deleteById(@Param("id") Long id, @Param("deleteAt") Long deleteAt);
}
