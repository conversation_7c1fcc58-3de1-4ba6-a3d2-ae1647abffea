package org.zxwl.smart.mybatis.mapper.device;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;

@Mapper
public interface EnvDeviceMapper {
  List<EnvDevice> selectList(
      @Param("warehouseId") Long warehouseId,
      @Param("idList") List<Long> idList,
      @Param("deviceType") String deviceType,
      @Param("deviceNo") String deviceNo,
      @Param("deviceName") String deviceName);

  List<EnvDevice> selectListByIdList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  EnvDevice selectById(@Param("id") Long id);

  EnvDevice selectByDeviceNo(@Param("deviceNo") String deviceNo);

  List<EnvDevice> selectListByDeviceType(@Param("deviceType") String deviceType);

  int existByDeviceNo(String deviceNo);

  int insert(EnvDevice device);

  int updateById(EnvDevice device);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
