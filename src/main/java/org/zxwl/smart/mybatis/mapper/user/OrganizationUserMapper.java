package org.zxwl.smart.mybatis.mapper.user;

import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.user.OrganizationUser;

@Mapper
public interface OrganizationUserMapper {
  Set<Long> selectOrganizationIdSetByUserId(@Param("userId") Long userId);

  int existByOrganizationIdAndUserId(
      @Param("organizationId") Long organizationId, @Param("userId") Long userId);

  int insertBatch(@Param("organizationUserList") List<OrganizationUser> organizationUserList);

  int deleteByUserId(@Param("userId") Long userId);
}
