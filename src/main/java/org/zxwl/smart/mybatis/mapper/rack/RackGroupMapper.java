package org.zxwl.smart.mybatis.mapper.rack;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;

@Mapper
public interface RackGroupMapper {
  List<RackGroup> selectList(
      @Param("organizationId") Long organizationId,
      @Param("warehouseId") Long warehouseId,
      @Param("rackType") String rackType,
      @Param("rackVendor") String rackVendor);

  List<RackGroup> selectGroupNoList(@Param("idList") List<Long> idList);

  List<RackGroup> selectListForStatTask(@Param("groupNo") Integer groupNo);

  List<RackGroup> selectListByWarehouseId(
      @Param("warehouseId") Long warehouseId, @Param("groupNo") Integer groupNo);

  List<RackGroup> selectListByGroupNoList(@Param("groupNoList") List<Integer> groupNoList);

  RackGroup selectById(@Param("id") Long id);

  RackGroup selectByGroupNo(@Param("groupNo") Integer groupNo);

  int existByGroupNo(@Param("groupNo") Integer groupNo);

  int existByOrganizationId(@Param("organizationId") Long organizationId);

  int existByWarehouseId(@Param("warehouseId") Long warehouseId);

  int maxGroupNo();

  int insert(RackGroup rackGroup);

  int updateById(RackGroup rackGroup);

  int updateUsedCapacityForStatTask(@Param("list") List<RackGroup> list);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
