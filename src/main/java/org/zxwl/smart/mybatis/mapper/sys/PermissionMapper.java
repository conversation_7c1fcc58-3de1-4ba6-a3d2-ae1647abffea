package org.zxwl.smart.mybatis.mapper.sys;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.Permission;

@Mapper
public interface PermissionMapper {

  int selectByPermissionCode(@Param("permissionCode") String permissionCode);

  List<Permission> selectList();

  List<Long> selectByIdList(@Param("idList") List<Long> idList);

  List<Permission> selectListByIdList(@Param("idList") List<Long> idList);

  List<Permission> selectPermissionList(
      @Param("userId") Long userId, @Param("organizationId") Long organizationId);

  List<String> selectPermissionCodeList(
      @Param("userId") Long userId, @Param("organizationId") Long organizationId);

  int insert(Permission permission);
}
