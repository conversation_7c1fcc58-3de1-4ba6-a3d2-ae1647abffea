package org.zxwl.smart.mybatis.mapper.sys;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.SysConfiguration;
import org.zxwl.smart.mybatis.ext.config.ConfigListWrapper;
import java.util.List;

@Mapper
public interface SysConfigurationMapper {

  int insert(SysConfiguration record);

  List<SysConfiguration> selectList(ConfigListWrapper wrapper);

  int existById(Long id);

  int updateById(SysConfiguration sysConfiguration);

  int updateOtherConfigsToHistory(
      @Param("configId") Long configId,
      @Param("configType") String configType,
      @Param("terminalCode") String terminalCode,
      @Param("userId") Long userId);

  int deleteByIdList(@Param("idList") List<Long> idList, @Param("deleteAt") long deleteAt);
}
