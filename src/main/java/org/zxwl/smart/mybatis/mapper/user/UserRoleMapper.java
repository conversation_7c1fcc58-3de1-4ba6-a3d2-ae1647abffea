package org.zxwl.smart.mybatis.mapper.user;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.UserRole;

@Mapper
public interface UserRoleMapper {

  List<UserRole> selectByRoleId(@Param("roleId") Long roleId);

  List<Long> selectRoleIdListByUserIdAndOrgId(
      @Param("userId") Long userId, @Param("organizationId") Long organizationId);

  int insertBatch(@Param("userRoleList") List<UserRole> userRoleList);

  int deleteByUserIdAndOrgId(
      @Param("userId") Long userId, @Param("organizationId") Long organizationId);
}
