package org.zxwl.smart.mybatis.mapper.archive;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveFieldValue;

@Mapper
public interface ArchiveFieldValueMapper {

  List<ArchiveFieldValue> selectListByArchiveIdList(
      @Param("archiveIdList") List<Long> archiveIdList);

  List<ArchiveFieldValue> selectListByArchiveId(@Param("archiveId") Long archiveId);

  int insertList(List<ArchiveFieldValue> archiveFieldValueList);

  int deleteByArchiveId(@Param("archiveId") Long archiveId);
}
