package org.zxwl.smart.mybatis.mapper.inventory;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.inventory.InventoryDetail;
import java.util.List;

@Mapper
public interface InventoryDetailMapper {
  List<InventoryDetail> selectList(@Param("inventoryRecordId") Long inventoryRecordId);

  int insertList(List<InventoryDetail> list);
}
