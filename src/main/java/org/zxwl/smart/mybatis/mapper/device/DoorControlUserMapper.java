package org.zxwl.smart.mybatis.mapper.device;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.device.DoorControlUser;

@Mapper
public interface DoorControlUserMapper {
  DoorControlUser selectById(@Param("id") Long id);

  Long selectIdBySnAndIdCard(@Param("deviceNo") String deviceNo, @Param("idCard") String idCard);

  List<DoorControlUser> selectList(
      @Param("deviceNo") String deviceNo, @Param("userName") String userName);

  DoorControlUser selectByDeviceNoAndIdCard(
      @Param("deviceNo") String deviceNo, @Param("idCard") String idCard);

  List<DoorControlUser> selectByIdList(@Param("idList") List<Long> idList);

  int existByIdCard(@Param("idCard") String idCard);

  int insert(DoorControlUser user);

  int updateUser(DoorControlUser user);

  int updateUserInfo(
      @Param("userName") String name,
      @Param("pri") String pri,
      @Param("userPwd") String passwd,
      @Param("card") String card,
      @Param("userGroup") String grp,
      @Param("tz") String tz,
      @Param("verifyType") String verify,
      @Param("viceCard") String viceCard,
      @Param("startDateTime") String startDatetime,
      @Param("endDateTime") String endDatetime,
      @Param("id") Long id);

  int updateUserFingerIdById(DoorControlUser user);

  int updateUserFaceIdById(DoorControlUser user);

  int deleteById(DoorControlUser user);

  int deleteByIdList(
      @Param("idList") List<Long> idList,
      @Param("userStatus") String userStatus,
      @Param("deleteAt") Long deleteAt);
}
