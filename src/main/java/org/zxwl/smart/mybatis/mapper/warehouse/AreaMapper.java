package org.zxwl.smart.mybatis.mapper.warehouse;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.warehouse.Area;

@Mapper
public interface AreaMapper {

  List<Area> selectList(@Param("warehouseId") Long warehouseId);

  List<Area> selectNameList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  Area selectById(@Param("id") Long id);

  int insert(Area area);

  int updateById(Area area);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
