package org.zxwl.smart.mybatis.mapper.env;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.env.Webcam;

@Mapper
public interface WebcamMapper {

  List<Webcam> selectList(
      @Param("warehouseId") Long warehouseId,
      @Param("includePublic") Boolean includePublic,
      @Param("deviceNameLike") String deviceNameLike);

  Webcam selectById(Long id);

  int insert(Webcam webcam);

  int updateById(Webcam webcam);

  int deleteById(Long id);
}
