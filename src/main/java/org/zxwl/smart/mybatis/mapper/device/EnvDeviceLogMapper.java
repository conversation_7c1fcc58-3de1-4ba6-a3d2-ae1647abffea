package org.zxwl.smart.mybatis.mapper.device;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.device.EnvDeviceLog;

@Mapper
public interface EnvDeviceLogMapper {

  EnvDeviceLog selectById(@Param("logId") Long logId);

  List<EnvDeviceLog> selectList(
      @Param("warehouseId") Long warehouseId,
      @Param("deviceIdList") List<Long> deviceIdList,
      @Param("deviceId") Long deviceId,
      @Param("createdId") Long createdId,
      @Param("createdAtBegin") Date createdAtBegin,
      @Param("createdAtEnd") Date createdAtEnd);

  int insert(EnvDeviceLog record);

  int insertList(List<EnvDeviceLog> list);

  int deleteByIdList(@Param("idList") List<Long> idList);
}
