package org.zxwl.smart.mybatis.mapper.sys;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.RolePermission;

@Mapper
public interface RolePermissionMapper {

  List<Long> selectPermissionIdListByRoleId(@Param("roleId") Long roleId);

  int insertBatch(@Param("rolePermissionList") List<RolePermission> rolePermissionList);

  int deleteByRoleId(@Param("roleId") Long roleId);

  int deleteByRoleIdList(@Param("roleIdList") List<Long> idList);
}
