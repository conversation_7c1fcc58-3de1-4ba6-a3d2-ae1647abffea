package org.zxwl.smart.mybatis.mapper.inventory;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.inventory.InventoryRecord;

@Mapper
public interface InventoryRecordMapper {
  List<InventoryRecord> selectList(
      @Param("organizationId") Long organizationId,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo,
      @Param("beginCreateAt") Date beginCreateAt,
      @Param("endCreateAt") Date endCreateAt);

  InventoryRecord selectById(@Param("id") Long id);

  int insert(InventoryRecord record);
}
