package org.zxwl.smart.mybatis.mapper.cabinet;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.cabinet.Cabinet;

@Mapper
public interface CabinetMapper {
  List<Cabinet> selectListByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);

  List<Cabinet> selectByCabinetGroupIdList(
      @Param("cabinetGroupIdList") List<Long> cabinetGroupIdList);

  List<Cabinet> selectListByCabinetGroupIdForStatTask(@Param("cabinetGroupId") Long cabinetGroupId);

  int insertList(List<Cabinet> cabinetList);

  int updateUsedCapacityForStatTask(List<Cabinet> list);

  int deleteByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);
}
