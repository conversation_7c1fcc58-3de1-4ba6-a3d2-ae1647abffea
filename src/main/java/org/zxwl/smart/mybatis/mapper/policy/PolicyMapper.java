package org.zxwl.smart.mybatis.mapper.policy;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.policy.Policy;

@Mapper
public interface PolicyMapper {
  List<Policy> selectList(@Param("warehouseId") Long warehouseId, @Param("areaId") Long areaId);

  List<Policy> selectListByAreaId(@Param("areaId") Long areaId);

  Policy selectById(@Param("id") Long id);

  int insert(Policy policy);

  int insertList(List<Policy> list);

  int updateById(Policy policy);

  int updateEnabledById(
      @Param("id") Long id, @Param("enabled") Boolean enabled, @Param("updatedId") Long updatedId);

  int updateList(@Param("list") List<Policy> list);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);

  int deleteByIdList(@Param("idList") List<Long> idList, @Param("deleteAt") long deleteAt);
}
