package org.zxwl.smart.mybatis.mapper.archive;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveApproval;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApprovalExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApprovalWrapper;

@Mapper
public interface ArchiveApprovalMapper {

  List<ArchiveApprovalExt> selectList(ArchiveApprovalWrapper wrapper);

  ArchiveApproval selectByArchiveApplicantId(
      @Param("archiveApplicantId") Long archiveApplicantId,
      @Param("containDelete") boolean containDelete);

  ArchiveApproval selectById(@Param("id") Long id);

  int insertList(List<ArchiveApproval> approvalList);
}
