package org.zxwl.smart.mybatis.mapper.cabinet;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGrid;
import org.zxwl.smart.mybatis.ext.cabinet.HrCabinetGridLocationExt;
import org.zxwl.smart.mybatis.ext.cabinet.HrCabinetGridWrapper;

@Mapper
public interface HrCabinetGridMapper {
  List<HrCabinetGrid> selectListByCabinetLayerIdList(
      @Param("cabinetLayerIdList") List<Long> cabinetLayerIdList);

  List<HrCabinetGrid> selectListByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);

  List<HrCabinetGridLocationExt> selectLocationList(
      @Param("wrapperList") List<HrCabinetGridWrapper> wrapperList);

  List<HrCabinetGrid> selectListByCabinetGroupIdForStatTask(Long cabinetGroupId);

  int insertList(List<HrCabinetGrid> cabinetGridList);

  int updateUsedCapacityForStatTask(@Param("list") List<HrCabinetGrid> list);

  int deleteByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);
}
