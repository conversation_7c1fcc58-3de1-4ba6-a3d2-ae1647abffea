package org.zxwl.smart.mybatis.mapper.device;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.ext.env.DoorControlPassTimeExt;
import org.zxwl.smart.mybatis.entity.device.DoorControlRecord;

@Mapper
public interface DoorControlRecordMapper {

  List<DoorControlRecord> selectList(
      @Param("warehouseId") Long warehouseId,
      @Param("deviceNoList") List<String> deviceNoList,
      @Param("createdAtBegin") Date createdAtBegin,
      @Param("createdAtEnd") Date createdAtEnd);

  long selectCount(
      @Param("warehouseId") Long warehouseId,
      @Param("createdAtBegin") Date createdAtBegin,
      @Param("createdAtEnd") Date createdAtEnd);

  long selectCountByDeviceNo(
      @Param("deviceNo") String deviceNo,
      @Param("createdAtBegin") Date createdAtBegin,
      @Param("createdAtEnd") Date createdAtEnd);

  List<DoorControlPassTimeExt> selectBatchLastTimeByUsers(
      @Param("deviceNo") String deviceNo, @Param("idCardList") List<String> idCardList);

  int insert(
      @Param("deviceNo") String deviceNo,
      @Param("warehouseId") Long warehouseId,
      @Param("deviceName") String deviceName,
      @Param("controlMethod") String controlMethod,
      @Param("deviceUserId") Long userId,
      @Param("deviceUserName") String deviceUserName,
      @Param("userIdCard") String userIdCard);

  int deleteByIdList(@Param("idList") List<Long> idList);
}
