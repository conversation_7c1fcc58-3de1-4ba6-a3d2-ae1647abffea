package org.zxwl.smart.mybatis.mapper.archive;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveApplicant;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApplicantExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApplicantWrapper;
import org.zxwl.smart.mybatis.ext.archive.PendingApprovalExt;
import org.zxwl.smart.mybatis.ext.archive.PendingApprovalWrapper;

@Mapper
public interface ArchiveApplicantMapper {

  List<ArchiveApplicantExt> selectList(ArchiveApplicantWrapper wrapper);

  List<PendingApprovalExt> selectPendingApprovalList(PendingApprovalWrapper wrapper);

  List<ArchiveApplicant> selectListByIdList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  ArchiveApplicant selectById(@Param("id") Long id, @Param("containDelete") boolean containDelete);

  List<ArchiveApplicant> selectListByArchiveIdList(@Param("archiveIdList") List<Long> archiveIdList, @Param("containDelete") boolean containDelete);

  int insertList(List<ArchiveApplicant> list);

  int updateStatusByIdList(@Param("idList") List<Long> idList, @Param("status") String status);

  int updateListForApproval(List<ArchiveApplicant> list);

  int deleteByIdList(@Param("idList") List<Long> idList, @Param("deleteAt") long deleteAt);
}
