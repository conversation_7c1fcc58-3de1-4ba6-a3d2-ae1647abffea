package org.zxwl.smart.mybatis.mapper.archive;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveAttachment;

@Mapper
public interface ArchiveAttachmentMapper {

  List<ArchiveAttachment> selectListByArchiveId(@Param("archiveId") Long archiveId);

  ArchiveAttachment selectById(@Param("id") Long id);

  int insert(ArchiveAttachment archiveAttachment);

  int updateById(ArchiveAttachment archiveAttachment);

  int deleteById(@Param("id") Long id);
}
