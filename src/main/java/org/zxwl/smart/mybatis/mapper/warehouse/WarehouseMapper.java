package org.zxwl.smart.mybatis.mapper.warehouse;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;

@Mapper
public interface WarehouseMapper {
  List<Warehouse> selectList(
      @Param("organizationId") Long organizationId,
      @Param("idList") List<Long> idList,
      @Param("warehouseFloorId") Long warehouseFloorId);

  List<Warehouse> selectNoAndNameList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  Warehouse selectById(@Param("id") Long id);

  int existByFloorId(@Param("warehouseFloorId") Long warehouseFloorId);

  int existByFloorIdAndWarehouseNo(
      @Param("warehouseFloorId") Long warehouseFloorId, @Param("warehouseNo") Integer warehouseNo);

  int existById(@Param("id") Long id);

  int existByOrganizationId(@Param("organizationId") Long organizationId);

  int count();

  int insert(Warehouse warehouse);

  int updateById(Warehouse warehouse);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
