package org.zxwl.smart.mybatis.mapper.cabinet;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGrid;
import org.zxwl.smart.mybatis.ext.cabinet.CabinetGridLocationExt;
import org.zxwl.smart.mybatis.ext.cabinet.CabinetGridWrapper;
import org.zxwl.smart.mybatis.ext.cabinet.IncCabinetGridUsedCapacityExt;

@Mapper
public interface CabinetGridMapper {
  List<CabinetGrid> selectListByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);

  List<CabinetGrid> selectListByCabinetIdList(@Param("cabinetIdList") List<Long> cabinetIdList);

  List<CabinetGridLocationExt> selectLocationList(
      @Param("wrapperList") List<CabinetGridWrapper> wrapperList);

  List<CabinetGrid> selectListByCabinetGroupIdForStatTask(
      @Param("cabinetGroupId") Long cabinetGroupId);

  CabinetGridLocationExt selectLocation(
      @Param("groupNo") Integer groupNo,
      @Param("cabinetNo") Integer cabinetNo,
      @Param("gridNo") Integer gridNo);

  int insertList(List<CabinetGrid> cabinetGridList);

  int updateUsedCapacityForStatTask(@Param("list") List<CabinetGrid> list);

  int deleteByCabinetGroupId(@Param("cabinetGroupId") Long cabinetGroupId);
}
