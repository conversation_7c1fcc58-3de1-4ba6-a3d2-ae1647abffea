package org.zxwl.smart.mybatis.mapper.device;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.device.EnvEnergyRecord;

@Mapper
public interface EnvEnergyRecordMapper {

  List<EnvEnergyRecord> selectListByWarehouseId(
      @Param("warehouseId") Long warehouseId,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  List<EnvEnergyRecord> selectListByAreaId(
      @Param("areaId") Long areaId,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  List<EnvEnergyRecord> selectListByDeviceId(
      @Param("deviceId") Long deviceId,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  int insert(EnvEnergyRecord record);
}
