package org.zxwl.smart.mybatis.mapper.cabinet;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup;

@Mapper
public interface HrCabinetGroupMapper {
  List<HrCabinetGroup> selectList(
      @Param("organizationId") Long organizationId, @Param("warehouseId") Long warehouseId);

  List<HrCabinetGroup> selectListForStatTask(@Param("groupNo") Integer groupNo);

  List<HrCabinetGroup> selectListByGroupNoList(@Param("groupNoList") List<Integer> groupNoList);

  HrCabinetGroup selectById(@Param("id") Long id);

  HrCabinetGroup selectByGroupNo(@Param("cabinetGroupNo") Integer cabinetGroupNo);

  int existByGroupNo(Integer groupNo);

  int maxGroupNo();

  int insert(HrCabinetGroup cabinetGroup);

  int updateById(HrCabinetGroup cabinetGroup);

  int updateUsedCapacityForStatTask(@Param("list") List<HrCabinetGroup> list);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
