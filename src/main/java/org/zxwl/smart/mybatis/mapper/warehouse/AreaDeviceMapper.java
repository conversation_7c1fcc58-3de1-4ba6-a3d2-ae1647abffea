package org.zxwl.smart.mybatis.mapper.warehouse;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.warehouse.AreaDevice;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;

@Mapper
public interface AreaDeviceMapper {

  List<AreaDevice> selectListByAreaIdList(@Param("areaIdList") List<Long> areaIdList);

  List<Long> selectEnvDeviceIdListByAreaId(@Param("areaId") Long areaId);

  List<EnvDevice> selectEnvDeviceListByAreaId(@Param("areaId") Long areaId);

  int insertList(List<AreaDevice> relationList);

  int deleteByAreaId(@Param("id") Long areaId);
}
