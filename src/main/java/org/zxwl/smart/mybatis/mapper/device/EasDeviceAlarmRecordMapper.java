package org.zxwl.smart.mybatis.mapper.device;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.device.EasDeviceAlarmRecord;

import java.util.List;

@Mapper
public interface EasDeviceAlarmRecordMapper {
  List<EasDeviceAlarmRecord> selectList(
      @Param("archiveNameLike") String archiveNameLike,
      @Param("archiveNoLike") String archiveNoLike,
      @Param("deviceNameLike") String deviceNameLike);

  int insert(EasDeviceAlarmRecord easDeviceAlarmRecord);

  int deleteById(@Param("id") Long id);
}
