package org.zxwl.smart.mybatis.mapper.rack;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.rack.RackGrid;

@Mapper
public interface RackGridMapper {
  List<RackGrid> selectListByRackLayerIdList(@Param("rackLayerIdList") List<Long> rackLayerIdList);

  List<RackGrid> selectListByRackLayerId(@Param("rackLayerId") Long rackLayerId);

  List<RackGrid> selectListByRackGroupId(@Param("rackGroupId") Long rackGroupId);

  List<RackGrid> selectListByRackGroupIdForStatTask(@Param("rackGroupId") Long rackGroupId);

  int insertList(List<RackGrid> rackGridList);

  int updateUsedCapacityForStatTask(@Param("list") List<RackGrid> list);

  int deleteByRackGroupId(@Param("rackGroupId") Long rackGroupId);
}
