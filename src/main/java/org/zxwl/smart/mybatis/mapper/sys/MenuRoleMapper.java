package org.zxwl.smart.mybatis.mapper.sys;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.MenuRole;

@Mapper
public interface MenuRoleMapper {

  List<Long> selectMenuIdListByRoleId(@Param("roleId") Long roleId);

  List<Long> selectMenuIdListByRoleIdList(@Param("roleIdList") List<Long> roleIdList);

  int insertBatch(@Param("menuRoleList") List<MenuRole> menuRoleList);

  int deleteByRoleId(@Param("roleId") Long roleId);
}
