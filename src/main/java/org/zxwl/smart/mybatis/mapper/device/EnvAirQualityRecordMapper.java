package org.zxwl.smart.mybatis.mapper.device;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.device.EnvAirQualityRecord;

@Mapper
public interface EnvAirQualityRecordMapper {

  List<EnvAirQualityRecord> selectListByWarehouseId(
      @Param("warehouseId") Long warehouseId,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  List<EnvAirQualityRecord> selectListByAreaId(
      @Param("areaId") Long areaId,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  List<EnvAirQualityRecord> selectListByDeviceId(
      @Param("deviceId") Long deviceId,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  int insert(EnvAirQualityRecord record);
}
