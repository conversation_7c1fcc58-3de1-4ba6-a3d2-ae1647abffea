package org.zxwl.smart.mybatis.mapper.archive;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchivePendingPickup;
import org.zxwl.smart.mybatis.ext.archive.ArchivePendingPickupExt;

import java.util.List;

@Mapper
public interface ArchivePendingPickupMapper {
  List<ArchivePendingPickupExt> selectList(
      @Param("archiveName") String archiveName,
      @Param("archiveNameLike") String archiveNameLike,
      @Param("archiveNo") String archiveNo,
      @Param("archiveNoLike") String archiveNoLike,
      @Param("createdId") Long createdId);

  List<ArchivePendingPickup> selectListByUserId(@Param("userId") Long userId);

  List<ArchivePendingPickup> selectListByIdList(@Param("idList") List<Long> idList);

  int countByUserId(@Param("userId") Long userId);

  int existByArchiveIdAndUserId(@Param("archiveId") Long archiveId, @Param("userId") Long userId);

  int insertList(List<ArchivePendingPickup> pendingPickupList);

  int deleteByArchiveIdListAndUserId(
      @Param("archiveIdList") List<Long> archiveIdList, @Param("userId") Long userId);

  int deleteByIdList(@Param("idList") List<Long> idList);
}
