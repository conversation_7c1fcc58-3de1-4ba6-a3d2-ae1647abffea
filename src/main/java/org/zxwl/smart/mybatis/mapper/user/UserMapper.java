package org.zxwl.smart.mybatis.mapper.user;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.user.User;
import java.util.Date;
import java.util.List;

@Mapper
public interface UserMapper {
  List<User> selectList(
      @Param("userNameLike") String userNameLike, @Param("updatedAt") Date updatedAt);

  List<User> selectNameListByIdList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  User selectById(@Param("id") Long id);

  User selectNameById(@Param("id") Long id, @Param("containDelete") boolean containDelete);

  User selectByUserAccountOrIdCard(
      @Param("userAccount") String userAccount, @Param("idCard") String idCard);

  int existByUserAccount(@Param("userAccount") String userAccount);

  int existByIdCard(@Param("idCard") String idCard);

  int count();

  int insert(User user);

  int updateById(User user);

  int deleteById(@Param("id") Long id, @Param("deleteAt") Long deleteAt);
}
