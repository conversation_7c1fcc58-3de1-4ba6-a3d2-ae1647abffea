package org.zxwl.smart.mybatis.mapper.sys;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.sys.Role;

@Mapper
public interface RoleMapper {

  Role selectById(@Param("id") Long id);

  List<Role> selectList(@Param("roleNameLike") String roleNameLike);

  List<Role> selectByIdList(@Param("idList") List<Long> idList);

  int selectByRoleName(@Param("roleName") String roleName);

  int insert(Role role);

  int update(Role role);

  int deleteByIdList(@Param("idList") List<Long> idList);
}
