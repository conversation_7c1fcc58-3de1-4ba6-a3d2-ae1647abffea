package org.zxwl.smart.mybatis.mapper.archive;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveOperatorRecord;
import org.zxwl.smart.mybatis.ext.archive.ArchiveOperatorRecordExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveOperatorRecordWrapper;
import org.zxwl.smart.mybatis.ext.archive.OperatorStat;

@Mapper
public interface ArchiveOperatorRecordMapper {
  List<OperatorStat> selectStatByOperatorType(
      @Param("warehouseId") Long warehouseId,
      @Param("rackGroupNo") Integer rackGroupNo,
      @Param("hrCabinetGroupNo") Integer hrCabinetGroupNo,
      @Param("cabinetGroupNo") Integer cabinetGroupNo,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  List<ArchiveOperatorRecord> selectLastArchiveOperatorRecord(
      ArchiveOperatorRecordWrapper recordWrapper);

  List<ArchiveOperatorRecord> selectListByIdList(@Param("recordIdList") List<Long> recordIdList);

  List<ArchiveOperatorRecord> selectList(ArchiveOperatorRecordExt ext);

  List<ArchiveOperatorRecord> selectListForStat(
      @Param("warehouseId") Long warehouseId,
      @Param("operatorTypeList") List<String> operatorTypeList,
      @Param("beginCreatedAt") Date beginCreatedAt,
      @Param("endCreatedAt") Date endCreatedAt);

  int insert(ArchiveOperatorRecord record);

  int insertList(List<ArchiveOperatorRecord> list);
}
