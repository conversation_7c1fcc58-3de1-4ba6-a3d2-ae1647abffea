package org.zxwl.smart.mybatis.mapper.archive;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;

@Mapper
public interface ArchiveTypeMapper {
  List<ArchiveType> selectList(
      @Param("organizationId") Long organizationId, @Param("type") String type);

  List<ArchiveType> selectNameList(
      @Param("idList") List<Long> idList, @Param("containDelete") boolean containDelete);

  ArchiveType selectById(@Param("id") Long id, @Param("containDelete") boolean containDelete);

  int existById(@Param("id") Long id);

  int existTypeName(@Param("typeName") String typeName);

  int existByOrganizationId(@Param("organizationId") Long organizationId);

  int count();

  int insert(ArchiveType archiveType);

  int updateById(ArchiveType archiveType);

  int deleteById(@Param("id") Long id, @Param("deleteAt") Long deleteAt);
}
