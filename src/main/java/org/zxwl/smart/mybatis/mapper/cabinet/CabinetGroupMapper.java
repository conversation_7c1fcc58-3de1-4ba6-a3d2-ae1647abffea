package org.zxwl.smart.mybatis.mapper.cabinet;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGroup;

@Mapper
public interface CabinetGroupMapper {
  List<CabinetGroup> selectList(
      @Param("organizationId") Long organizationId, @Param("warehouseId") Long warehouseId);

  List<CabinetGroup> selectListForStatTask(@Param("groupNo") Integer groupNo);

  List<CabinetGroup> selectListByGroupNoList(@Param("groupNoList") List<Integer> groupNoList);

  CabinetGroup selectById(@Param("id") Long id);

  CabinetGroup selectByGroupNo(@Param("groupNo") Integer groupNo);

  int existByGroupNo(@Param("groupNo") Integer groupNo);

  int existByOrganizationId(@Param("organizationId") Long organizationId);

  int existByWarehouseId(@Param("warehouseId") Long warehouseId);

  int maxGroupNo();

  int insert(CabinetGroup cabinetGroup);

  int updateById(CabinetGroup cabinetGroup);

  int updateUsedCapacityForStatTask(@Param("list") List<CabinetGroup> list);

  int deleteById(@Param("id") Long id, @Param("deleteAt") long deleteAt);
}
