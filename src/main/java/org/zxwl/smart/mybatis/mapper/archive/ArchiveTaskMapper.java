package org.zxwl.smart.mybatis.mapper.archive;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.archive.ArchiveTask;
import org.zxwl.smart.mybatis.ext.archive.ArchiveTaskExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveTaskListWrapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface ArchiveTaskMapper {

  List<ArchiveTaskExt> selectList(ArchiveTaskListWrapper wrapper);

  ArchiveTask selectById(@Param("id") Long id);

  int insertList(List<ArchiveTask> list);

  int updateStatusByArchiveIdListIfExists(
      @Param("archiveIdList") List<Long> archiveIdList, @Param("status") String status);

  int updateCompleteInfoByIdList(@Param("status") String status, @Param("completionTime") Date completionTime, @Param("updatedId") Long updatedId, @Param("taskIdList") List<Long> taskIdList);
}
