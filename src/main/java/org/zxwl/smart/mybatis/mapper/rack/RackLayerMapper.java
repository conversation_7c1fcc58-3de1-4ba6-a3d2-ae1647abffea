package org.zxwl.smart.mybatis.mapper.rack;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.zxwl.smart.mybatis.entity.rack.RackLayer;
import org.zxwl.smart.mybatis.ext.rack.RackGridLocationExt;
import org.zxwl.smart.mybatis.ext.rack.RackGridWrapper;
import org.zxwl.smart.mybatis.ext.rack.RackLayerExt;

@Mapper
public interface RackLayerMapper {
  List<RackLayerExt> selectList(
      @Param("rackGroupId") Long rackGroupId,
      @Param("columnNo") Integer columnNo,
      @Param("panelNo") Integer panelNo,
      @Param("sectionNo") Integer sectionNo);

  List<RackLayer> selectListByGroupIdList(@Param("rackGroupIdList") List<Long> rackGroupIdList);

  List<RackLayer> selectListByRackGroupId(
      @Param("rackGroupId") Long rackGroupId, @Param("columnNo") Integer columnNo);

  RackLayer selectByIdOrLayerTid(@Param("id") Long id, @Param("layerTid") String layerTid);

  List<RackGridLocationExt> selectLocationList(
      @Param("wrapperList") List<RackGridWrapper> wrapperList);

  RackGridLocationExt selectLocation(
      @Param("groupNo") Integer groupNo,
      @Param("columnNo") Integer columnNo,
      @Param("panelNo") Integer panelNo,
      @Param("sectionNo") Integer sectionNo,
      @Param("layerNo") Integer layerNo,
      @Param("gridNo") Integer gridNo);

  List<RackLayer> selectListByRackGroupIdForStatTask(@Param("rackGroupId") Long rackGroupId);

  int existByTid(@Param("tid") String tid);

  int insertList(List<RackLayer> rackLayerList);

  int updateByIdSelective(RackLayer rackLayer);

  int updateBatchArchiveTypeId(
      @Param("idList") List<Long> idList, @Param("archivesTypeId") Long archivesTypeId);

  int updateUsedCapacityForStatTask(@Param("list") List<RackLayer> list);

  int deleteByRackGroupId(@Param("rackGroupId") Long rackGroupId);
}
