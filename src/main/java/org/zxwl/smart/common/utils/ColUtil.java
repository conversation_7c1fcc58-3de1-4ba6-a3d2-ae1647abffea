package org.zxwl.smart.common.utils;

import cn.hutool.core.collection.CollUtil;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.lang.Triple;

public class ColUtil {

  /**
   * 对比找出两个{@link Collection}变动，前者（coll1）相对于后者（coll2）的变动，该方法只可提取新增的和删除的元素
   *
   * @param coll1 原始集合
   * @param coll2 对比集合
   * @param keyMapper 生成对比键的映射函数
   * @param <T> 集合中元素类型
   * @param <K> 对比的key值类型
   * @return 比较结果，key：coll1对比coll2里面新增的；value：coll1对比coll2里面删除的；
   *     <pre>{@code
   * 		// 对下面的People集合使用id进行对比，筛选出新增的和删除的
   * 		List<People> coll1 = ListUtil.of(new People(1L, "<PERSON>"), new People(2L, "<PERSON>"));
   * 		List<People> coll2 = ListUtil.of(new People(2L, "Tony2"), new People(null, "Jerry"));
   *
   * 		MutableEntry<List<People>, List<People>> pair = ColUtil.diff(coll1, coll2, People::getId);
   *
   * 		System.out.println("新增的：" + pair.getLeft());
   * 		System.out.println("删除的：" + pair.getRight());
   *
   * 		// 控制台输出
   * 		// 新增的：[People(id=null, name=Jerry)]
   * 		// 删除的：[People(id=1, name=Tom)]
   * }</pre>
   *
   * @see #diff(Collection, Collection, Function, BiPredicate, BiFunction)
   * @see #diff(Collection, Collection, Function, Function, BiPredicate, BiFunction)
   */
  public static <T, K> Pair<List<T>, List<T>> diff(
      Collection<T> coll1, Collection<T> coll2, Function<T, K> keyMapper) {
    Triple<List<T>, List<T>, List<Object>> triple =
        diff(coll1, coll2, keyMapper, keyMapper, null, null);
    return Pair.of(triple.getLeft(), triple.getMiddle());
  }

  /**
   * 对比找出两个{@link Collection}变动，前者（coll1）相对于后者（coll2）的变动，该方法可提取新增、删除和修改的元素
   *
   * @param coll1 原始集合
   * @param coll2 对比集合
   * @param keyMapper 生成对比键的映射函数
   * @param equalFunction 对比函数，比较键一样的元素是否发生变化
   * @param <T> 集合中元素类型
   * @param <K> 对比的key值类型
   * @return 比较结果，left：coll1对比coll2里面新增的；middle：coll1对比coll2里面删除的；right：coll1对比coll2里面发生改变的
   *     <pre>{@code
   * 		// 对下面的People集合使用id进行对比，筛选出新增的和删除的
   * 		List<People> coll1 = ListUtil.of(new People(1L, "Tom"), new People(2L, "Tony"));
   * 		List<People> coll2 = ListUtil.of(new People(2L, "Tony2"), new People(null, "Jerry"));
   *
   * 		Triple<List<People>, List<People>, List<Pair<People, People>>> triple =
   * 				ColUtil.diff(
   * 						coll1,
   * 						coll2,
   * 						People::getId,
   * 						(p1, p2) -> p1.getName().equals(p2.getName()));
   *
   * 		System.out.println("新增的：" + triple.getLeft());
   * 		System.out.println("删除的：" + triple.getMiddle());
   * 		triple.getRight().forEach(pair -> System.out.println("修改前：" + pair.getLeft() + "；修改后：" + pair.getRight()));
   *
   * 		// 控制台输出
   * 		// 新增的：[People(id=null, name=Jerry)]
   * 		// 删除的：[People(id=1, name=Tom)]
   * 		// 修改前：ColUtil.People(id=2, name=Tony)；修改后：ColUtil.People(id=2, name=Tony2)
   * }</pre>
   *
   * @see #diff(Collection, Collection, Function)
   * @see #diff(Collection, Collection, Function, Function, BiPredicate, BiFunction)
   */
  public static <T, K> Triple<List<T>, List<T>, List<Pair<T, T>>> diff(
      Collection<T> coll1,
      Collection<T> coll2,
      Function<T, K> keyMapper,
      BiPredicate<T, T> equalFunction) {
    return diff(coll1, coll2, keyMapper, keyMapper, equalFunction, Pair::of);
  }

  /**
   * 对比找出两个{@link Collection}变动，前者（coll1）相对于后者（coll2）的变动，该方法可提取新增、删除和修改的元素
   *
   * @param coll1 原始集合
   * @param coll2 对比集合
   * @param keyMapper 生成对比键的映射函数
   * @param equalFunction 对比函数，比较键一样的元素是否发生变化
   * @param mergeFunction 元素变动处理选择器，必填参数，apply第一个参数为原始集合元素，第二个参数为对比集合元素
   * @param <T> 集合中元素类型
   * @param <K> 对比的key值类型
   * @param <R> 变更的新旧元素合并后的类型
   * @return 比较结果，left：coll1对比coll2里面新增的；middle：coll1对比coll2里面删除的；right：coll1对比coll2里面发生改变的
   *     <pre>{@code
   * 		// 对下面的People集合使用id进行对比，筛选出新增的和删除的
   * 		List<People> coll1 = ListUtil.of(new People(1L, "Tom"), new People(2L, "Tony"));
   * 		List<People> coll2 = ListUtil.of(new People(2L, "Tony2"), new People(null, "Jerry"));
   *
   * 		Triple<List<People>, List<People>, List<People>> triple =
   * 				ColUtil.diff(
   * 						coll1,
   * 						coll2,
   * 						People::getId,
   * 						(p1, p2) -> p1.getName().equals(p2.getName()),
   * 						(p1, p2) -> p2);
   *
   * 		System.out.println("新增的：" + triple.getLeft());
   * 		System.out.println("删除的：" + triple.getMiddle());
   * 		System.out.println("修改的：" + triple.getRight());
   *
   * 		// 控制台输出
   * 		// 新增的：[People(id=null, name=Jerry)]
   * 		// 删除的：[People(id=1, name=Tom)]
   * 		// 修改的：[People(id=2, name=Tony2)]
   * }</pre>
   *
   * @see #diff(Collection, Collection, Function)
   * @see #diff(Collection, Collection, Function, Function, BiPredicate, BiFunction)
   */
  public static <T, K, R> Triple<List<T>, List<T>, List<R>> diff(
      Collection<T> coll1,
      Collection<T> coll2,
      Function<T, K> keyMapper,
      BiPredicate<T, T> equalFunction,
      BiFunction<T, T, R> mergeFunction) {
    return diff(coll1, coll2, keyMapper, keyMapper, equalFunction, mergeFunction);
  }

  /**
   * 对比找出两个{@link Collection}变动，前者（coll1）相对于后者（coll2）的变动，该方法可提取新增、删除和修改的元素
   *
   * @param collForT 原始集合
   * @param collForU 对比集合
   * @param keyMapperForT 原始集合生成对比键的映射函数
   * @param keyMapperForU 对比集合生成对比键的映射函数
   * @param equalFunction 对比函数，比较键一样的元素是否发生变化
   * @param mergeFunction 元素变动处理选择器，必填参数，apply第一个参数为原始集合元素，第二个参数为对比集合元素
   * @param <T> 原始集合（coll1）中元素类型
   * @param <U> 对比集合（coll2）中元素类型
   * @param <K> 对比的key值类型
   * @param <R> 变更的新旧元素合并后的类型
   * @return 比较结果，left：coll1对比coll2里面新增的；middle：coll1对比coll2里面删除的；right：coll1对比coll2里面发生改变的
   *     <pre>{@code
   * 		// 对下面的People集合使用id进行对比，筛选出新增的和删除的
   * 		List<People> coll1 = ListUtil.of(new People(1L, "Tom"), new People(2L, "Tony"));
   * 		List<American> coll2 = ListUtil.of(new American(2L, "Tony2"), new American(null, "Jerry"));
   *
   * 		Triple<List<American>, List<People>, List<American>> triple =
   * 				ColUtil.diff(
   * 						coll1,
   * 						coll2,
   * 						People::getId,
   * 						American::getId,
   * 						(p, a) -> p.getName().equals(a.getName()),
   * 						(p, a) -> a);
   *
   * 		System.out.println("新增的：" + triple.getLeft());
   * 		System.out.println("删除的：" + triple.getMiddle());
   * 		System.out.println("修改的：" + triple.getRight());
   *
   * 		// 控制台输出
   * 		// 新增的：[American(id=null, name=Jerry)]
   * 		// 删除的：[People(id=1, name=Tom)]
   * 		// 修改的：[American(id=2, name=Tony2)]
   * }</pre>
   *
   * @see #diff(Collection, Collection, Function)
   * @see #diff(Collection, Collection, Function, BiPredicate, BiFunction)
   */
  public static <T, U, K, R> Triple<List<U>, List<T>, List<R>> diff(
      Collection<T> collForT,
      Collection<U> collForU,
      Function<T, K> keyMapperForT,
      Function<U, K> keyMapperForU,
      BiPredicate<T, U> equalFunction,
      BiFunction<T, U, R> mergeFunction) {
    Objects.requireNonNull(keyMapperForT);
    Objects.requireNonNull(keyMapperForU);
    // 记录 collForT 里面新加的元素
    List<U> addElementList = new ArrayList<>();
    // 记录 collForT 里面删除的元素
    List<T> removeElementList = new ArrayList<>();
    // 记录 collForT 里面修改的元素
    List<R> modifyElementList = new ArrayList<>();

    if (isEmpty(collForT)) {
      if (!isEmpty(collForU)) {
        addElementList.addAll(collForU);
      }
      return Triple.of(addElementList, removeElementList, modifyElementList);
    }
    if (isEmpty(collForU)) {
      if (!isEmpty(collForT)) {
        removeElementList.addAll(collForT);
      }
      return Triple.of(addElementList, removeElementList, modifyElementList);
    }

    // 为了保证之前集合的元素顺序，这里使用 LinkedHashMap 和 LinkedHashSet
    LinkedHashMap<K, T> mapForT =
        collForT.stream()
            .collect(Collectors.toMap(keyMapperForT, t -> t, (t, t2) -> t, LinkedHashMap::new));
    LinkedHashMap<K, U> mapForU =
        collForU.stream()
            .collect(Collectors.toMap(keyMapperForU, u -> u, (u, u2) -> u, LinkedHashMap::new));
    LinkedHashSet<K> unionKeySet = new LinkedHashSet<>();
    unionKeySet.addAll(mapForU.keySet());
    unionKeySet.addAll(mapForT.keySet());

    for (K k : unionKeySet) {
      T t = mapForT.get(k);
      U u = mapForU.get(k);
      if (Objects.isNull(t)) {
        // collForT 里面新加的
        addElementList.add(u);
      } else if (Objects.isNull(u)) {
        // collForT 里面删除的
        removeElementList.add(t);
      } else {
        if (Objects.nonNull(equalFunction)) {
          boolean equal = equalFunction.test(t, u);
          if (!equal) {
            // collForT 里面变动的
            R apply = mergeFunction.apply(t, u);
            modifyElementList.add(apply);
          }
        }
      }
    }
    return Triple.of(addElementList, removeElementList, modifyElementList);
  }

  /**
   * 集合支持指定key进行去重
   *
   * @param coll 需要去重的集合
   * @param keyMapper 对比键的映射函数
   * @param <T> 去重集合（coll）中元素类型
   * @param <K> 对比的key值类型
   * @return 去重之后的新集合
   */
  public static <T, K> List<T> distinct(Collection<T> coll, Function<T, K> keyMapper) {
    if (isEmpty(coll)) {
      return new ArrayList<>();
    }
    LinkedHashMap<K, T> map =
        coll.stream()
            .collect(Collectors.toMap(keyMapper, t -> t, (t, t2) -> t, LinkedHashMap::new));
    return new ArrayList<>(map.values());
  }

  private static boolean isEmpty(Collection<?> coll) {
    return Objects.isNull(coll) || coll.isEmpty();
  }

  /**
   * 根据指定的分类函数对集合进行分组，兼容key值为null的情况
   *
   * @param <T> 集合元素类型
   * @param <K> 分组key类型
   * @param coll 集合
   * @param classifier 分类函数
   * @return 分组结果
   */
  public static <T, K> Map<K, List<T>> groupingBy(Collection<T> coll, Function<T, K> classifier) {
    if (isEmpty(coll)) {
      return Collections.emptyMap();
    }
    Map<K, List<T>> result = new HashMap<>();
    for (T t : coll) {
      K key = classifier.apply(t);
      result.computeIfAbsent(key, k -> new ArrayList<>()).add(t);
    }
    return result;
  }

  /**
   * 合并两个Map并返回值发生变更的条目
   *
   * @param originalMap 原始Map，将被更新
   * @param newMap 包含新数据的Map
   * @return 包含所有值发生变更key集合
   */
  public static Set<String> mergeAndGetChanges(
      Map<String, Object> originalMap, Map<String, Object> newMap) {
    Set<String> changedKeys = new HashSet<>();
    for (Map.Entry<String, Object> entry : newMap.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();
      if (!originalMap.containsKey(key) || !originalMap.get(key).equals(value)) {
        changedKeys.add(key);
      }
    }
    originalMap.putAll(newMap);
    return changedKeys;
  }

  /**
   * 合并列表
   *
   * @param list 待合并列表
   * @param mergeKeyGetter 合并键获取函数
   * @param merger 合并函数
   * @return 合并后的列表
   * @param <T> 列表元素类型
   */
  public static <T> List<T> mergeList(
      List<T> list, Function<T, String> mergeKeyGetter, Function<List<T>, T> merger) {
    if (CollUtil.isEmpty(list)) {
      return list;
    }
    Map<String, List<T>> map = list.stream().collect(Collectors.groupingBy(mergeKeyGetter));
    List<T> newList = new ArrayList<>();
    map.forEach(
        (key, pairList) -> {
          newList.add(merger.apply(pairList));
        });
    return newList;
  }
}
