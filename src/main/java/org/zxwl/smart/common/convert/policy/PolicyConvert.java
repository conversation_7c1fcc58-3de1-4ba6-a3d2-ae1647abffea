package org.zxwl.smart.common.convert.policy;

import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.zxwl.smart.domain.request.policy.PolicyActionRequest;
import org.zxwl.smart.domain.request.policy.PolicyConditionRequest;
import org.zxwl.smart.domain.request.policy.PolicyPeriodRequest;
import org.zxwl.smart.domain.response.policy.*;
import org.zxwl.smart.mybatis.entity.policy.Policy;
import org.zxwl.smart.mybatis.ext.policy.PolicyAction;
import org.zxwl.smart.mybatis.ext.policy.PolicyCondition;
import org.zxwl.smart.mybatis.ext.policy.PolicyPeriod;

public class PolicyConvert {

  public static List<PolicyListVO> getPolicyListVOList(List<Policy> list) {
    List<PolicyListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(list)) {
      return voList;
    }
    for (Policy policy : list) {
      PolicyListVO vo = new PolicyListVO();
      vo.setId(policy.getId());
      vo.setWarehouseId(policy.getWarehouseId());
      vo.setAreaId(policy.getAreaId());
      vo.setSystem(policy.getSystem());
      vo.setEnabled(policy.getEnabled());
      vo.setPolicyType(policy.getPolicyType());
      vo.setPolicyName(policy.getPolicyName());
      vo.setPolicyDescription(policy.getPolicyDescription());
      vo.setCreatedAt(policy.getCreatedAt());
      voList.add(vo);
    }
    return voList;
  }

  public static PolicyVO getPolicyVO(Policy policy) {
    PolicyVO vo = new PolicyVO();
    vo.setId(policy.getId());
    vo.setWarehouseId(policy.getWarehouseId());
    vo.setAreaId(policy.getAreaId());
    vo.setSystem(policy.getSystem());
    vo.setEnabled(policy.getEnabled());
    vo.setPolicyType(policy.getPolicyType());
    vo.setPolicyName(policy.getPolicyName());
    vo.setPolicyDescription(policy.getPolicyDescription());
    vo.setCreatedAt(policy.getCreatedAt());
    return vo;
  }

  public static List<PolicyConditionVO> getPolicyConditionVOList(
      List<PolicyCondition> policyConditionList) {
    if (Objects.isNull(policyConditionList)) {
      return null;
    }
    List<PolicyConditionVO> voList = new ArrayList<>();
    for (PolicyCondition policyCondition : policyConditionList) {
      PolicyConditionVO vo = new PolicyConditionVO();
      vo.setFieldName(policyCondition.getFieldName());
      vo.setOperator(policyCondition.getOperator());
      vo.setFieldValue(policyCondition.getFieldValue());
      vo.setRangeValue(policyCondition.getRangeValue());
      voList.add(vo);
    }
    return voList;
  }

  public static List<PolicyPeriodVO> getPolicyPeriodVOList(List<PolicyPeriod> policyPeriodList) {
    if (Objects.isNull(policyPeriodList)) {
      return null;
    }
    List<PolicyPeriodVO> voList = new ArrayList<>();
    for (PolicyPeriod policyPeriod : policyPeriodList) {
      PolicyPeriodVO vo = new PolicyPeriodVO();
      vo.setRepeatCycle(policyPeriod.getRepeatCycle());
      vo.setRepeatDayList(policyPeriod.getRepeatDayList());
      vo.setRepeatDate(policyPeriod.getRepeatDate());
      vo.setStartTime(policyPeriod.getStartTime());
      vo.setEndTime(policyPeriod.getEndTime());
      voList.add(vo);
    }
    return voList;
  }

  public static List<PolicyActionVO> getPolicyActionVOList(List<PolicyAction> policyActionList) {
    if (Objects.isNull(policyActionList)) {
      return null;
    }
    List<PolicyActionVO> voList = new ArrayList<>();
    for (PolicyAction policyAction : policyActionList) {
      PolicyActionVO vo = new PolicyActionVO();
      vo.setType(policyAction.getType());
      vo.setDeviceId(policyAction.getDeviceId());
      vo.setControlFieldNameList(policyAction.getControlFieldNameList());
      vo.setEndControlFieldNameList(policyAction.getEndControlFieldNameList());
      vo.setPhone(policyAction.getPhone());
      voList.add(vo);
    }
    return voList;
  }

  public static List<PolicyCondition> getPolicyConditionList(
      List<PolicyConditionRequest> conditionRequestList) {
    if (Objects.isNull(conditionRequestList)) {
      return null;
    }
    List<PolicyCondition> policyConditionList = new ArrayList<>();
    for (PolicyConditionRequest conditionRequest : conditionRequestList) {
      PolicyCondition policyCondition = new PolicyCondition();
      policyCondition.setFieldName(conditionRequest.getFieldName());
      policyCondition.setOperator(conditionRequest.getOperator());
      policyCondition.setFieldValue(conditionRequest.getFieldValue());
      policyCondition.setRangeValue(conditionRequest.getRangeValue());
      policyConditionList.add(policyCondition);
    }
    return policyConditionList;
  }

  public static List<PolicyPeriod> getPolicyPeriodList(
      List<PolicyPeriodRequest> periodRequestList) {
    if (Objects.isNull(periodRequestList)) {
      return null;
    }
    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    for (PolicyPeriodRequest periodRequest : periodRequestList) {
      PolicyPeriod policyPeriod = new PolicyPeriod();
      policyPeriod.setRepeatCycle(periodRequest.getRepeatCycle());
      policyPeriod.setRepeatDayList(periodRequest.getRepeatDayList());
      policyPeriod.setRepeatDate(periodRequest.getRepeatDate());
      policyPeriod.setStartTime(periodRequest.getStartTime());
      policyPeriod.setEndTime(periodRequest.getEndTime());
      policyPeriodList.add(policyPeriod);
    }
    return policyPeriodList;
  }

  public static List<PolicyAction> getPolicyActionList(
      List<PolicyActionRequest> actionRequestList) {
    if (Objects.isNull(actionRequestList)) {
      return null;
    }
    List<PolicyAction> policyActionList = new ArrayList<>();
    for (PolicyActionRequest actionRequest : actionRequestList) {
      PolicyAction policyAction = new PolicyAction();
      policyAction.setType(actionRequest.getType());
      policyAction.setDeviceId(actionRequest.getDeviceId());
      policyAction.setControlFieldNameList(actionRequest.getControlFieldNameList());
      policyAction.setEndControlFieldNameList(actionRequest.getEndControlFieldNameList());
      policyAction.setPhone(actionRequest.getPhone());
      policyActionList.add(policyAction);
    }
    return policyActionList;
  }

    public static Object getPolicyCacheModel(Policy policy) {
    return null;
    }
}
