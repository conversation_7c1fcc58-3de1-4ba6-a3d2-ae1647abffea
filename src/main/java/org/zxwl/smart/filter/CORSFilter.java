package org.zxwl.smart.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/** CORS跨域过滤器，确保所有跨域请求都能正确响应 */
@Slf4j
@Order(value = 50) // 给它一个最高的优先级，确保在其他过滤器之前运行
@Component
public class CORSFilter extends OncePerRequestFilter {

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
      throws ServletException, IOException {

    // 设置跨域响应头
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "*");
    response.setHeader("Access-Control-Max-Age", "3600");

    // 对于OPTIONS请求直接返回200状态码
    if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
      response.setStatus(HttpServletResponse.SC_OK);
      return;
    }

    // 继续后续过滤器链
    filterChain.doFilter(request, response);
  }
}
