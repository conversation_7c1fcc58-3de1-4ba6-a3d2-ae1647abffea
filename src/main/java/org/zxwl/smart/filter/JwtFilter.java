package org.zxwl.smart.filter;

import cn.hutool.core.date.DateUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.UserCache;
import org.zxwl.smart.cache.model.JwtToken;
import org.zxwl.smart.cache.model.JwtTokenSubject;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.common.properties.FileUploadProperties;
import org.zxwl.smart.common.utils.JwtTokenUtil;
import org.zxwl.smart.common.utils.RequestUtil;
import org.zxwl.smart.common.utils.ResponseUtil;
import org.zxwl.smart.constant.consist.AuthorizeConstant;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ResultCodeEnum;
import org.zxwl.smart.service.user.UserService;

/** jwt token 验证和解析过滤器 */
@Slf4j
@Order(value = 300)
@Component
public class JwtFilter extends AbstractBaseFilter {

  private final UserService userService;
  private final UserCache userCache;
  private final RequestMappingHandlerMapping handlerMapping;

  public JwtFilter(
      FileUploadProperties fileUploadProperties,
      UserService userService,
      UserCache userCache,
      RequestMappingHandlerMapping handlerMapping) {
    super(fileUploadProperties);
    this.userService = userService;
    this.userCache = userCache;
    this.handlerMapping = handlerMapping;
  }

  @Override
  protected void doFilterInternal2(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws IOException, ServletException {
    String uri = request.getRequestURI();
    // 1、取出 并解析 Token
    String terminal = AccountLocal.getTerminal();
    String jwtTokenKey = AuthorizeConstant.HEADER_AUTHORIZE_KEY;
    String token = request.getHeader(jwtTokenKey);
    JwtTokenSubject subject = JwtTokenUtil.parseAccessToken(token, JwtTokenSubject.class);
    if (Objects.isNull(subject)
        || Objects.isNull(subject.getUserId())
        || Objects.isNull(subject.getOrganizationId())) {
      String header = RequestUtil.getRequestHeaderJsonStr(request);
      String format = "解析 jwt token 失败，uri：{}，header：{}，subject：{}";
      log.error(format, uri, header, subject);
      ResponseUtil.responseJson(response, new BaseResult(ResultCodeEnum.UNAUTHORIZED));
      return;
    }
    Long organizationId = subject.getOrganizationId();
    Long userId = subject.getUserId();
    // 2、检查是否有登录Token缓存
    JwtToken jwtToken = userCache.getJwtToken(userId, organizationId, terminal);
    if (Objects.isNull(jwtToken)) {
      String header = RequestUtil.getRequestHeaderJsonStr(request);
      String format = "解析 jwt token 失败：没有对应的缓存，uri：{}，header：{}，subject：{}";
      log.error(format, uri, header, subject);
      BaseResult result = new BaseResult(ResultCodeEnum.UNAUTHORIZED);
      ResponseUtil.responseJson(response, result);
      return;
    }
    /*
    // 3、比较前端传过来的Token跟服务端缓存的Token是否一致（被别人登录挤掉）
    if (!StrUtil.equals(jwtToken.getToken(), token)) {
      // 主Token对不上，这里取出即将临期token比较下，防止前端接口并发传过来的是一个旧的Token而报错
      JwtToken neJwtToken = userCache.getNeJwtToken(userId, terminal);
      if (Objects.isNull(neJwtToken) || !StrUtil.equals(neJwtToken.getToken(), token)) {
        String header = RequestUtil.getRequestHeaderJsonStr(request);
        String format = "解析 jwt token 失败：redis缓存token跟请求token不一样，token已被刷新，uri：{}，header：{}";
        log.error(format, uri, header);
        BaseResult result = new BaseResult(ResultCodeEnum.UNAUTHORIZED);
        ResponseUtil.responseJson(response, result);
        return;
      }
    }
     */
    // 4、检查Token是否需要刷新
    if (jwtToken.getRefreshDate().before(new Date())) {
      log.debug("解析 jwt token 成功：已过刷新时间，重新刷新");
      // 将之前的旧的Token缓存成临期的Token，防止前端并发后面的接口传过来的还是旧Token
      jwtToken.setExpiredDate(
          DateUtil.offsetMillisecond(new Date(), AuthorizeConstant.JWT_NE_TOKEN_TIME));
      userCache.cacheNeJwtToken(userId, organizationId, terminal, jwtToken);
      // 生成新Token，放入缓存，并放到响应头中去
      jwtToken = userService.refreshJwtTokenCache(userId, organizationId, terminal);
      ResponseUtil.addHeader(response, jwtTokenKey, jwtToken.getToken(), true);
    }
    // 5、缓存JwtToken中携带信息
    AccountLocal.setUserId(userId);
    AccountLocal.setOrganizationId(organizationId);
    AccountLocal.setExpiredDate(jwtToken.getExpiredDate());
    // 6、根据Token携带字段，刷新缓存信息并绑定到当前线程
    userService.refreshUserCacheIfNeed(userId, null);
    filterChain.doFilter(request, response);
  }

  @Override
  protected boolean shouldInterceptedRequestInternal(HttpServletRequest request) throws Exception {
    HandlerExecutionChain handler = handlerMapping.getHandler(request);
    if (Objects.isNull(handler)) {
      return false;
    }
    String terminal = AccountLocal.getTerminal();
    HandlerMethod handlerMethod = (HandlerMethod) handler.getHandler();
    if (AuthorizeConstant.TERMINAL_WEB.equals(terminal)) {
      WebApi webApi = handlerMethod.getMethodAnnotation(WebApi.class);
      return Objects.nonNull(webApi) && webApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_HANDHELD.equals(terminal)) {
      HandheldApi handheldApi = handlerMethod.getMethodAnnotation(HandheldApi.class);
      return Objects.nonNull(handheldApi) && handheldApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_WORKSTATION.equals(terminal)) {
      WorkstationApi workstationApi = handlerMethod.getMethodAnnotation(WorkstationApi.class);
      return Objects.nonNull(workstationApi) && workstationApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_INVENTORY_CAR.equals(terminal)) {
      InventoryCarApi inventoryCarApi = handlerMethod.getMethodAnnotation(InventoryCarApi.class);
      return Objects.nonNull(inventoryCarApi) && inventoryCarApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_AREA_CONTROL.equals(terminal)) {
      AreaControlApi areaControlApi = handlerMethod.getMethodAnnotation(AreaControlApi.class);
      return Objects.nonNull(areaControlApi) && areaControlApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_EAS.equals(terminal)) {
      EASApi easApi = handlerMethod.getMethodAnnotation(EASApi.class);
      return Objects.nonNull(easApi) && easApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_ARCHIVE_CABINET.equals(terminal)) {
      ArchiveCabinetApi archiveCabinetApi =
          handlerMethod.getMethodAnnotation(ArchiveCabinetApi.class);
      return Objects.nonNull(archiveCabinetApi) && archiveCabinetApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_HR_CABINET.equals(terminal)) {
      HRCabinetApi hrCabinetApi = handlerMethod.getMethodAnnotation(HRCabinetApi.class);
      return Objects.nonNull(hrCabinetApi) && hrCabinetApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_SMART_RACK.equals(terminal)) {
      SmartRackApi smartRackApi = handlerMethod.getMethodAnnotation(SmartRackApi.class);
      return Objects.nonNull(smartRackApi) && smartRackApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_LIGHT_RACK.equals(terminal)) {
      LightRackApi lightRackApi = handlerMethod.getMethodAnnotation(LightRackApi.class);
      return Objects.nonNull(lightRackApi) && lightRackApi.requireAuth();
    } else if (AuthorizeConstant.TERMINAL_BOX_RACK.equals(terminal)) {
      BoxRackApi boxRackApi = handlerMethod.getMethodAnnotation(BoxRackApi.class);
      return Objects.nonNull(boxRackApi) && boxRackApi.requireAuth();
    }
    return true;
  }
}
