package org.zxwl.smart.scheduler;

import cn.hutool.core.map.MapUtil;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.cache.model.AreaCacheModel;
import org.zxwl.smart.cache.model.DoorControlDeviceCacheModel;
import org.zxwl.smart.cache.model.EnvDeviceCacheModel;
import org.zxwl.smart.cache.model.WarehouseCacheModel;
import org.zxwl.smart.client.hk.ConnectionManager;
import org.zxwl.smart.client.hk.enums.*;
import org.zxwl.smart.constant.consist.WebsocketConstant;
import org.zxwl.smart.domain.response.ws.WSDeviceDataChangedVO;
import org.zxwl.smart.mybatis.entity.device.EnvAirQualityRecord;
import org.zxwl.smart.mybatis.entity.device.EnvEnergyRecord;
import org.zxwl.smart.mybatis.mapper.device.EnvAirQualityRecordMapper;
import org.zxwl.smart.mybatis.mapper.device.EnvEnergyRecordMapper;

@Slf4j
@Component
@AllArgsConstructor
public class EnvDeviceScheduler {

  private EnvDataCache envDataCache;
  private EnvAirQualityRecordMapper envAirQualityRecordMapper;
  private EnvEnergyRecordMapper envEnergyRecordMapper;
  private ConnectionManager connectionManager;
  private SimpMessagingTemplate messagingTemplate;

  /** 设备在线状态检查 */
  @Scheduled(cron = "0/5 * * * * ?")
  public void checkDeviceOnlineStatus() {
    // 环控设备
    List<EnvDeviceCacheModel> deviceList = envDataCache.getAllDevice();
    for (EnvDeviceCacheModel device : deviceList) {
      if (device.isOnline()
          && System.currentTimeMillis() - device.getLastReceiveDataTime() > 1000 * 60) {
        log.info(
            "设备已离线, deviceIp={}, serialPort={}, serialAddr={}, {}",
            device.getDeviceIp(),
            device.getDeviceSerialPort(),
            device.getDeviceSerialAddr(),
            System.currentTimeMillis() - device.getLastReceiveDataTime());
        device.setOnline(false);
        // 发送websocket通知
        WSDeviceDataChangedVO vo = new WSDeviceDataChangedVO();
        vo.setId(device.getId());
        vo.setOnline(device.isOnline());
        vo.setDeviceFieldList(Collections.emptyList());
        messagingTemplate.convertAndSend(WebsocketConstant.DEVICE_DATA_CHANGED, vo);
      }
    }
    // 门禁设备
    List<DoorControlDeviceCacheModel> doorControlDeviceList =
        envDataCache.getAllDoorControlDevice();
    for (DoorControlDeviceCacheModel doorControlDevice : doorControlDeviceList) {
      // 如果设备当前是在线状态，但超过1分钟没有通信，则标记为离线
      if (doorControlDevice.isOnline()
          && System.currentTimeMillis() - doorControlDevice.getLastReceiveDataTime() > 1000 * 60) {
        log.info(
            "门禁设备已离线, deviceNo={}, 最后通信时间: {}",
            doorControlDevice.getDeviceNo(),
            doorControlDevice.getLastReceiveDataTime());
        doorControlDevice.setOnline(false);
      }
    }
  }

  /** 设备历史记录生成 */
  @Scheduled(cron = "0 0,30 * * * ?")
  public void generateDeviceRecord() {
    List<EnvDeviceCacheModel> deviceList = envDataCache.getAllDevice();
    for (EnvDeviceCacheModel device : deviceList) {
      // 单个设备数据采集
      if (EnvDeviceTypeEnum.WSDCGQ.equalsValue(device.getDeviceType())) {
        // 生成温湿度记录
        EnvAirQualityRecord record = new EnvAirQualityRecord();
        record.setWarehouseId(device.getWarehouseId());
        record.setDeviceId(device.getId());
        record.setTemperature(
            MapUtil.get(
                device.getFieldValuesMap(), WSDCGQFieldEnum.TEMPERATURE.getName(), Float.class));
        record.setHumidity(
            MapUtil.get(
                device.getFieldValuesMap(), WSDCGQFieldEnum.HUMIDITY.getName(), Float.class));
        envAirQualityRecordMapper.insert(record);
      } else if (EnvDeviceTypeEnum.YCY.equalsValue(device.getDeviceType())) {
        // 生成空气质量记录
        EnvAirQualityRecord record = new EnvAirQualityRecord();
        record.setWarehouseId(device.getWarehouseId());
        record.setDeviceId(device.getId());
        record.setTemperature(
            MapUtil.get(
                device.getFieldValuesMap(), YCYFieldEnum.TEMPERATURE.getName(), Float.class));
        record.setHumidity(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.HUMIDITY.getName(), Float.class));
        record.setPm25(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.PM25.getName(), Float.class));
        record.setPm10(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.PM10.getName(), Float.class));
        record.setTvoc(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.TVOC.getName(), Float.class));
        record.setCo2(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.CO2.getName(), Float.class));
        record.setHcho(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.HCHO.getName(), Float.class));
        record.setO3(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.O3.getName(), Float.class));
        record.setLight(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.LIGHT.getName(), Float.class));
        record.setNo2(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.NO2.getName(), Float.class));
        record.setSo2(
            MapUtil.get(device.getFieldValuesMap(), YCYFieldEnum.SO2.getName(), Float.class));
        envAirQualityRecordMapper.insert(record);
      } else if (EnvDeviceTypeEnum.NHCJQ.equalsValue(device.getDeviceType())) {
        // 生成能耗记录
        EnvEnergyRecord record = new EnvEnergyRecord();
        record.setWarehouseId(device.getWarehouseId());
        record.setDeviceId(device.getId());
        record.setVoltage(
            MapUtil.get(device.getFieldValuesMap(), NHCJQFieldEnum.VOLTAGE.getName(), Float.class));
        record.setCurrent(
            MapUtil.get(device.getFieldValuesMap(), NHCJQFieldEnum.CURRENT.getName(), Float.class));
        record.setPower(
            MapUtil.get(device.getFieldValuesMap(), NHCJQFieldEnum.POWER.getName(), Float.class));
        envEnergyRecordMapper.insert(record);
      }
    }
    // 区域空气数据采集
    List<AreaCacheModel> areaList = envDataCache.getAllArea();
    for (AreaCacheModel area : areaList) {
      // 生成记录
      EnvAirQualityRecord record = new EnvAirQualityRecord();
      record.setWarehouseId(area.getWarehouseId());
      record.setAreaId(area.getId());
      record.setTemperature(area.getTemperature());
      record.setHumidity(area.getHumidity());
      record.setPm25(area.getPm25());
      record.setPm10(area.getPm10());
      record.setTvoc(area.getTvoc());
      record.setCo2(area.getCo2());
      envAirQualityRecordMapper.insert(record);
    }
    // 区域能耗数据采集
    for (AreaCacheModel area : areaList) {
      // 生成记录
      EnvEnergyRecord record = new EnvEnergyRecord();
      record.setWarehouseId(area.getWarehouseId());
      record.setAreaId(area.getId());
      record.setVoltage(area.getVoltage());
      record.setCurrent(area.getCurrent());
      record.setPower(area.getPower());
      envEnergyRecordMapper.insert(record);
    }
    // 库房空气数据采集
    List<WarehouseCacheModel> warehouseList = envDataCache.getAllWarehouse();
    for (WarehouseCacheModel warehouse : warehouseList) {
      // 生成记录
      EnvAirQualityRecord record = new EnvAirQualityRecord();
      record.setWarehouseId(warehouse.getWarehouseId());
      record.setTemperature(warehouse.getTemperature());
      record.setHumidity(warehouse.getHumidity());
      record.setPm25(warehouse.getPm25());
      record.setPm10(warehouse.getPm10());
      record.setTvoc(warehouse.getTvoc());
      record.setCo2(warehouse.getCo2());
      envAirQualityRecordMapper.insert(record);
    }
    // 库房能耗数据采集
    for (WarehouseCacheModel warehouse : warehouseList) {
      // 生成记录
      EnvEnergyRecord record = new EnvEnergyRecord();
      record.setWarehouseId(warehouse.getWarehouseId());
      record.setVoltage(warehouse.getVoltage());
      record.setCurrent(warehouse.getCurrent());
      record.setPower(warehouse.getPower());
      envEnergyRecordMapper.insert(record);
    }
    // 全馆空气数据采集
    EnvAirQualityRecord airQualityRecord = new EnvAirQualityRecord();
    airQualityRecord.setTemperature(envDataCache.getBuilding().getTemperature());
    airQualityRecord.setHumidity(envDataCache.getBuilding().getHumidity());
    airQualityRecord.setPm25(envDataCache.getBuilding().getPm25());
    airQualityRecord.setPm10(envDataCache.getBuilding().getPm10());
    airQualityRecord.setTvoc(envDataCache.getBuilding().getTvoc());
    airQualityRecord.setCo2(envDataCache.getBuilding().getCo2());
    envAirQualityRecordMapper.insert(airQualityRecord);
    // 全馆能耗数据采集
    EnvEnergyRecord energyRecord = new EnvEnergyRecord();
    energyRecord.setVoltage(envDataCache.getBuilding().getVoltage());
    energyRecord.setCurrent(envDataCache.getBuilding().getCurrent());
    energyRecord.setPower(envDataCache.getBuilding().getPower());
    envEnergyRecordMapper.insert(energyRecord);
  }

  /** 设备数据查询 */
  @Scheduled(fixedDelay = 1000)
  public void queryDeviceData() throws Exception {
    List<EnvDeviceCacheModel> deviceList = envDataCache.getAllDevice();
    for (EnvDeviceCacheModel device : deviceList) {
      // 获取设备数据
      String deviceType = device.getDeviceType();
      String deviceIp = device.getDeviceIp();
      Byte serialPort = device.getDeviceSerialPort();
      Byte serialAddr = device.getDeviceSerialAddr();
      if (!connectionManager.isConnected(deviceIp)) {
        continue;
      }
      if (EnvDeviceTypeEnum.DATA_HUB.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, null, null, DataHubControlFieldEnum.GET_IO_STATE.getName(), null);
        Thread.sleep(2000);
        connectionManager.sendCommand(
            deviceIp, null, null, DataHubControlFieldEnum.GET_RELAY_STATE.getName(), null);
      } else if (EnvDeviceTypeEnum.SF6.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, SF6ControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.NHCJQ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, NHCJQControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.GZDTCQ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp,
            serialPort,
            serialAddr,
            GZDTCQControlFieldEnum.QUERY_REGISTER.getName(),
            null);
      } else if (EnvDeviceTypeEnum.ZNQSQ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, ZNQSQControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.WSDCGQ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp,
            serialPort,
            serialAddr,
            WSDCGQControlFieldEnum.QUERY_REGISTER.getName(),
            null);
      } else if (EnvDeviceTypeEnum.YCY.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, YCYControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.ZXHSJ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, ZXHSJControlFieldEnum.QUERY_COIL.getName(), null);
        Thread.sleep(2000);
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, ZXHSJControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.KTKZQ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, KTKZQControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.ZXXFJ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp, serialPort, serialAddr, ZXXFJControlFieldEnum.QUERY_REGISTER.getName(), null);
      } else if (EnvDeviceTypeEnum.ZXKQJKJJJ.equalsValue(deviceType)) {
        connectionManager.sendCommand(
            deviceIp,
            serialPort,
            serialAddr,
            ZXKQJKJJJControlFieldEnum.QUERY_REGISTER.getName(),
            null);
      } else {
        log.warn("环控 - 定时查询，未找到对应的设备类型: {}", device);
      }
      Thread.sleep(2000);
    }
  }
}
