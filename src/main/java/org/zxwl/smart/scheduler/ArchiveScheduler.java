package org.zxwl.smart.scheduler;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.zxwl.smart.mybatis.entity.cabinet.*;
import org.zxwl.smart.mybatis.mapper.cabinet.*;
import org.zxwl.smart.service.cabinet.CabinetService;
import org.zxwl.smart.service.cabinet.HrCabinetService;
import org.zxwl.smart.service.rack.RackGroupService;

@Slf4j
@Component
@AllArgsConstructor
public class ArchiveScheduler {

  private final RackGroupService rackGroupService;
  private final CabinetService cabinetService;
  private final HrCabinetService hrCabinetService;

  /** 定时更新容量 */
  @Scheduled(cron = "* * 01 * * ?")
  public void updateRackAndCabinetUsedCapacity() {
    // 密集架容量更新
    rackGroupService.updateRackUsedCapacityAsync(null);
    // 人事柜容量更新
    hrCabinetService.updateHrCabinetUsedCapacityAsync(null);
    // 档案柜容量更新
    cabinetService.updateCabinetUsedCapacityAsync(null);
  }
}
