package org.zxwl.smart.client.hk;

import cn.hutool.core.util.HexUtil;
import cn.hutool.socket.nio.NioClient;
import io.netty.buffer.Unpooled;
import java.nio.ByteBuffer;
import java.util.Scanner;
import org.zxwl.smart.client.hk.frame.DataHubFrameParser;
import org.zxwl.smart.client.hk.utils.FrameUtil;

public class TcpClient {

  public static void main(String[] args) {

    /*
    [发送查询网络参数指令] A5 A5 00 06 1B 00 00 C0 A8 3C B4 FF FF FF 00 C0 A8 3C 01 10 27 C0 A8 3C 92 A3 46 70 19 88 62 82 55 AA 0A
    [发送查询波特率指令] A5 A5 00 0C 00 00 94 F4
    [发送设置波特率] A5 A5 00 0D 20 00 C0 12 00 00 C0 12 00 00 80 25 00 00 80 25 00 00 80 25 00 00 80 25 00 00 80 25 00 00 80 25 00 00 8D 2B
    [发送重启指令] A5 A5 00 08 00 00 D5 35
    [发送查询版本号指令] A5 A5 00 05 00 00 44 F6
     */
    /*
    设置波特率：温湿度和云测仪 4800，其他公司产品 9600
    [9600, 9600, 9600, 9600, 9600, 9600, 9600, 4800]
    System.out.println(FrameUtil.byteBufToHexStr(DataHubFrameBuilder.buildSetBaudRateFrame(ListUtil.of(9600, 4800, 4800, 9600, 9600, 9600, 9600, 4800))));
     */
    /*
    设置网络参数
     System.out.println(FrameUtil.byteBufToHexStr(DataHubFrameBuilder.buildSetNetParamFrame("**************", "***********", "************", (short) 10000, "**************", (short) 18083, "3C:AB:72:F3:13:3D")));
     */
    // 创建 NIO 客户端
    // A5 A5 03 80 0A 00 00 01 03 04 02 94 01 3A 3B E4 8E 8A
    NioClient client = new NioClient("localhost", 18083);
    // NioClient client = new NioClient("*************", 18083);
    // NioClient client = new NioClient("**************", 10000);
    // NioClient client = new NioClient("**************", 10000);
    // 设置客户端监听器（处理服务端响应）
    client.setChannelHandler(
        channel -> {
          // 读取服务端返回的数据
          ByteBuffer buffer = ByteBuffer.allocate(1024);
          int readBytes = channel.read(buffer);
          if (readBytes > 0) {
            buffer.flip();
            byte[] bytes = new byte[buffer.remaining()];
            buffer.get(bytes);
            System.out.println("[收到服务端响应] " + FrameUtil.bytesToHexStr(bytes));
            if (bytes[3] == 0x06) {
              System.out.println(
                  DataHubFrameParser.parseGetNetParamFrame(Unpooled.wrappedBuffer(bytes)));
            } else if (bytes[3] == 0x0C) {
              System.out.println(
                  DataHubFrameParser.parseGetBaudRateFrame(Unpooled.wrappedBuffer(bytes)));
            }
          }
        });

    // 启动客户端连接
    client.listen(); // 监听可读事件

    // 发送消息给服务端
    try (Scanner scanner = new Scanner(System.in)) {
      while (true) {
        // 进入升级：A5A50001010000BE10
        System.out.print("请输入要发送的消息（输入 exit 退出）: ");
        String message = scanner.nextLine();
        if ("exit".equalsIgnoreCase(message)) {
          break;
        }
        client.write(ByteBuffer.wrap(HexUtil.decodeHex(message)));
      }
    } finally {
      client.close(); // 关闭客户端连接
    }
  }
}
