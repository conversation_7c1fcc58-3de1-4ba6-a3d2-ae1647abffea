package org.zxwl.smart.constant.consist;

public class AuthorizeConstant {

  /** jwt 签发人 */
  public static final String JWT_ISSUER = "ZXWL-JWT-ISSUER";

  /** jwt 签名秘钥，需要定期变动 */
  public static final String JWT_SIGNER_KEY = "ZXWL-JWT-SIGN-KEY";

  /** jwt 持续的有效时间，单位毫秒 604800000 */
  public static final Integer JWT_EXPIRATION_TIME = 7 * 24 * 3600 * 1000;

  /** jwt 刷新时间，单位毫秒 */
  public static final Integer JWT_REFRESH_TIME = 6 * 3600 * 1000;

  /** jwt 临期token缓冲时间，单位毫秒 */
  public static final int JWT_NE_TOKEN_TIME = 5 * 1000;

  /** token 前缀 */
  public static final String AUTHORIZE_TOKEN_PREFIX = "Bearer_";

  /** token 在响应或请求头中的KEY */
  public static final String HEADER_AUTHORIZE_KEY = "Authorization";

  /** 响应头里面的登录状态标识 */
  public static final String LOGIN = "Login";

  /** 终端版本在请求头中的key */
  public static final String HEADER_VERSION_KEY = "Version";

  /** 终端类型在请求头中的KEY */
  public static final String HEADER_TERMINAL_KEY = "Terminal";

  /** 终端类型：web */
  public static final String TERMINAL_WEB = "web";

  /** 终端类型：手持机 */
  public static final String TERMINAL_HANDHELD = "handheld";

  /** 终端类型：馆员工作站 */
  public static final String TERMINAL_WORKSTATION = "workstation";

  /** 终端类型：盘点车 */
  public static final String TERMINAL_INVENTORY_CAR = "inventory_car";

  /** 终端类型：区域控制器 */
  public static final String TERMINAL_AREA_CONTROL = "area_control";

  /** 终端类型：防盗门设备 */
  public static final String TERMINAL_EAS = "eas";

  /** 终端类型：档案柜 */
  public static final String TERMINAL_ARCHIVE_CABINET = "archive_cabinet";

  /** 终端类型：人事柜 */
  public static final String TERMINAL_HR_CABINET = "hr_cabinet";

  /** 终端类型：交接柜 */
  // public static final String TERMINAL_HANDOVER_CABINET = "handover_cabinet";

  /** 终端类型：智能密集架 */
  public static final String TERMINAL_SMART_RACK = "smart_rack";

  /** 终端类型：发光密集架 */
  public static final String TERMINAL_LIGHT_RACK = "light_rack";

  /** 终端类型：盒定位密集架 */
  public static final String TERMINAL_BOX_RACK = "box_rack";

  /**
   * 是否是智能载具
   *
   * @param terminal 终端类型
   * @return 是否是智能载具
   */
  public static boolean isSmartVehicle(String terminal) {
    return TERMINAL_ARCHIVE_CABINET.equals(terminal)
        || TERMINAL_HR_CABINET.equals(terminal)
        || TERMINAL_SMART_RACK.equals(terminal)
        || TERMINAL_LIGHT_RACK.equals(terminal)
        || TERMINAL_BOX_RACK.equals(terminal);
  }
}
