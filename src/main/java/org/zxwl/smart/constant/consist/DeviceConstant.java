package org.zxwl.smart.constant.consist;

public class DeviceConstant {

  /** 数据汇集箱 R485 起始 */
  public static final byte DATA_HUB_R485_BEGIN_PORT = 1;

  /** 数据汇集箱 R485 结束端口 */
  public static final byte DATA_HUB_R485_END_PORT = 8;

  /** 数据汇集箱输入IO口起始端口 */
  public static final byte DATA_HUB_IN_IO_SERIAL_BEGIN_PORT = 9;

  /** 数据汇集箱输入IO口结束端口 */
  public static final byte DATA_HUB_IN_IO_SERIAL_END_PORT = 20;

  /** 数据汇集箱继电器输出口端口 */
  public static final byte DATA_HUB_IN_RELAY_BEGIN_PORT = 21;

  /** 数据汇集箱继电器输出口结束端口 */
  public static final byte DATA_HUB_IN_RELAY_END_PORT = 24;
}
