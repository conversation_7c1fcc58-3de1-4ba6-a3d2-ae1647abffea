package org.zxwl.smart.constant.consist;

public class GlobalConstant {

  /** 智慧平台接口请求前缀 */
  public static final String ZHPT_API_PREFIX = "/zhpt";

  /** 运维平台接口请求前缀 */
  public static final String OPS_API_PREFIX = "/ops";

  /** 逻辑未被删除值（存在逻辑删除的资源） */
  public static final Long LOGIC_UNDELETE = 0L;

  /** 批量操作sql条数 */
  public static final Integer BATCH_SQL_MAX_SIZE = 1000;

  /** 载具容量更新防抖延迟时间（毫秒） */
  public static final long STORAGE_CAPACITY_UPDATE_DEBOUNCE_DELAY_MS = 5000;
}
