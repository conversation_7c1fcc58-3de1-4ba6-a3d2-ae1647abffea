package org.zxwl.smart.constant.consist;

public class PolicyConstant {

  /** 每天开始时间 */
  public static final String DAILY_START_TIME = "00:00:00";

  /** 每天结束时间 */
  public static final String DAILY_END_TIME = "23:59:59";

  /** 高温调节温度 */
  public static final String HIGH_TEMP_REGULATION_TEMP = "26";

  /** 高温调节温度浮动区间 */
  public static final String HIGH_TEMP_REGULATION_RANGE = "3";

  /** 低温调节温度 */
  public static final String LOW_TEMP_REGULATION_TEMP = "18";

  /** 低温调节温度浮动区间 */
  public static final String LOW_TEMP_REGULATION_RANGE = "3";

  /** 高湿调节湿度 */
  public static final String HIGH_HUM_REGULATION_HUM = "70";

  /** 高湿调节湿度浮动区间 */
  public static final String HIGH_HUM_REGULATION_RANGE = "10";

  /** 低湿调节湿度 */
  public static final String LOW_HUM_REGULATION_HUM = "40";

  /** 低湿调节湿度浮动区间 */
  public static final String LOW_HUM_REGULATION_RANGE = "10";

  /** 高温告警 */
  public static final String HIGH_TEMP_ALARM_TEMP = "30";

  /** 低温告警 */
  public static final String LOW_TEMP_ALARM_TEMP = "10";

  /** 高湿告警 */
  public static final String HIGH_HUM_ALARM_HUM = "80";

  /** 低湿告警 */
  public static final String LOW_HUM_ALARM_HUM = "30";

  /** PM25调节值 */
  public static final String PM25_REGULATION_VALUE = "35";

  /** PM25浮动区间 */
  public static final String PM25_REGULATION_RANGE = "3";

  /** PM10调节值 */
  public static final String PM10_REGULATION_VALUE = "75";

  /** PM10浮动区间 */
  public static final String PM10_REGULATION_RANGE = "3";

  /** TVOC调节值 */
  public static final String TVOC_REGULATION_VALUE = "500";

  /** TVOC浮动区间 */
  public static final String TVOC_REGULATION_RANGE = "3";

  /** CO2调节值 */
  public static final String CO2_REGULATION_VALUE = "1000";

  /** CO2浮动区间 */
  public static final String CO2_REGULATION_RANGE = "3";

  /** PM25告警值 */
  public static final String PM25_ALARM_VALUE = "50";

  /** PM10告警值 */
  public static final String PM10_ALARM_VALUE = "100";

  /** TVOC告警值 */
  public static final String TVOC_ALARM_VALUE = "1000";

  /** CO2告警值 */
  public static final String CO2_ALARM_VALUE = "2000";
}
