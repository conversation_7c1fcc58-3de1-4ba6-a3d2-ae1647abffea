package org.zxwl.smart.constant.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 档案存取任务状态 */
@Getter
@AllArgsConstructor
public enum ArchiveTaskStatusEnum implements BaseEnum {
  // 待执行
  PENDING("pending"),
  // 执行中
  EXECUTING("executing"),
  // 待取出
  PENDING_PICKUP("pending_pickup"),
  // 完成
  COMPLETED("completed");

  private final String value;
}
