package org.zxwl.smart.constant.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 档案在架状态类型 */
@Getter
@AllArgsConstructor
public enum ArchiveShelvingStatusEnum implements BaseEnum {
  // 待上架
  PENDING_IN("pending_in"),
  // 在架
  IN("in"),
  // 待下架
  PENDING_OUT("pending_out"),
  // 不在架
  OUT("out");

  private final String value;
}
