package org.zxwl.smart.constant.enums.inventory;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 盘点条目状态 */
@Getter
@AllArgsConstructor
public enum InventoryDetailStatusEnum implements BaseEnum {
  // 在架
  ON_SHELF("on_shelf"),
  // 不在架
  OFF_SHELF("off_shelf"),
  // 错架
  MISPLACED("misplaced"),
  // 外接
  BORROW("borrow");

  private final String value;
}
