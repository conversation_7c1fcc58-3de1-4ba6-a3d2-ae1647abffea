package org.zxwl.smart.constant.enums.policy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 策略类型枚举 */
@Getter
@AllArgsConstructor
public enum PolicyTypeEnum implements BaseEnum {
  // 调控策略
  HIGH_TEMP_REGULATION("高温调控策略", "高温调控策略"),
  LOW_TEMP_REGULATION("低温调控策略", "低温调控策略"),
  HIGH_HUM_REGULATION("高湿调控策略", "高湿调控策略"),
  LOW_HUM_REGULATION("低湿调控策略", "低湿调控策略"),
  AIR_QUALITY_REGULATION("空气质量调控策略", "空气质量调控策略"),
  // 警情策略
  HIGH_TEMP_ALARM("高温警情策略", "高温警情策略"),
  LOW_TEMP_ALARM("低温警情策略", "低温警情策略"),
  HIGH_HUM_ALARM("高湿警情策略", "高湿警情策略"),
  LOW_HUM_ALARM("低湿警情策略", "低湿警情策略"),
  AIR_QUALITY_ALARM("空气质量警情策略", "空气质量警情策略"),
  INFRARED_SECURITY("红外防盗策略", "红外防盗策略"),
  WATER_LEAKAGE("漏水策略", "漏水策略"),
  FIRE_PREVENTION("防火策略", "防火策略"),
  PEST_CONTROL("防虫害策略", "防虫害策略"),
  RODENT_PREVENTION("防鼠策略", "防鼠策略");

  private final String value;
  private final String description;
}
