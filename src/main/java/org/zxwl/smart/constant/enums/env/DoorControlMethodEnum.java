package org.zxwl.smart.constant.enums.env;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** 门禁设备开门方式 */
@Getter
@AllArgsConstructor
public enum DoorControlMethodEnum {
  AUTO(0, "自动识别"),
  FINGER_ID_ONLY(1, "仅指纹"),
  PIN(2, "工号验证"),
  PASSWORD_ONLY(3, "仅密码"),
  CARD_ONLY(4, "仅卡"),
  FINGER_ID_OR_PASSWORD(5, "指纹或密码"),
  FINGER_ID_OR_CARD(6, "指纹或卡"),
  CARD_OR_PASSWORD(7, "卡或密码"),
  PIN_AND_FINGER_ID(8, "工号加指纹"),
  FINGER_ID_AND_PASSWORD(9, "指纹加密码"),
  CARD_AND_FINGER_ID(10, "卡加指纹"),
  CARD_AND_PASSWORD(11, "卡加密码"),
  FINGER_ID_AND_PASSWORD_AND_CARD(12, "指纹加密码加卡"),
  PIN_AND_FINGER_ID_AND_PASSWORD(13, "工号加指纹加密码"),
  PIN_AND_FINGER_ID_OR_CARD_AND_PASSWORD(14, "工号加指纹 或 卡加指纹"),
  FACE_ID(15, "人脸"),
  FACE_ID_AND_FINGER_ID(16, "人脸加指纹"),
  FACE_ID_AND_PASSWORD(17, "人脸加密码"),
  FACE_ID_AND_CARD(18, "人脸加卡"),
  FACE_ID_AND_FINGER_ID_AND_CARD(19, "人脸加指纹加卡"),
  FACE_ID_AND_FINGER_ID_AND_PASSWORD(20, "人脸加指纹加密码"),
  FINGER_VEINS(21, "指静脉"),
  FINGER_VEINS_AND_PASSWORD(22, "指静脉加密码"),
  FINGER_VEINS_AND_CARD(23, "指静脉加卡"),
  FINGER_VEINS_AND_PASSWORD_AND_CARD(24, "指静脉加密码加卡"),
  PALM_PRINT(25, "掌纹"),
  PALM_PRINT_AND_CARD(26, "掌纹加卡"),
  PALM_PRINT_AND_FACE_ID(27, "掌纹加面部"),
  PALM_PRINT_AND_FINGER_ID(28, "掌纹加指纹"),
  PALM_PRINT_AND_FINGER_ID_AND_FACE_ID(29, "掌纹加指纹加面部");

  private final int value;

  private final String verifyType;

  public static String getVerifyTypeByValue(int value) {
    for (DoorControlMethodEnum method : DoorControlMethodEnum.values()) {
      if (method.getValue() == value) {
        return method.getVerifyType();
      }
    }
    return null;
  }
}
