package org.zxwl.smart.constant.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 档案操作记录类型 */
@Getter
@AllArgsConstructor
public enum ArchiveOperatorTypeEnum implements BaseEnum {
  // 新建
  CREATE("create", "新建"),
  // 更新
  UPDATE("update", "更新位置"),
  // 删除
  DELETE("delete", "删除"),
  // 物理删除
  DELETE_PHYSICALLY("delete_physically", "物理删除"),
  // 恢复删除
  RESTORE("restore", "恢复删除"),
  // 绑定TID
  BIND_TID("bind_tid", "绑定TID"),
  // 更新TID
  UPDATE_TID("update_tid", "更新TID"),
  // 解绑TID
  UNBIND_TID("unbind_tid", "解绑TID"),
  // 装盒
  BOXING("boxing", "装盒"),
  // 拆盒
  UNBOXING("unboxing", "拆盒"),
  // 入库
  STOCK_IN("stock_in", "入库"),
  // 出库
  STOCK_OUT("stock_out", "出库"),
  // 上架
  SHELVE("shelve", "上架"),
  // 下架
  UNSHELVE("unshelve", "下架");

  private final String value;
  private final String name;

  public static String getNameByValue(String operatorTypeValue) {
    for (ArchiveOperatorTypeEnum operatorTypeEnum : ArchiveOperatorTypeEnum.values()) {
      if (operatorTypeEnum.getValue().equals(operatorTypeValue)) {
        return operatorTypeEnum.getName();
      }
    }
    return null;
  }
}
