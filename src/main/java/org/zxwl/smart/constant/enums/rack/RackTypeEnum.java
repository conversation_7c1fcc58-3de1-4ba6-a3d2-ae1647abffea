package org.zxwl.smart.constant.enums.rack;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 密集架类型 */
@Getter
@AllArgsConstructor
public enum RackTypeEnum implements BaseEnum {
  PU_TONG("普通密集架", false),
  ZHI_NENG("智能密集架", false),
  HE_DING_WEI("盒定位密集架", true),
  FA_GUANG("发光密集架", false);

  private final String value;

  /** 是否存在格口 */
  private final boolean gridFlag;

  public Boolean getGridFlag() {
    return gridFlag;
  }

  public static RackTypeEnum getEnumByValue(String value) {
    for (RackTypeEnum rackTypeEnum : RackTypeEnum.values()) {
      if (rackTypeEnum.getValue().equals(value)) {
        return rackTypeEnum;
      }
    }
    return null;
  }
}
