package org.zxwl.smart.constant.enums.inventory;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 盘点类型枚举 */
@Getter
@AllArgsConstructor
public enum InventoryRecordTypeEnum implements BaseEnum {
  // 手持机
  HANDHELD("handheld"),
  // 盘点车
  INVENTORY_CAR("inventory_car"),
  // 智能柜
  CABINET("cabinet"),
  // 密集架
  RACK("rack");

  private final String value;
}
