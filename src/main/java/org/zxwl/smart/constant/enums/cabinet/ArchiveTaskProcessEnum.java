package org.zxwl.smart.constant.enums.cabinet;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 任务过程 */
@Getter
@AllArgsConstructor
public enum ArchiveTaskProcessEnum implements BaseEnum {
  // 审批通过
  APPROVAL_PASS("approvalPass"),
  // 开始取档
  UNSHELVE_BEGIN("unshelveBegin"),
  // 完成取档
  UNSHELVE_FINISH("unshelveFinish");

  // todo 目前任务执行过程还不明确，后面再完善

  // region 桁架/AGV
  // endregion

  // region 智能档案柜、盒定位密集架
  // endregion

  // region 常规智能密集架
  // endregion

  // region 人工确认
  // endregion

  private final String value;
}
