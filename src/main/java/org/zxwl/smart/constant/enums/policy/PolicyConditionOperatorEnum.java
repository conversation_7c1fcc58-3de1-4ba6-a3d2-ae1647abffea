package org.zxwl.smart.constant.enums.policy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 策略条件比较枚举 */
@Getter
@AllArgsConstructor
public enum PolicyConditionOperatorEnum implements BaseEnum {
  EQUAL_TO("="),
  GREATER_THAN(">"),
  GREATER_THAN_OR_EQUAL_TO(">="),
  LESS_THAN("<"),
  LESS_THAN_OR_EQUAL_TO("<=");

  private final String value;
}
