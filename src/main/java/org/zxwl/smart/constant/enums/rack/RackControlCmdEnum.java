package org.zxwl.smart.constant.enums.rack;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

@Getter
@AllArgsConstructor
public enum RackControlCmdEnum implements BaseEnum {
  // 开架
  OPEN("open"),
  // 闭架
  CLOSE("close"),
  // 通风
  VENTILATE("ventilate"),
  // 解锁
  UNLOCK("unlock"),
  // 锁定
  LOCK("lock"),
  // 停止
  STOP("stop");

  private final String value;
}
