package org.zxwl.smart.constant.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 借阅记录借阅目的 */
@Getter
@AllArgsConstructor
public enum ArchiveBorrowIntentEnum implements BaseEnum {
  // 内部查阅
  INTERNAL("internal"),
  // 外部查阅
  EXTERNAL("external"),
  // 移交
  TRANSFER("transfer"),
  // 出库
  OUTBOUND("outbound");

  private final String value;
}
