package org.zxwl.smart.constant.enums.env;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

@Getter
@AllArgsConstructor
public enum EnvDeviceGroupEnum implements BaseEnum {
  PERCEPTION("感知类设备", 1),
  CONTROL("控制类设备", 2),
  SECURITY("安防类设备", 4),
  DOOR_CONTROL("门禁类设备", 8);

  private final String value;

  private final Integer code;

  public static List<String> getValuesByCode(Integer code) {
    return Arrays.stream(values())
        .filter(e -> (e.getCode() & code) != 0)
        .map(EnvDeviceGroupEnum::getValue)
        .collect(Collectors.toList());
  }
}
