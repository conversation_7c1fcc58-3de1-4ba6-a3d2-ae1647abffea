package org.zxwl.smart.constant.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.zxwl.smart.constant.enums.BaseEnum;

/** 档案字段配置固定列名字枚举 */
@Getter
@AllArgsConstructor
public enum ArchiveFixedFieldEnum implements BaseEnum {
  // 名字
  ARCHIVE_NAME("archiveName", "文件名", 1, true, true, true, true, true),
  // 编号
  ARCHIVE_NO("archiveNo", "文件编号", 2, true, true, true, true, true),
  // 创建日期
  CREATE_DATE("createdAt", "创建时间", 3, true, false, false, false, false),
  // 装盒状态
  BOX_STATUS("boxStatus", "装盒状态", 4, true, false, false, false, false),
  // 在库状态
  STOCK_STATUS("stockStatus", "在库状态", 5, true, false, false, false, false),
  // 在架状态
  SHELVING_STATUS("shelvingStatus", "在架状态", 6, true, false, false, false, false),
  // 位置
  LOCATION("location", "位置", 7, true, true, false, false, false),
  // 卷内档案数
  SUB_ARCHIVE_COUNT("subArchiveCount", "卷内档案数", 8, true, false, false, false, false);
  // 字段属性名
  private final String value;
  // 默认字段名
  private final String defaultFieldName;
  // 字段排序
  private final Integer fieldOrdinal;
  // 是否列表展示，0不展示，1展示
  private final Boolean showList;
  // 是否支持录入，0不支持，1支持
  private final Boolean input;
  // 是否是必填，0不必填，1必填
  private final Boolean required;
  // 是否支持搜索，0不支持，1支持
  private final Boolean search;
  // 是否支持高级搜索，0不支持，1支持
  private final Boolean adSearch;
}
