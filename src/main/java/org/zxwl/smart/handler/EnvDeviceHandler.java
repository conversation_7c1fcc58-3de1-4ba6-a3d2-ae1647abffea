package org.zxwl.smart.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;
import org.zxwl.smart.cache.*;
import org.zxwl.smart.cache.model.*;
import org.zxwl.smart.client.hk.ConnectionFunction;
import org.zxwl.smart.client.hk.enums.*;
import org.zxwl.smart.common.utils.ColUtil;
import org.zxwl.smart.common.utils.EnvDataUtil;
import org.zxwl.smart.common.utils.PolicyUtil;
import org.zxwl.smart.constant.consist.WebsocketConstant;
import org.zxwl.smart.domain.response.env.EnvDataVO;
import org.zxwl.smart.domain.response.ws.WSAreaDataChangedVO;
import org.zxwl.smart.domain.response.ws.WSDeviceDataChangedVO;

@Slf4j
@Component
@AllArgsConstructor
public class EnvDeviceHandler implements ConnectionFunction {

  private final EnvDataCache envDataCache;
  private final PolicyCache policyCache;
  private final SimpMessagingTemplate messagingTemplate;

  @Override
  public String getDeviceType(String ip, Byte serialPort, Byte serialAddr) {
    EnvDeviceCacheModel cacheModel = envDataCache.getDevice(ip, serialPort, serialAddr);
    return Objects.nonNull(cacheModel) ? cacheModel.getDeviceType() : null;
  }

  @Override
  public <T> T getFieldValue(
      String ip,
      Byte serialPort,
      Byte serialAddr,
      String fieldName,
      Class<T> clazz,
      T defaultValue) {
    EnvDeviceCacheModel cacheModel = envDataCache.getDevice(ip, serialPort, serialAddr);
    if (Objects.nonNull(cacheModel)) {
      return MapUtil.get(cacheModel.getFieldValuesMap(), fieldName, clazz, defaultValue);
    }
    return null;
  }

  @Override
  public void handleDeviceMap(
      String ip, Byte serialPort, Byte serialAddr, Map<String, Object> map) {
    if (ServerConfigCache.isHkLogEnabled()) {
      log.debug(
          "解析环控设备数据完成: ip = {} serialPort = {} serialAddr = {} map = {}",
          ip,
          serialPort,
          serialAddr,
          map);
    }
    EnvDeviceCacheModel cacheModel = envDataCache.getDevice(ip, serialPort, serialAddr);
    if (Objects.isNull(cacheModel)) {
      return;
    }
    cacheModel.setLastReceiveDataTime(System.currentTimeMillis());
    cacheModel.setOnline(true);
    Map<String, Object> fieldValuesMap = cacheModel.getFieldValuesMap();
    Set<String> changeKeys = ColUtil.mergeAndGetChanges(fieldValuesMap, map);
    if (CollUtil.isEmpty(changeKeys)) {
      return;
    }
    // 处理设备数据变化
    handleDeviceDataChanged(cacheModel);
    // 处理区域数据变化
    handleDeviceAreaDataChanged(cacheModel);
    // 处理库房及全馆的数据变化
    handleWarehouseDataChanged(cacheModel);
  }

  /** 处理设备数据变化，发送Websocket通知 */
  public void handleDeviceDataChanged(EnvDeviceCacheModel cacheModel) {
    String deviceType = cacheModel.getDeviceType();
    EnvDeviceTypeEnum deviceTypeEnum = EnvDeviceTypeEnum.getByValue(deviceType);
    if (Objects.isNull(deviceTypeEnum)) {
      return;
    }
    Class<? extends EnvFieldEnum> fieldEnumClass = deviceTypeEnum.getFieldEnumClass();
    if (Objects.isNull(fieldEnumClass)) {
      return;
    }
    // 更新缓存数据
    WSDeviceDataChangedVO vo = new WSDeviceDataChangedVO();
    vo.setId(cacheModel.getId());
    vo.setOnline(cacheModel.isOnline());
    // 发送websocket通知
    List<WSDeviceDataChangedVO.FieldVO> fieldList = new ArrayList<>();
    for (EnvFieldEnum fieldEnum : fieldEnumClass.getEnumConstants()) {
      String name = fieldEnum.getName();
      Object value = cacheModel.getFieldValuesMap().get(name);
      String displayValue = null;
      Class<? extends EnvFieldValueMapperEnum> mapperEnumClass =
          fieldEnum.getValueMapperEnumClass();
      if (Objects.nonNull(mapperEnumClass)) {
        for (EnvFieldValueMapperEnum mapperEnum : mapperEnumClass.getEnumConstants()) {
          if (mapperEnum.equalValue(value)) {
            displayValue = mapperEnum.getDisplayValue();
            break;
          }
        }
      }
      String unit = fieldEnum.getUnit();
      fieldList.add(
          new WSDeviceDataChangedVO.FieldVO(
              name, StrUtil.toStringOrNull(value), displayValue, unit));
    }
    vo.setDeviceFieldList(fieldList);
    messagingTemplate.convertAndSend(WebsocketConstant.DEVICE_DATA_CHANGED, vo);
  }

  /** 处理区域数据变化，发送Websocket通知 */
  public void handleDeviceAreaDataChanged(EnvDeviceCacheModel cacheModel) {
    if (!EnvDeviceTypeEnum.WSDCGQ.equalsValue(cacheModel.getDeviceType())
        && !EnvDeviceTypeEnum.YCY.equalsValue(cacheModel.getDeviceType())
        && !EnvDeviceTypeEnum.NHCJQ.equalsValue(cacheModel.getDeviceType())) {
      return;
    }
    List<AreaCacheModel> deviceAreaList = envDataCache.getAreaListByDeviceId(cacheModel.getId());
    if (CollUtil.isEmpty(deviceAreaList)) {
      return;
    }
    for (AreaCacheModel areaCacheModel : deviceAreaList) {
      Set<Long> deviceIdSet = areaCacheModel.getDeviceIdSet();
      List<EnvDeviceCacheModel> deviceCacheList = envDataCache.getDeviceListByIdSet(deviceIdSet);
      List<Float> temperatureList = new ArrayList<>();
      List<Float> humidityList = new ArrayList<>();
      List<Float> co2List = new ArrayList<>();
      List<Float> tvocList = new ArrayList<>();
      List<Float> pm10List = new ArrayList<>();
      List<Float> pm25List = new ArrayList<>();
      List<Float> voltageList = new ArrayList<>();
      List<Float> currentList = new ArrayList<>();
      List<Float> powerList = new ArrayList<>();
      for (EnvDeviceCacheModel device : deviceCacheList) {
        if (!device.isOnline()) continue;
        Map<String, Object> fieldValuesMap = device.getFieldValuesMap();
        String deviceType = device.getDeviceType();
        if (EnvDeviceTypeEnum.WSDCGQ.equalsValue(deviceType)) {
          // 取温湿度传感器的温湿度
          temperatureList.add(
              MapUtil.get(fieldValuesMap, WSDCGQFieldEnum.TEMPERATURE.getName(), Float.class));
          humidityList.add(
              MapUtil.get(fieldValuesMap, WSDCGQFieldEnum.HUMIDITY.getName(), Float.class));
        } else if (EnvDeviceTypeEnum.YCY.equalsValue(deviceType)) {
          // 取云测仪的空气质量
          // ps: 暂时手动排除掉云测仪采集温湿度为0的数据
          Float temperature =
              MapUtil.get(fieldValuesMap, YCYFieldEnum.TEMPERATURE.getName(), Float.class);
          Float humidity =
              MapUtil.get(fieldValuesMap, YCYFieldEnum.HUMIDITY.getName(), Float.class);
          if (!Objects.equals(temperature, 0.0f) || !Objects.equals(humidity, 0.0f)) {
            temperatureList.add(temperature);
            humidityList.add(humidity);
          }
          co2List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.CO2.getName(), Float.class));
          tvocList.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.TVOC.getName(), Float.class));
          pm10List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.PM10.getName(), Float.class));
          pm25List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.PM25.getName(), Float.class));
        } else if (EnvDeviceTypeEnum.NHCJQ.equalsValue(deviceType)) {
          // 取能耗控制器的能耗数据
          voltageList.add(
              MapUtil.get(fieldValuesMap, NHCJQFieldEnum.VOLTAGE.getName(), Float.class));
          currentList.add(
              MapUtil.get(fieldValuesMap, NHCJQFieldEnum.CURRENT.getName(), Float.class));
          powerList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.POWER.getName(), Float.class));
        }
      }
      areaCacheModel.setTemperature(EnvDataUtil.calculateIQR(temperatureList));
      areaCacheModel.setHumidity(EnvDataUtil.calculateIQR(humidityList));
      areaCacheModel.setCo2(EnvDataUtil.calculateIQR(co2List));
      areaCacheModel.setTvoc(EnvDataUtil.calculateIQR(tvocList));
      areaCacheModel.setPm10(EnvDataUtil.calculateIQR(pm10List));
      areaCacheModel.setPm25(EnvDataUtil.calculateIQR(pm25List));
      areaCacheModel.setVoltage(EnvDataUtil.calculateIQR(voltageList));
      areaCacheModel.setCurrent(EnvDataUtil.calculateIQR(currentList));
      areaCacheModel.setPower(EnvDataUtil.calculateIQR(powerList));
      // 发送websocket通知
      WSAreaDataChangedVO vo = new WSAreaDataChangedVO();
      vo.setId(areaCacheModel.getId());
      vo.setTemperature(areaCacheModel.getTemperature());
      vo.setHumidity(areaCacheModel.getHumidity());
      vo.setCo2(areaCacheModel.getCo2());
      vo.setTvoc(areaCacheModel.getTvoc());
      vo.setPm10(areaCacheModel.getPm10());
      vo.setPm25(areaCacheModel.getPm25());
      vo.setVoltage(areaCacheModel.getVoltage());
      vo.setCurrent(areaCacheModel.getCurrent());
      vo.setPower(areaCacheModel.getPower());
      messagingTemplate.convertAndSend(WebsocketConstant.DEVICE_AREA_DATA_CHANGED, vo);
      // 十防策略检查
      checkPolicy(areaCacheModel);
    }
  }

  private void checkPolicy(AreaCacheModel areaCacheModel) {
    Long id = areaCacheModel.getId();
    Float temperature = areaCacheModel.getTemperature();
    Float humidity = areaCacheModel.getHumidity();
    Float co2 = areaCacheModel.getCo2();
    Float tvoc = areaCacheModel.getTvoc();
    Float pm10 = areaCacheModel.getPm10();
    Float pm25 = areaCacheModel.getPm25();
    // 根据区域id获取对应的策略列表
    List<PolicyCacheModel> policyList = policyCache.getPolicy(id);
    if (CollUtil.isEmpty(policyList)) {
      return;
    }
    for (PolicyCacheModel policy : policyList) {
      if (!policy.getEnabled()) {
        continue;
      }
      // 检查策略是否在执行范围内
      if (!PolicyUtil.checkPolicyPeriod(policy.getPeriodList())) {
        continue;
      }
      // 检查策略条件是否满足
      if (!PolicyUtil.checkPolicyCondition(
          policy.getConditionList(), temperature, humidity, co2, tvoc, pm10, pm25)) {
        continue;
      }
      // 执行策略动作
      // executePolicyAction(policy);
    }
  }

  /** 处理库房及全馆的数据变化，发送Websocket通知 */
  public void handleWarehouseDataChanged(EnvDeviceCacheModel cacheModel) {
    if (!EnvDeviceTypeEnum.WSDCGQ.equalsValue(cacheModel.getDeviceType())
        && !EnvDeviceTypeEnum.YCY.equalsValue(cacheModel.getDeviceType())
        && !EnvDeviceTypeEnum.NHCJQ.equalsValue(cacheModel.getDeviceType())) {
      return;
    }
    // 计算全馆数据，更新缓存，并走websocket推送
    List<EnvDeviceCacheModel> deviceAllList = envDataCache.getAllDevice();
    List<Float> temperatureList = new ArrayList<>();
    List<Float> humidityList = new ArrayList<>();
    List<Float> co2List = new ArrayList<>();
    List<Float> tvocList = new ArrayList<>();
    List<Float> pm10List = new ArrayList<>();
    List<Float> pm25List = new ArrayList<>();
    List<Float> voltageList = new ArrayList<>();
    List<Float> currentList = new ArrayList<>();
    List<Float> powerList = new ArrayList<>();
    for (EnvDeviceCacheModel device : deviceAllList) {
      if (!device.isOnline()) continue;
      String deviceType = device.getDeviceType();
      Map<String, Object> fieldValuesMap = device.getFieldValuesMap();
      if (EnvDeviceTypeEnum.WSDCGQ.equalsValue(deviceType)) {
        // 取温湿度传感器的温湿度
        temperatureList.add(
            MapUtil.get(fieldValuesMap, WSDCGQFieldEnum.TEMPERATURE.getName(), Float.class));
        humidityList.add(
            MapUtil.get(fieldValuesMap, WSDCGQFieldEnum.HUMIDITY.getName(), Float.class));
      } else if (EnvDeviceTypeEnum.YCY.equalsValue(deviceType)) {
        // 取云测仪的空气质量
        // ps: 暂时手动排除掉云测仪采集温湿度为0的数据
        Float temperature =
            MapUtil.get(fieldValuesMap, YCYFieldEnum.TEMPERATURE.getName(), Float.class);
        Float humidity = MapUtil.get(fieldValuesMap, YCYFieldEnum.HUMIDITY.getName(), Float.class);
        if (!Objects.equals(temperature, 0.0f) || Objects.equals(humidity, 0.0f)) {
          temperatureList.add(temperature);
          humidityList.add(humidity);
        }
        co2List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.CO2.getName(), Float.class));
        tvocList.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.TVOC.getName(), Float.class));
        pm10List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.PM10.getName(), Float.class));
        pm25List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.PM25.getName(), Float.class));
      } else if (EnvDeviceTypeEnum.NHCJQ.equalsValue(deviceType)) {
        // 取能耗控制器的能耗数据
        voltageList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.VOLTAGE.getName(), Float.class));
        currentList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.CURRENT.getName(), Float.class));
        powerList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.POWER.getName(), Float.class));
      }
    }
    // 更新缓存
    BuildingCacheModel buildingCacheModel = envDataCache.getBuilding();
    buildingCacheModel.setTemperature(EnvDataUtil.calculateIQR(temperatureList));
    buildingCacheModel.setHumidity(EnvDataUtil.calculateIQR(humidityList));
    buildingCacheModel.setCo2(EnvDataUtil.calculateIQR(co2List));
    buildingCacheModel.setTvoc(EnvDataUtil.calculateIQR(tvocList));
    buildingCacheModel.setPm10(EnvDataUtil.calculateIQR(pm10List));
    buildingCacheModel.setPm25(EnvDataUtil.calculateIQR(pm25List));
    buildingCacheModel.setVoltage(EnvDataUtil.calculateIQR(voltageList));
    buildingCacheModel.setCurrent(EnvDataUtil.calculateIQR(currentList));
    buildingCacheModel.setPower(EnvDataUtil.calculateIQR(powerList));
    // 发送Websocket推送
    EnvDataVO vo = new EnvDataVO();
    vo.setWarehouseId(null);
    vo.setTemperature(buildingCacheModel.getTemperature());
    vo.setHumidity(buildingCacheModel.getHumidity());
    vo.setCo2(buildingCacheModel.getCo2());
    vo.setTvoc(buildingCacheModel.getTvoc());
    vo.setPm10(buildingCacheModel.getPm10());
    vo.setPm25(buildingCacheModel.getPm25());
    vo.setVoltage(EnvDataUtil.calculateIQR(voltageList));
    vo.setCurrent(EnvDataUtil.calculateIQR(currentList));
    vo.setPower(EnvDataUtil.calculateIQR(powerList));
    messagingTemplate.convertAndSend(WebsocketConstant.DEVICE_WAREHOUSE_DATA_CHANGED, vo);
    // 计算指定库房的，更新缓存，并走websocket推送
    Long warehouseId = cacheModel.getWarehouseId();
    List<EnvDeviceCacheModel> warehouseDeviceList =
        deviceAllList.stream()
            .filter(device -> Objects.equals(device.getWarehouseId(), warehouseId))
            .toList();
    temperatureList.clear();
    humidityList.clear();
    co2List.clear();
    tvocList.clear();
    pm10List.clear();
    pm25List.clear();
    voltageList.clear();
    currentList.clear();
    powerList.clear();
    for (EnvDeviceCacheModel device : warehouseDeviceList) {
      if (!device.isOnline()) continue;
      String deviceType = device.getDeviceType();
      Map<String, Object> fieldValuesMap = device.getFieldValuesMap();
      if (EnvDeviceTypeEnum.WSDCGQ.equalsValue(deviceType)) {
        // 取温湿度传感器的温湿度
        temperatureList.add(
            MapUtil.get(fieldValuesMap, WSDCGQFieldEnum.TEMPERATURE.getName(), Float.class));
        humidityList.add(
            MapUtil.get(fieldValuesMap, WSDCGQFieldEnum.HUMIDITY.getName(), Float.class));
      } else if (EnvDeviceTypeEnum.YCY.equalsValue(deviceType)) {
        // 取云测仪的空气质量
        // ps: 暂时手动排除掉云测仪采集温湿度为0的数据
        Float temperature =
            MapUtil.get(fieldValuesMap, YCYFieldEnum.TEMPERATURE.getName(), Float.class);
        if (Objects.nonNull(temperature) && !temperature.equals(0f)) {
          temperatureList.add(temperature);
        }
        Float humidity = MapUtil.get(fieldValuesMap, YCYFieldEnum.HUMIDITY.getName(), Float.class);
        if (Objects.nonNull(humidity) && !humidity.equals(0f)) {
          humidityList.add(humidity);
        }
        co2List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.CO2.getName(), Float.class));
        tvocList.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.TVOC.getName(), Float.class));
        pm10List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.PM10.getName(), Float.class));
        pm25List.add(MapUtil.get(fieldValuesMap, YCYFieldEnum.PM25.getName(), Float.class));
      } else if (EnvDeviceTypeEnum.NHCJQ.equalsValue(deviceType)) {
        // 取能耗控制器的能耗数据
        voltageList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.VOLTAGE.getName(), Float.class));
        currentList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.CURRENT.getName(), Float.class));
        powerList.add(MapUtil.get(fieldValuesMap, NHCJQFieldEnum.POWER.getName(), Float.class));
      }
    }
    // 更新缓存
    WarehouseCacheModel warehouseCacheModel = envDataCache.getWarehouse(warehouseId);
    warehouseCacheModel.setTemperature(EnvDataUtil.calculateIQR(temperatureList));
    warehouseCacheModel.setHumidity(EnvDataUtil.calculateIQR(humidityList));
    warehouseCacheModel.setCo2(EnvDataUtil.calculateIQR(co2List));
    warehouseCacheModel.setTvoc(EnvDataUtil.calculateIQR(tvocList));
    warehouseCacheModel.setPm10(EnvDataUtil.calculateIQR(pm10List));
    warehouseCacheModel.setPm25(EnvDataUtil.calculateIQR(pm25List));
    warehouseCacheModel.setVoltage(EnvDataUtil.calculateIQR(voltageList));
    warehouseCacheModel.setCurrent(EnvDataUtil.calculateIQR(currentList));
    warehouseCacheModel.setPower(EnvDataUtil.calculateIQR(powerList));
    // 发送Websocket推送
    EnvDataVO warehouseVo = new EnvDataVO();
    warehouseVo.setWarehouseId(warehouseId);
    warehouseVo.setTemperature(warehouseCacheModel.getTemperature());
    warehouseVo.setHumidity(warehouseCacheModel.getHumidity());
    warehouseVo.setCo2(warehouseCacheModel.getCo2());
    warehouseVo.setTvoc(warehouseCacheModel.getTvoc());
    warehouseVo.setPm10(warehouseCacheModel.getPm10());
    warehouseVo.setPm25(warehouseCacheModel.getPm25());
    warehouseVo.setVoltage(warehouseCacheModel.getVoltage());
    warehouseVo.setCurrent(warehouseCacheModel.getCurrent());
    warehouseVo.setPower(warehouseCacheModel.getPower());
    messagingTemplate.convertAndSend(WebsocketConstant.DEVICE_WAREHOUSE_DATA_CHANGED, warehouseVo);
  }
}
