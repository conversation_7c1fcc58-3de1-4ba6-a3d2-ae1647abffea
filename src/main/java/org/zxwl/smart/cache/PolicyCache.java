package org.zxwl.smart.cache;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;
import org.zxwl.smart.cache.model.PolicyCacheModel;

/** 策略缓存类 */
@Component
public class PolicyCache {

  private final ConcurrentHashMap<Long, List<PolicyCacheModel>> areaPolicyMap;

  public PolicyCache() {
    areaPolicyMap = new ConcurrentHashMap<>();
  }

  public void putPolicy(Long areaId, List<PolicyCacheModel> policyList) {
    areaPolicyMap.put(areaId, policyList);
  }

  public List<PolicyCacheModel> getPolicy(Long areaId) {
    return areaPolicyMap.get(areaId);
  }

  public void removePolicy(Long areaId) {
    areaPolicyMap.remove(areaId);
  }
}
