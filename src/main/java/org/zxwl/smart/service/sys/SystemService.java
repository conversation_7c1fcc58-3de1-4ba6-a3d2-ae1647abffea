package org.zxwl.smart.service.sys;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.sys.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.sys.MenuVO;

@Validated
public interface SystemService {

  ListResult<MenuVO> getMenuList();

  ListResult<MenuVO> getUserMenuList(@NotNull @Valid UserMenuListRequest request);

  BaseResult createMenu(@NotNull @Valid MenuCreateRequest request);

  BaseResult updateMenu(@NotNull @Valid MenuUpdateRequest request);

  BaseResult deleteMenu(@NotNull @Valid MenuDeleteRequest request);
}
