package org.zxwl.smart.service.sys.impl;

import cn.hutool.core.collection.CollUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.sys.MenuConvert;
import org.zxwl.smart.domain.request.sys.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.sys.MenuVO;
import org.zxwl.smart.mybatis.entity.sys.SysMenu;
import org.zxwl.smart.mybatis.mapper.sys.MenuRoleMapper;
import org.zxwl.smart.mybatis.mapper.sys.RoleMapper;
import org.zxwl.smart.mybatis.mapper.sys.SysMenuMapper;
import org.zxwl.smart.mybatis.mapper.user.UserRoleMapper;
import org.zxwl.smart.service.sys.SystemService;

@Service
@AllArgsConstructor
public class SystemServiceImpl implements SystemService {

  private final SysMenuMapper sysMenuMapper;

  private final UserRoleMapper userRoleMapper;

  private final MenuRoleMapper menuRoleMapper;

  private final RoleMapper roleMapper;

  @Override
  public ListResult<MenuVO> getMenuList() {
    List<SysMenu> menuList = sysMenuMapper.selectList();
    if (Objects.isNull(menuList)) {
      return new ListResult<>("菜单列表为空");
    }
    List<MenuVO> menuVOList =
        menuList.stream().map(MenuConvert::getMenuVO).collect(Collectors.toList());
    List<MenuVO> treeList = buildMenuTree(menuVOList);
    return new ListResult<>(treeList);
  }

  @Override
  public ListResult<MenuVO> getUserMenuList(UserMenuListRequest request) {
    Long userId = request.getUserId();
    Long organizationId = request.getOrganizationId();
    // 查询用户的角色
    List<Long> roleIdList = userRoleMapper.selectRoleIdListByUserIdAndOrgId(userId, organizationId);
    if (Objects.isNull(roleIdList)) {
      return new ListResult<>("用户没有分配角色");
    }
    // 查询角色关联菜单id列表
    List<Long> menuIdList = menuRoleMapper.selectMenuIdListByRoleIdList(roleIdList);
    if (CollUtil.isEmpty(menuIdList)) {
      return new ListResult<>("角色没有分配菜单");
    }
    // 查询菜单详情
    List<SysMenu> userMenuList = sysMenuMapper.selectListByIdList(menuIdList);
    if (Objects.isNull(userMenuList)) {
      return new ListResult<>("用户无可访问菜单");
    }
    // 返回
    List<MenuVO> menuVOList =
        userMenuList.stream().map(MenuConvert::getMenuVO).collect(Collectors.toList());
    List<MenuVO> treeList = buildMenuTree(menuVOList);
    return new ListResult<>(treeList);
  }

  @Override
  public BaseResult createMenu(MenuCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    SysMenu menu = MenuConvert.getMenu(request);
    menu.setCreatedId(userId);
    menu.setUpdatedId(userId);
    sysMenuMapper.insert(menu);
    return BaseResult.success();
  }

  @Override
  public BaseResult updateMenu(MenuUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    if (Objects.isNull(sysMenuMapper.selectById(id))) {
      return BaseResult.failed("菜单不存在");
    }
    SysMenu menu = MenuConvert.getMenu(request);
    menu.setUpdatedId(userId);
    sysMenuMapper.update(menu);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteMenu(MenuDeleteRequest request) {
    Long id = request.getId();
    if (Objects.isNull(sysMenuMapper.selectById(id))) {
      return BaseResult.failed("菜单不存在");
    }
    // 检查菜单下是否存在子菜单
    List<SysMenu> sysMenuList = sysMenuMapper.selectByParentId(id);
    if (CollUtil.isNotEmpty(sysMenuList)) {
      return BaseResult.failed("该菜单下存在子菜单，不能直接删除");
    }
    sysMenuMapper.deleteById(id, System.currentTimeMillis());
    return BaseResult.success();
  }

  /** 构建菜单树 */
  private List<MenuVO> buildMenuTree(List<MenuVO> menuVOList) {
    if (Objects.isNull(menuVOList)) {
      return new ArrayList<>();
    }
    Map<Long, MenuVO> menuMap =
        menuVOList.stream().collect(Collectors.toMap(MenuVO::getId, menu -> menu));
    List<MenuVO> rootMenuList = new ArrayList<>();
    for (MenuVO menuVO : menuVOList) {
      if (Objects.isNull(menuVO.getParentId()) || menuVO.getParentId() == 0) {
        rootMenuList.add(menuVO);
      } else {
        MenuVO parentMenu = menuMap.get(menuVO.getParentId());
        if (Objects.nonNull(parentMenu)) {
          if (Objects.isNull(parentMenu.getChildren())) {
            parentMenu.setChildren(new ArrayList<>());
          }
          parentMenu.getChildren().add(menuVO);
        }
      }
    }
    sortChildren(rootMenuList);
    return rootMenuList;
  }

  private void sortChildren(List<MenuVO> rootMenuList) {
    if (Objects.isNull(rootMenuList)) {
      return;
    }
    for (MenuVO menuVO : rootMenuList) {
      if (Objects.nonNull(menuVO.getChildren()) && !menuVO.getChildren().isEmpty()) {
        menuVO
            .getChildren()
            .sort(
                Comparator.comparing(MenuVO::getOrdinal, Comparator.nullsLast(Integer::compareTo)));
        sortChildren(menuVO.getChildren());
      }
    }
  }
}
