package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.archive.*;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.*;

@Validated
public interface ArchiveStatsService {

  SimpleResult<ArchiveStockStatVO> getArchiveStockStat(
      @NotNull @Valid ArchiveStockStatRequest request);

  SimpleResult<ArchiveShelvingStatVO> getArchiveShelvingStat(
      @NotNull @Valid ArchiveShelvingStatRequest request);

  ListResult<ArchiveTypeStatVO> getArchiveTypeStat(@NotNull @Valid ArchiveTypeStatRequest request);

  SimpleResult<ArchiveAccessStatVO> getArchiveAccessStat(
      @NotNull @Valid ArchiveAccessStatRequest request);

  ListResult<ArchiveStockStatListVO> getArchiveStockStatList(
      @NotNull @Valid ArchiveStockStatListRequest request);
}
