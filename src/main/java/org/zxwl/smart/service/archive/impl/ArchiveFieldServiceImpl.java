package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ArchiveFieldConvert;
import org.zxwl.smart.domain.request.archive.ArchiveFieldCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldListRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.ArchiveFieldListVO;
import org.zxwl.smart.mybatis.entity.archive.ArchiveField;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveFieldMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper;
import org.zxwl.smart.service.archive.ArchiveFieldService;

@Service
@AllArgsConstructor
public class ArchiveFieldServiceImpl implements ArchiveFieldService {

  private final ArchiveFieldMapper archiveFieldMapper;
  private final ArchiveTypeMapper archiveTypeMapper;

  @Override
  public ListResult<ArchiveFieldListVO> getArchiveFieldList(ArchiveFieldListRequest request) {
    Long archiveTypeId = request.getArchiveTypeId();
    List<ArchiveField> list = archiveFieldMapper.selectListByArchiveTypeId(archiveTypeId);
    List<Long> archiveTypeIdList =
        list.stream()
            .map(ArchiveField::getArchiveTypeId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, ArchiveType> archiveTypeMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(archiveTypeIdList)) {
      List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
      archiveTypeMap =
          archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    }
    List<ArchiveFieldListVO> voList = ArchiveFieldConvert.getArchiveFieldListVOList(list);
    for (ArchiveFieldListVO vo : voList) {
      ArchiveType archiveType = archiveTypeMap.get(vo.getArchiveTypeId());
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    voList.sort(Comparator.comparingInt(ArchiveFieldListVO::getFieldOrdinal));
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createArchiveField(ArchiveFieldCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long archiveTypeId = request.getArchiveTypeId();
    if (Objects.nonNull(archiveTypeId) && archiveTypeMapper.existById(archiveTypeId) == 0) {
      return new SimpleResult<>("门类不存在");
    }
    ArchiveField archiveField = ArchiveFieldConvert.getArchiveField(request);
    archiveField.setCreatedId(userId);
    archiveField.setUpdatedId(userId);
    archiveFieldMapper.insert(archiveField);
    return new SimpleResult<>(new BaseCreateVO(archiveField.getId()));
  }

  @Override
  public BaseResult updateArchiveField(ArchiveFieldUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Long archiveTypeId = request.getArchiveTypeId();
    ArchiveField orgArchiveField = archiveFieldMapper.selectById(id);
    if (Objects.isNull(orgArchiveField)) {
      return BaseResult.failed("档案字段不存在");
    }
    if (Objects.nonNull(archiveTypeId)
        && !archiveTypeId.equals(orgArchiveField.getArchiveTypeId())) {
      if (archiveTypeMapper.existById(archiveTypeId) > 0) {
        return BaseResult.failed("门类不存在");
      }
    }
    ArchiveField archiveField = ArchiveFieldConvert.getArchiveField(request);
    archiveField.setUpdatedId(userId);
    archiveFieldMapper.updateById(archiveField);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArchiveField(ArchiveFieldDeleteRequest request) {
    Long id = request.getId();
    ArchiveField orgArchiveField = archiveFieldMapper.selectById(id);
    if (Objects.isNull(orgArchiveField)) {
      return BaseResult.failed("档案字段不存在");
    }
    Long archiveTypeId = orgArchiveField.getArchiveTypeId();
    if (archiveFieldMapper.countByArchiveTypeId(archiveTypeId) <= 1) {
      return BaseResult.failed("该门类下至少保留一个字段，不能删除");
    }
    String fixedFieldName = orgArchiveField.getFixedFieldName();
    if (Objects.nonNull(fixedFieldName)) {
      return BaseResult.failed("固定列字段不能删除");
    }
    archiveFieldMapper.deleteById(id, System.currentTimeMillis());
    return BaseResult.success();
  }
}
