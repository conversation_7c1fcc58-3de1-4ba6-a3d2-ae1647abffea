package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentRequest;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.archive.ArchiveAttachmentVO;

@Validated
public interface ArchiveAttachmentService {

  ListResult<ArchiveAttachmentVO> getArchiveAttachmentList(
      @NotNull @Valid ArchiveAttachmentRequest request);

  BaseResult createArchiveAttachment(@NotNull @Valid ArchiveAttachmentCreateRequest request);

  BaseResult updateArchiveAttachment(@NotNull @Valid ArchiveAttachmentUpdateRequest request);

  BaseResult deleteArchiveAttachment(@NotNull @Valid ArchiveAttachmentDeleteRequest request);
}
