package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ApprovalConvert;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.constant.enums.archive.*;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.pickup.ApprovedListVO;
import org.zxwl.smart.domain.response.pickup.ApprovedVO;
import org.zxwl.smart.domain.response.pickup.PendingApprovalListVO;
import org.zxwl.smart.domain.response.pickup.PendingApprovalVO;
import org.zxwl.smart.mybatis.entity.archive.*;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApprovalExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApprovalWrapper;
import org.zxwl.smart.mybatis.ext.archive.PendingApprovalExt;
import org.zxwl.smart.mybatis.ext.archive.PendingApprovalWrapper;
import org.zxwl.smart.mybatis.mapper.archive.*;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.service.archive.ArchiveApprovalService;
import org.zxwl.smart.service.archive.ArchiveTaskService;

@Service
@AllArgsConstructor
public class ArchiveApprovalServiceImpl implements ArchiveApprovalService {

  private final ArchiveApprovalMapper archiveApprovalMapper;
  private final ArchiveApplicantMapper archiveApplicantMapper;
  private final ArchiveMapper archiveMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final UserMapper userMapper;
  private final ArchiveTaskService archiveTaskService;
  private final TransactionTemplate transactionTemplate;

  @Override
  public PageResult<PendingApprovalListVO> getArchivePendingApprovalList(
      PendingApprovalListRequest request) {
    // Long userId = AccountLocal.getUserId();
    List<Date> createAtRange = request.getApplicantCreateAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    // todo: 这里需要判断自己是否是具有审核权限
    PendingApprovalWrapper wrapper = ApprovalConvert.getPendingApprovalWrapper(request);
    wrapper.setStatus(ApplicantStatusEnum.PENDING.getValue());
    if (Objects.nonNull(createAtRange)) {
      wrapper.setBeginCreateAt(createAtRange.get(0));
      wrapper.setEndCreateAt(createAtRange.get(1));
    }
    PageHelper.startPage(pageNum, pageSize);
    List<PendingApprovalExt> applicantList =
        archiveApplicantMapper.selectPendingApprovalList(wrapper);
    List<PendingApprovalListVO> voList = getPendingApprovalListVOList(applicantList);
    return new PageResult<>(voList, applicantList);
  }

  @Override
  public SimpleResult<PendingApprovalVO> getArchivePendingApproval(PendingApprovalRequest request) {
    Long id = request.getId();
    ArchiveApplicant applicant = archiveApplicantMapper.selectById(id, false);
    if (Objects.isNull(applicant)
        || !ApplicantStatusEnum.PENDING.equalsValue(applicant.getStatus())) {
      return new SimpleResult<>("申请记录不存在");
    }
    Long archiveId = applicant.getArchiveId();
    Archive archive = archiveMapper.selectByIdOrTid(archiveId, null, true);
    Long archiveTypeId = archive.getArchiveTypeId();
    ArchiveType archiveType = archiveTypeMapper.selectById(archiveTypeId, true);
    Long operatorId = applicant.getOperatorId();
    User operator = userMapper.selectNameById(operatorId, true);
    PendingApprovalVO vo = ApprovalConvert.getPendingApprovalVO(applicant);
    vo.setArchiveTypeId(archiveTypeId);
    vo.setArchiveTypeName(archiveType.getTypeName());
    vo.setType(archive.getType());
    vo.setArchiveName(archive.getArchiveName());
    vo.setArchiveNo(archive.getArchiveNo());
    vo.setLocation(LocationUtil.location(archive));
    vo.setOperatorName(operator.getUserName());
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<BaseCreateListVO> approveArchive(ApprovalRequest request) {
    Long userId = AccountLocal.getUserId();
    // todo: 这里需要判断自己是否具有审核权限
    List<Long> idList = request.getIdList();
    String approvalStatus = request.getApprovalStatus();
    List<ArchiveApplicant> orgApplicantList =
        archiveApplicantMapper.selectListByIdList(idList, false);
    // 判断
    Map<Long, ArchiveApplicant> orgApplicantMap =
        orgApplicantList.stream().collect(Collectors.toMap(ArchiveApplicant::getId, v -> v));
    for (Long id : idList) {
      ArchiveApplicant applicant = orgApplicantMap.get(id);
      if (Objects.isNull(applicant)) {
        return new SimpleResult<>("申请记录不存在，id 为 " + id);
      }
      if (!ApplicantStatusEnum.PENDING.equalsValue(applicant.getStatus())) {
        return new SimpleResult<>("申请记录已被审核或撤销");
      }
    }
    // 转换
    // 申请记录状态更新实例
    Date now = new Date();
    List<ArchiveApplicant> applicantList = new ArrayList<>();
    for (Long id : idList) {
      ArchiveApplicant orgApplicant = orgApplicantMap.get(id);
      Integer borrowDays = orgApplicant.getBorrowDays();
      ArchiveApplicant applicant = new ArchiveApplicant();
      applicant.setId(id);
      if (ApprovalStatusEnum.APPROVED.equalsValue(approvalStatus)) {
        applicant.setStatus(ApplicantStatusEnum.APPROVED.getValue());
        applicant.setNeedBackAt(DateUtil.offsetDay(now, borrowDays));
      } else {
        applicant.setStatus(ApplicantStatusEnum.REJECT.getValue());
      }
      applicant.setApprovalAt(now);
      applicantList.add(applicant);
    }
    // 档案状态更新实例
    List<Long> archiveIdList = new ArrayList<>();
    for (Long id : idList) {
      archiveIdList.add(orgApplicantMap.get(id).getArchiveId());
    }
    String archiveShelvingStatus;
    if (ApprovalStatusEnum.APPROVED.equalsValue(approvalStatus)) {
      archiveShelvingStatus = ArchiveShelvingStatusEnum.PENDING_OUT.getValue();
    } else {
      archiveShelvingStatus = null;
    }
    List<Pair<ArchiveApproval, ArchiveTask>> pairList = new ArrayList<>();
    for (Long id : idList) {
      // 审批记录实例
      ArchiveApproval approval = new ArchiveApproval();
      approval.setArchiveApplicantId(id);
      approval.setApproverId(userId);
      approval.setApprovalStatus(approvalStatus);
      approval.setApprovalAt(now);
      approval.setApprovalComment(request.getApprovalComment());
      ArchiveTask task;
      if (ApprovalStatusEnum.APPROVED.equalsValue(approvalStatus)) {
        // 存取任务实例
        ArchiveApplicant orgApplicant = orgApplicantMap.get(id);
        task = new ArchiveTask();
        // task.setArchiveApprovalId();
        task.setOperatorId(orgApplicant.getOperatorId());
        task.setArchiveId(orgApplicant.getArchiveId());
        task.setTaskType(ArchiveTaskTypeEnum.OUT.getValue());
        task.setTaskSource(ArchiveTaskSourceEnum.PICKUP_APPROVAL.getValue());
        task.setCreatedId(userId);
        task.setUpdatedId(userId);
      } else {
        task = null;
      }
      pairList.add(Pair.of(approval, task));
    }
    // 入库
    transactionTemplate.execute(
        status -> {
          // 更新取档申请状态
          archiveApplicantMapper.updateListForApproval(applicantList);
          // 档案在架状态更新
          if (Objects.nonNull(archiveShelvingStatus)) {
            archiveMapper.updateStatusByIdList(archiveIdList, null, archiveShelvingStatus, userId);
          }
          // 生成请审批记录
          List<ArchiveApproval> approvalList =
              pairList.stream().map(Pair::getLeft).collect(Collectors.toList());
          archiveApprovalMapper.insertList(approvalList);
          // 创建出库任务
          List<ArchiveTask> taskList =
              pairList.stream()
                  .filter(p -> Objects.nonNull(p.getRight()))
                  .peek(p -> p.getRight().setArchiveApprovalId(p.getLeft().getId()))
                  .map(Pair::getRight)
                  .collect(Collectors.toList());
          if (CollUtil.isNotEmpty(taskList)) {
            archiveTaskService.createArchiveTask(taskList);
          }
          return true;
        });
    List<Long> approvalIdList =
        pairList.stream()
            .map(Pair::getLeft)
            .map(ArchiveApproval::getId)
            .collect(Collectors.toList());
    return new SimpleResult<>(new BaseCreateListVO(approvalIdList));
  }

  @Override
  public PageResult<ApprovedListVO> getArchiveApprovedList(ApprovedListRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Date> applicantCreateAtRange = request.getApplicantCreateAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    ArchiveApprovalWrapper wrapper = ApprovalConvert.getArchiveApprovalWrapper(request);
    if (Objects.nonNull(applicantCreateAtRange)) {
      wrapper.setApplicantBeginCreateAt(applicantCreateAtRange.get(0));
      wrapper.setApplicantEndCreateAt(applicantCreateAtRange.get(1));
    }
    wrapper.setApproverId(userId);
    PageHelper.startPage(pageNum, pageSize);
    List<ArchiveApprovalExt> approvalList = archiveApprovalMapper.selectList(wrapper);
    List<ApprovedListVO> voList = getApprovedListVOList(approvalList);
    return new PageResult<>(voList, approvalList);
  }

  @Override
  public SimpleResult<ApprovedVO> getArchiveApproved(ApprovedRequest request) {
    Long id = request.getId();
    ArchiveApproval approval = archiveApprovalMapper.selectById(id);
    if (Objects.isNull(approval)) {
      return new SimpleResult<>("审批记录不存在");
    }
    Long archiveApplicantId = approval.getArchiveApplicantId();
    ArchiveApplicant applicant = archiveApplicantMapper.selectById(archiveApplicantId, true);
    Long archiveId = applicant.getArchiveId();
    Archive archive = archiveMapper.selectByIdOrTid(archiveId, null, true);
    Long archiveTypeId = archive.getArchiveTypeId();
    ArchiveType archiveType = archiveTypeMapper.selectById(archiveTypeId, true);
    Long operatorId = applicant.getOperatorId();
    User operator = userMapper.selectNameById(operatorId, true);
    ApprovedVO vo = ApprovalConvert.getApprovedVO(applicant);
    vo.setArchiveTypeId(archiveTypeId);
    vo.setArchiveTypeName(archiveType.getTypeName());
    vo.setType(archive.getType());
    vo.setArchiveName(archive.getArchiveName());
    vo.setArchiveNo(archive.getArchiveNo());
    vo.setLocation(LocationUtil.location(archive));
    vo.setOperatorName(operator.getUserName());
    vo.setApprovalStatus(approval.getApprovalStatus());
    vo.setApprovalComment(approval.getApprovalComment());
    return new SimpleResult<>(vo);
  }

  private List<PendingApprovalListVO> getPendingApprovalListVOList(
      List<PendingApprovalExt> applicantList) {
    List<PendingApprovalListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(applicantList)) {
      return voList;
    }
    List<Long> archiveTypeIdList =
        applicantList.stream()
            .map(PendingApprovalExt::getArchiveTypeId)
            .distinct()
            .collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (PendingApprovalExt applicant : applicantList) {
      ArchiveType archiveType = archiveTypeMap.get(applicant.getArchiveTypeId());
      PendingApprovalListVO vo = ApprovalConvert.getPendingApprovalListVO(applicant);
      vo.setArchiveTypeName(archiveType.getTypeName());
      if (Objects.nonNull(applicant.getRackGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForRack(
                applicant.getRackGroupNo(),
                applicant.getRackColumnNo(),
                applicant.getRackPanelNo(),
                applicant.getRackSectionNo(),
                applicant.getRackLayerNo(),
                applicant.getRackGridNo()));
      }
      if (Objects.nonNull(applicant.getCabinetGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForCabinet(
                applicant.getCabinetGroupNo(),
                applicant.getCabinetNo(),
                applicant.getCabinetGridNo()));
      }
      voList.add(vo);
    }
    return voList;
  }

  private List<ApprovedListVO> getApprovedListVOList(List<ArchiveApprovalExt> approvalList) {
    List<ApprovedListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(approvalList)) {
      return voList;
    }
    List<Long> archiveTypeIdList =
        approvalList.stream()
            .map(ArchiveApprovalExt::getArchiveTypeId)
            .distinct()
            .collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveApprovalExt approval : approvalList) {
      ArchiveType archiveType = archiveTypeMap.get(approval.getArchiveTypeId());
      ApprovedListVO vo = ApprovalConvert.getApprovedListVO(approval);
      vo.setArchiveTypeName(archiveType.getTypeName());
      if (Objects.nonNull(approval.getRackGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForRack(
                approval.getRackGroupNo(),
                approval.getRackColumnNo(),
                approval.getRackPanelNo(),
                approval.getRackSectionNo(),
                approval.getRackLayerNo(),
                approval.getRackGridNo()));
      }
      if (Objects.nonNull(approval.getCabinetGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForCabinet(
                approval.getCabinetGroupNo(),
                approval.getCabinetNo(),
                approval.getCabinetGridNo()));
      }
      voList.add(vo);
    }
    return voList;
  }
}
