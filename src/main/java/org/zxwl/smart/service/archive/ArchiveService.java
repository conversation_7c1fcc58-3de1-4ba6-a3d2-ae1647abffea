package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.archive.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.*;

@Validated
public interface ArchiveService {

  PageResult<ArchiveListVO> getArchiveList(@NotNull @Valid ArchiveListRequest request);

  PageResult<ArchivePendingOutListVO> getArchivePendingOutListByAccountLocal(
      @NotNull @Valid ArchivePendingOutListRequest request);

  SimpleResult<ArchiveVO> getArchive(@NotNull @Valid ArchiveRequest request);

  PageResult<ArchivePendingStorageListVO> getArchivePendingStorageList(
      @NotNull @Valid ArchivePendingStorageListRequest request);

  PageResult<ArchiveDeletedListVO> getDeletedArchiveList(
      @NotNull @Valid ArchiveDeletedListRequest request);

  SimpleResult<BaseCreateVO> createArchive(@NotNull @Valid ArchiveCreateRequest request);

  BaseResult updateArchive(@NotNull @Valid ArchiveUpdateRequest request);

  BaseResult updateArchiveStockStatus(@NotNull @Valid UpdateStockStatusRequest request);

  BaseResult updateDeletedArchiveRestored(@NotNull @Valid ArchiveRestoreRequest request);

  BaseResult deleteArchive(@NotNull @Valid ArchiveDeleteRequest request);

  BaseResult deleteArchivePhysically(@NotNull @Valid ArchiveDeleteRequest request);

  BaseResult archiveBindTID(@NotNull @Valid ArchiveBindTIDRequest request);

  BaseResult updateArchiveTID(@NotNull @Valid ArchiveUpdateTIDRequest request);

  BaseResult unbindArchiveTID(@NotNull @Valid ArchiveUnbindTIDRequest request);

  BaseResult updateArchiveLocation(@NotNull @Valid UpdateLocationRequest request);

  BaseResult archiveShelve(@NotNull @Valid ArchiveShelveRequest request);

  BaseResult archiveUnshelve(@NotNull @Valid ArchiveUnshelveRequest request);

  BaseResult archiveBoxing(@NotNull @Valid ArchiveBoxingRequest request);

  BaseResult archiveUnboxing(@NotNull @Valid ArchiveUnboxingRequest request);
}
