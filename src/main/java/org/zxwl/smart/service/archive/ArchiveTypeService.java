package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.archive.ArchiveTypeCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeListRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.ArchiveTypeListVO;

@Validated
public interface ArchiveTypeService {

  ListResult<ArchiveTypeListVO> getArchiveTypeList(@NotNull @Valid ArchiveTypeListRequest request);

  SimpleResult<BaseCreateVO> createArchiveType(@NotNull @Valid ArchiveTypeCreateRequest request);

  BaseResult updateArchiveType(@NotNull @Valid ArchiveTypeUpdateRequest request);

  BaseResult deleteArchiveType(@NotNull @Valid ArchiveTypeDeleteRequest request);
}
