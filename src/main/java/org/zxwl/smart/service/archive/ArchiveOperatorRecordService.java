package org.zxwl.smart.service.archive;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.archive.OperatorRecordShelveExcelRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordShelveListRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordStockExcelRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordStockListRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.archive.OperatorRecordListVO;

@Validated
public interface ArchiveOperatorRecordService {

  PageResult<OperatorRecordListVO> getArchiveOperateStockRecordList(
      @NotNull @Valid OperatorRecordStockListRequest request);

  PageResult<OperatorRecordListVO> getArchiveOperateShelveRecordList(
      @NotNull @Valid OperatorRecordShelveListRequest request);

  void getArchiveOperateStockRecordExcel(
      OperatorRecordStockExcelRequest request, HttpServletResponse response);

  void getArchiveOperateShelveRecordExcel(
      OperatorRecordShelveExcelRequest request, HttpServletResponse response);
}
