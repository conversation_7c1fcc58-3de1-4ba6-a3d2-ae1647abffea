package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ArchiveTaskConvert;
import org.zxwl.smart.common.utils.ArchiveUtil;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.constant.enums.archive.ApprovalStatusEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveTaskExecutionTypeEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveTaskStatusEnum;
import org.zxwl.smart.constant.enums.cabinet.ArchiveTaskProcessCompletionStatusEnum;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskListCompleteManualRequest;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskListRequest;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskProcessRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.pickup.ArchiveTaskListVO;
import org.zxwl.smart.domain.response.pickup.ArchiveTaskProcessVO;
import org.zxwl.smart.mybatis.entity.archive.*;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.ext.archive.ArchiveTaskExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveTaskListWrapper;
import org.zxwl.smart.mybatis.mapper.archive.*;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.service.archive.ArchiveTaskService;

@Service
@AllArgsConstructor
public class ArchiveTaskServiceImpl implements ArchiveTaskService {

  private final ArchiveTaskMapper archiveTaskMapper;
  private final ArchiveMapper archiveMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final ArchiveApprovalMapper archiveApprovalMapper;
  private final UserMapper userMapper;
  private final ArchiveApplicantMapper archiveApplicantMapper;

  @Override
  public PageResult<ArchiveTaskListVO> getArchiveTaskList(ArchiveTaskListRequest request) {
    ArchiveTaskListWrapper wrapper = ArchiveTaskConvert.getArchiveTaskListWrapper(request);
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<ArchiveTaskExt> archiveTaskList = archiveTaskMapper.selectList(wrapper);
    List<ArchiveTaskListVO> voList = getArchiveTaskListVOList(archiveTaskList);
    return new PageResult<>(voList, archiveTaskList);
  }

  @Override
  public ListResult<ArchiveTaskProcessVO> getArchiveTaskProcess(ArchiveTaskProcessRequest request) {
    ArrayList<ArchiveTaskProcessVO> voList = new ArrayList<>();
    Long id = request.getId();
    ArchiveTask archiveTask = archiveTaskMapper.selectById(id);
    if (ObjectUtil.isNull(archiveTask)) {
      return new ListResult<>("任务不存在");
    }

    // todo 这块先给个结构，各执行方式还未实现，具体数据情况不清晰，暂时保留
    // region 审批情况
    ArchiveApproval archiveApproval =
        archiveApprovalMapper.selectById(archiveTask.getArchiveApprovalId());
    if (ObjectUtil.isNotNull(archiveApproval)) {
      ArchiveTaskProcessVO vo = new ArchiveTaskProcessVO();
      vo.setId(archiveApproval.getId());
      vo.setProcessTime(archiveApproval.getApprovalAt());
      vo.setCompletionStatus(ArchiveTaskProcessCompletionStatusEnum.COMPLETED.getValue());
      if (ApprovalStatusEnum.APPROVED.getValue().equals(archiveApproval.getApprovalStatus())) {
        vo.setProcessName("审批通过");
      } else if (ApprovalStatusEnum.REJECT.getValue().equals(archiveApproval.getApprovalStatus())) {
        vo.setProcessName("审批拒绝");
      }

      // 获取审批人
      String approverName = "";
      User approver = userMapper.selectById(archiveApproval.getApproverId());
      if (ObjectUtil.isNotNull(approver)) {
        approverName = approver.getUserName();
      }
      // 获取申请人
      String applicantName = "";
      ArchiveApplicant archiveApplicant =
          archiveApplicantMapper.selectById(archiveApproval.getArchiveApplicantId(), true);
      if (ObjectUtil.isNotNull(archiveApplicant)) {
        User applicantCreatedUser = userMapper.selectById(archiveApplicant.getCreatedId());
        if (ObjectUtil.isNotNull(applicantCreatedUser)) {
          applicantName = applicantCreatedUser.getUserName();
        }
      }
      vo.setProcessDescription(String.format("由[%s]办理了取档，申请人[%s]", approverName, applicantName));
      voList.add(vo);
    }
    // endregion

    if (true) {
      ArchiveTaskProcessVO vo = new ArchiveTaskProcessVO();
      vo.setProcessName("开始调档");
      vo.setProcessDescription("暂未操作");
      vo.setCompletionStatus(ArchiveTaskProcessCompletionStatusEnum.UNCOMPLETED.getValue());
      voList.add(vo);
    }
    if (true) {
      ArchiveTaskProcessVO vo = new ArchiveTaskProcessVO();
      vo.setProcessName("完成调档");
      vo.setProcessDescription("暂未操作");
      vo.setCompletionStatus(ArchiveTaskProcessCompletionStatusEnum.UNCOMPLETED.getValue());
      voList.add(vo);
    }
    if (true) {
      ArchiveTaskProcessVO vo = new ArchiveTaskProcessVO();
      vo.setProcessName("取出确认");
      vo.setProcessDescription("暂未操作");
      vo.setCompletionStatus(ArchiveTaskProcessCompletionStatusEnum.UNCOMPLETED.getValue());
      voList.add(vo);
    }
    if (true) {
      ArchiveTaskProcessVO vo = new ArchiveTaskProcessVO();
      vo.setProcessName("完成取档");
      vo.setProcessDescription("暂未操作");
      vo.setCompletionStatus(ArchiveTaskProcessCompletionStatusEnum.UNCOMPLETED.getValue());
      voList.add(vo);
    }
    return new ListResult<>(voList);
  }

  @Override
  public void createArchiveTask(List<ArchiveTask> archiveTaskList) {
    archiveTaskList.forEach(
        task -> {
          task.setStatus(ArchiveTaskStatusEnum.PENDING.getValue());
          // todo:这里的任务执行方式后面去要根据载具类型来定
          task.setExecutionType(ArchiveTaskExecutionTypeEnum.UNKNOWN.getValue());
        });
    archiveTaskMapper.insertList(archiveTaskList);
  }

  @Override
  public BaseResult completeArchiveTaskListManual(ArchiveTaskListCompleteManualRequest request) {
    List<Long> idList = request.getIdList();
    String status = ArchiveTaskStatusEnum.COMPLETED.getValue();
    Date completionTime = new Date();
    Long updatedId = AccountLocal.getUserId();
    int i = archiveTaskMapper.updateCompleteInfoByIdList(status, completionTime, updatedId, idList);
    return BaseResult.success();
  }

  private List<ArchiveTaskListVO> getArchiveTaskListVOList(List<ArchiveTaskExt> archiveTaskList) {
    List<ArchiveTaskListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(archiveTaskList)) {
      return voList;
    }
    List<Long> archiveIdList =
        archiveTaskList.stream().map(ArchiveTaskExt::getArchiveId).collect(Collectors.toList());
    List<Archive> archiveList = archiveMapper.selectListByIdList(archiveIdList, true);
    List<Long> archiveTypeIdList =
        archiveList.stream().map(Archive::getArchiveTypeId).distinct().collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, Archive> archiveMap =
        archiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveTaskExt archiveTaskExt : archiveTaskList) {
      Archive archive = archiveMap.get(archiveTaskExt.getArchiveId());
      ArchiveType archiveType = archiveTypeMap.get(archive.getArchiveTypeId());
      ArchiveTaskListVO vo = ArchiveTaskConvert.getArchiveTaskListVO(archiveTaskExt);
      vo.setArchiveTypeId(archive.getArchiveTypeId());
      vo.setArchiveTypeName(archiveType.getTypeName());
      vo.setBoxStatus(ArchiveUtil.getArchiveBoxStatus(archive));
      vo.setArchiveName(archive.getArchiveName());
      vo.setArchiveNo(archive.getArchiveNo());
      vo.setLocation(LocationUtil.location(archive));
      vo.setStorageType(ArchiveUtil.getArchiveStorageType(archive));
      voList.add(vo);
    }
    return voList;
  }
}
