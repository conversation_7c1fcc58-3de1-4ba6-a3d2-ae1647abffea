package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.archive.ArchiveFieldCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldListRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.ArchiveFieldListVO;

@Validated
public interface ArchiveFieldService {

  ListResult<ArchiveFieldListVO> getArchiveFieldList(
      @NotNull @Valid ArchiveFieldListRequest request);

  SimpleResult<BaseCreateVO> createArchiveField(@NotNull @Valid ArchiveFieldCreateRequest request);

  BaseResult updateArchiveField(@NotNull @Valid ArchiveFieldUpdateRequest request);

  BaseResult deleteArchiveField(@NotNull @Valid ArchiveFieldDeleteRequest request);
}
