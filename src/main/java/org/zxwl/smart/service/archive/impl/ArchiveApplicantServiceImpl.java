package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ApplicationConvert;
import org.zxwl.smart.common.utils.ArchiveUtil;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.constant.enums.archive.ApplicantStatusEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.ApplicantApprovedVO;
import org.zxwl.smart.domain.response.pickup.ApplicantListVO;
import org.zxwl.smart.domain.response.pickup.ApplicantVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.archive.ArchiveApplicant;
import org.zxwl.smart.mybatis.entity.archive.ArchiveApproval;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApplicantExt;
import org.zxwl.smart.mybatis.ext.archive.ArchiveApplicantWrapper;
import org.zxwl.smart.mybatis.mapper.archive.*;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.service.archive.ArchiveApplicantService;

@Service
@AllArgsConstructor
public class ArchiveApplicantServiceImpl implements ArchiveApplicantService {

  private final ArchiveApplicantMapper archiveApplicantMapper;
  private final ArchivePendingPickupMapper pendingPickupMapper;
  private final ArchiveApprovalMapper archiveApprovalMapper;
  private final UserMapper userMapper;
  private final ArchiveMapper archiveMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final TransactionTemplate transactionTemplate;

  @Override
  public PageResult<ApplicantListVO> getArchiveApplicantList(ApplicantListRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Date> createAtRange = request.getCreateAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    ArchiveApplicantWrapper wrapper = ApplicationConvert.getArchiveApplicantWrapper(request);
    wrapper.setCreatedId(userId);
    if (Objects.nonNull(createAtRange)) {
      wrapper.setBeginCreateAt(createAtRange.get(0));
      wrapper.setEndCreateAt(createAtRange.get(1));
    }
    PageHelper.startPage(pageNum, pageSize);
    List<ArchiveApplicantExt> applicantList = archiveApplicantMapper.selectList(wrapper);
    List<ApplicantListVO> voList = getApplicantListVOList(applicantList);
    return new PageResult<>(voList, applicantList);
  }

  @Override
  public SimpleResult<ApplicantVO> getArchiveApplicant(ApplicantRequest request) {
    Long id = request.getId();
    ArchiveApplicant applicant = archiveApplicantMapper.selectById(id, false);
    if (Objects.isNull(applicant)) {
      return new SimpleResult<>("申请记录不存在");
    }
    Long archiveId = applicant.getArchiveId();
    Archive archive = archiveMapper.selectByIdOrTid(archiveId, null, true);
    Long archiveTypeId = archive.getArchiveTypeId();
    ArchiveType archiveType = archiveTypeMapper.selectById(archiveTypeId, true);
    ArchiveApproval approval = archiveApprovalMapper.selectByArchiveApplicantId(id, true);
    ArrayList<Long> userIdList = ListUtil.toList(applicant.getOperatorId());
    if (Objects.nonNull(approval)) {
      userIdList.add(approval.getApproverId());
    }
    List<User> userList = userMapper.selectNameListByIdList(userIdList, true);
    Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, v -> v));
    ApplicantVO vo = ApplicationConvert.getApplicantVO(applicant);
    vo.setArchiveTypeId(archiveTypeId);
    if (Objects.nonNull(archiveType)) {
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    vo.setBoxStatus(ArchiveUtil.getArchiveBoxStatus(archive));
    vo.setArchiveName(archive.getArchiveName());
    vo.setArchiveNo(archive.getArchiveNo());
    vo.setLocation(LocationUtil.location(archive));
    vo.setOperatorName(userMap.get(applicant.getOperatorId()).getUserName());
    if (Objects.nonNull(approval)) {
      Long approverId = approval.getApproverId();
      ApplicantApprovedVO approvedVO = new ApplicantApprovedVO();
      approvedVO.setApproverId(approverId);
      approvedVO.setApproverName(userMap.get(approverId).getUserName());
      approvedVO.setApprovalAt(approval.getApprovalAt());
      approvedVO.setApprovalComment(approval.getApprovalComment());
      vo.setApproved(approvedVO);
    }
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<BaseCreateListVO> createArchiveApplicant(ApplicantCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Long> archiveIdList = request.getArchiveIdList();
    Long operatorId = request.getOperatorId();
    List<Archive> archiveList = archiveMapper.selectListByIdList(archiveIdList, false);
    User operator = userMapper.selectById(operatorId);
    Map<Long, Archive> archiveMap =
        archiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    List<ArchiveApplicant> archiveApplicantList =
        archiveApplicantMapper.selectListByArchiveIdList(archiveIdList, false);
    for (Long archiveId : archiveIdList) {
      Archive archive = archiveMap.get(archiveId);
      if (Objects.isNull(archive)) {
        return new SimpleResult<>("档案不存在");
      }
      if (archiveApplicantList.stream()
          .anyMatch(
              archiveApplicant ->
                  ApplicantStatusEnum.PENDING.equalsValue(archiveApplicant.getStatus()))) {
        return new SimpleResult<>(archive.getArchiveName() + "正在走取档申请流程，请勿重复申请");
      }
      if (!ArchiveShelvingStatusEnum.IN.equalsValue(archive.getShelvingStatus())) {
        return new SimpleResult<>(archive.getArchiveName() + "不在架，不可取档申请");
      }
    }
    if (Objects.isNull(operator)) {
      return new SimpleResult<>("经办人不存在");
    }
    List<ArchiveApplicant> applicantList = new ArrayList<>();
    for (Long archiveId : archiveIdList) {
      ArchiveApplicant applicant = ApplicationConvert.getArchiveApplicant(request);
      applicant.setArchiveId(archiveId);
      applicant.setStatus(ApplicantStatusEnum.PENDING.getValue());
      applicant.setCreatedId(userId);
      applicant.setUpdatedId(userId);
      applicantList.add(applicant);
    }
    transactionTemplate.execute(
        status -> {
          pendingPickupMapper.deleteByArchiveIdListAndUserId(archiveIdList, userId);
          archiveApplicantMapper.insertList(applicantList);
          return true;
        });
    List<Long> idList =
        applicantList.stream().map(ArchiveApplicant::getId).collect(Collectors.toList());
    return new SimpleResult<>(new BaseCreateListVO(idList));
  }

  @Override
  public BaseResult revokeArchiveApplicant(ApplicantRevokeRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Long> idList = request.getIdList();
    List<ArchiveApplicant> orgApplicantList =
        archiveApplicantMapper.selectListByIdList(idList, false);
    Map<Long, ArchiveApplicant> orgApplicantMap =
        orgApplicantList.stream().collect(Collectors.toMap(ArchiveApplicant::getId, v -> v));
    for (Long id : idList) {
      ArchiveApplicant applicant = orgApplicantMap.get(id);
      if (Objects.isNull(applicant)) {
        return BaseResult.failed("申请记录不存在，id 为 " + id);
      }
      if (!ApplicantStatusEnum.PENDING.equalsValue(applicant.getStatus())) {
        return BaseResult.failed("申请记录已被审核，id 为 " + id);
      }
    }
    transactionTemplate.execute(
        status -> {
          archiveApplicantMapper.updateStatusByIdList(
              idList, ApplicantStatusEnum.REVOKE.getValue());
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArchiveApplicant(ApplicantDeleteRequest request) {
    List<Long> idList = request.getIdList();
    List<ArchiveApplicant> orgApplicantList =
        archiveApplicantMapper.selectListByIdList(idList, false);
    Map<Long, ArchiveApplicant> orgApplicantMap =
        orgApplicantList.stream().collect(Collectors.toMap(ArchiveApplicant::getId, v -> v));
    for (Long id : idList) {
      ArchiveApplicant applicant = orgApplicantMap.get(id);
      if (Objects.isNull(applicant)) {
        return BaseResult.failed("申请记录不存在，id 为 " + id);
      }
      if (ApplicantStatusEnum.PENDING.equalsValue(applicant.getStatus())) {
        return BaseResult.failed("记录正在等待审核，不允许删除，id 为 " + id);
      }
    }
    archiveApplicantMapper.deleteByIdList(idList, System.currentTimeMillis());
    return BaseResult.success();
  }

  private List<ApplicantListVO> getApplicantListVOList(List<ArchiveApplicantExt> applicantList) {
    List<ApplicantListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(applicantList)) {
      return voList;
    }
    List<Long> archiveTypeIdList =
        applicantList.stream()
            .map(ArchiveApplicantExt::getArchiveTypeId)
            .distinct()
            .collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveApplicantExt applicant : applicantList) {
      ArchiveType archiveType = archiveTypeMap.get(applicant.getArchiveTypeId());
      ApplicantListVO vo = ApplicationConvert.getApplicantListVO(applicant);
      vo.setArchiveTypeName(archiveType.getTypeName());
      if (Objects.nonNull(applicant.getRackGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForRack(
                applicant.getRackGroupNo(),
                applicant.getRackColumnNo(),
                applicant.getRackPanelNo(),
                applicant.getRackSectionNo(),
                applicant.getRackLayerNo(),
                applicant.getRackGridNo()));
      }
      if (Objects.nonNull(applicant.getCabinetGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForCabinet(
                applicant.getCabinetGroupNo(),
                applicant.getCabinetNo(),
                applicant.getCabinetGridNo()));
      }
      voList.add(vo);
    }
    return voList;
  }
}
