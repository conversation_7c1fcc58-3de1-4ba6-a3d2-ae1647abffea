package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.pickup.PendingPickupCreateRequest;
import org.zxwl.smart.domain.request.pickup.PendingPickupDeleteRequest;
import org.zxwl.smart.domain.request.pickup.PendingPickupListRequest;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.PendingPickupVO;

@Validated
public interface PendingPickupService {

  PageResult<PendingPickupVO> getArchivePendingPickupList(
      @NotNull @Valid PendingPickupListRequest request);

  SimpleResult<BaseCreateListVO> createArchivePendingPickup(
      @NotNull @Valid PendingPickupCreateRequest request);

  BaseResult deleteArchivePendingPickup(@NotNull @Valid PendingPickupDeleteRequest request);
}
