package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ArchiveTypeConvert;
import org.zxwl.smart.common.utils.ColUtil;
import org.zxwl.smart.constant.consist.ArchiveConstant;
import org.zxwl.smart.constant.enums.archive.ArchiveFieldTypeEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveFixedFieldEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveTypeTypeEnum;
import org.zxwl.smart.constant.enums.archive.BoxFixedFieldEnum;
import org.zxwl.smart.domain.request.archive.ArchiveTypeCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeListRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.ArchiveTypeListVO;
import org.zxwl.smart.mybatis.entity.archive.ArchiveField;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveFieldMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper;
import org.zxwl.smart.service.archive.ArchiveTypeService;

@Slf4j
@Service
@AllArgsConstructor
public class ArchiveTypeServiceImpl implements ArchiveTypeService {

  private final ArchiveTypeMapper archiveTypeMapper;
  private final ArchiveFieldMapper archiveFieldMapper;
  private final ArchiveMapper archiveMapper;
  private final TransactionTemplate transactionTemplate;

  @Override
  public ListResult<ArchiveTypeListVO> getArchiveTypeList(ArchiveTypeListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    String type = request.getType();
    List<ArchiveType> list = archiveTypeMapper.selectList(organizationId, type);
    List<ArchiveTypeListVO> voList = ArchiveTypeConvert.getArchiveTypeListVOList(list);
    Map<Long, List<ArchiveTypeListVO>> map = ColUtil.groupingBy(voList, ArchiveTypeListVO::getPid);
    for (ArchiveTypeListVO vo : voList) {
      vo.setChildrenArchiveTypeList(map.get(vo.getId()));
    }
    List<ArchiveTypeListVO> rootVoList =
        voList.stream().filter(v -> Objects.isNull(v.getPid())).collect(Collectors.toList());
    return new ListResult<>(rootVoList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createArchiveType(ArchiveTypeCreateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long pid = request.getPid();
    String type = request.getType();
    String typeName = request.getTypeName();
    if (Objects.nonNull(pid)) {
      ArchiveType parentArchiveType = archiveTypeMapper.selectById(pid, false);
      if (Objects.isNull(parentArchiveType)) {
        return new SimpleResult<>("父门类不存在");
      }
      if (!StrUtil.equals(type, parentArchiveType.getType())) {
        return new SimpleResult<>("父门类类型与当前门类类型不一致");
      }
    }
    if (archiveTypeMapper.existTypeName(typeName) > 0) {
      return new SimpleResult<>("门类名称已存在");
    }
    if (archiveTypeMapper.count() > ArchiveConstant.MAX_ARCHIVE_TYPE_COUNT) {
      return new SimpleResult<>("门类数量已达" + ArchiveConstant.MAX_ARCHIVE_TYPE_COUNT + "条上限");
    }
    ArchiveType archiveType = ArchiveTypeConvert.getArchiveType(request);
    archiveType.setOrganizationId(organizationId);
    archiveType.setCreatedId(userId);
    archiveType.setUpdatedId(userId);
    List<ArchiveField> fixedArchiveFieldList;
    if (ArchiveTypeTypeEnum.ARCHIVE.equalsValue(type)) {
      fixedArchiveFieldList = getArchiveFixedFieldList();
    } else {
      fixedArchiveFieldList = getBoxFixedFieldList();
    }
    fixedArchiveFieldList.forEach(
        f -> {
          f.setCreatedId(userId);
          f.setUpdatedId(userId);
        });
    transactionTemplate.execute(
        status -> {
          archiveTypeMapper.insert(archiveType);
          Long id = archiveType.getId();
          fixedArchiveFieldList.forEach(f -> f.setArchiveTypeId(id));
          if (CollUtil.isNotEmpty(fixedArchiveFieldList)) {
            archiveFieldMapper.insertList(fixedArchiveFieldList);
          }
          return false;
        });
    return new SimpleResult<>(new BaseCreateVO(archiveType.getId()));
  }

  @Override
  public BaseResult updateArchiveType(ArchiveTypeUpdateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Long pid = request.getPid();
    String typeName = request.getTypeName();
    Boolean supportManual = request.getSupportManual();
    ArchiveType orgArchiveType = archiveTypeMapper.selectById(id, false);
    if (Objects.isNull(orgArchiveType)) {
      return BaseResult.failed("门类不存在");
    }
    if (!Objects.equals(organizationId, orgArchiveType.getOrganizationId())) {
      return BaseResult.failed("该门类不属于当前机构");
    }
    if (Objects.nonNull(pid) && !Objects.equals(pid, orgArchiveType.getPid())) {
      ArchiveType parentArchiveType = archiveTypeMapper.selectById(pid, false);
      if (Objects.isNull(parentArchiveType)) {
        return BaseResult.failed("父门类不存在");
      }
      if (!StrUtil.equals(orgArchiveType.getType(), parentArchiveType.getType())) {
        return BaseResult.failed("父门类类型与当前门类类型不一致");
      }
    }
    if (!StrUtil.equals(typeName, orgArchiveType.getTypeName())) {
      if (archiveTypeMapper.existTypeName(typeName) > 0) {
        return BaseResult.failed("门类名称已存在");
      }
    }
    ArchiveType archiveType = ArchiveTypeConvert.getArchiveType(request);
    archiveType.setSupportManual(
        Objects.isNull(supportManual) ? orgArchiveType.getSupportManual() : supportManual);
    archiveType.setUpdatedId(userId);
    archiveTypeMapper.updateById(archiveType);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArchiveType(ArchiveTypeDeleteRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    ArchiveType orgArchiveType = archiveTypeMapper.selectById(id, false);
    if (Objects.isNull(orgArchiveType)) {
      return BaseResult.failed("门类不存在");
    }
    if (!Objects.equals(organizationId, orgArchiveType.getOrganizationId())) {
      return BaseResult.failed("该门类不属于当前机构");
    }
    if (archiveMapper.existByArchiveTypeId(id) > 0) {
      return BaseResult.failed("该门类下存在档案，不能直接删除");
    }
    // todo：检查该门类下面是否有关联密集架层位、字段配置等信息
    transactionTemplate.execute(
        status -> {
          long deleteAt = System.currentTimeMillis();
          archiveTypeMapper.deleteById(id, deleteAt);
          archiveFieldMapper.deleteByArchiveTypeId(id, deleteAt);
          return true;
        });
    return BaseResult.success();
  }

  private List<ArchiveField> getArchiveFixedFieldList() {
    List<ArchiveField> archiveFieldList = new ArrayList<>();
    for (ArchiveFixedFieldEnum fixedField : ArchiveFixedFieldEnum.values()) {
      ArchiveField archiveField = new ArchiveField();
      archiveField.setFieldType(ArchiveFieldTypeEnum.STRING.getValue());
      archiveField.setFixedFieldName(fixedField.getValue());
      archiveField.setFieldName(fixedField.getDefaultFieldName());
      archiveField.setFieldOrdinal(fixedField.getFieldOrdinal());
      archiveField.setFieldWidth(null);
      archiveField.setShowList(fixedField.getShowList());
      archiveField.setInput(fixedField.getInput());
      archiveField.setRequired(fixedField.getRequired());
      archiveField.setSearch(fixedField.getSearch());
      archiveField.setAdSearch(fixedField.getAdSearch());
      archiveFieldList.add(archiveField);
    }
    return archiveFieldList;
  }

  private List<ArchiveField> getBoxFixedFieldList() {
    List<ArchiveField> archiveFieldList = new ArrayList<>();
    for (BoxFixedFieldEnum fixedField : BoxFixedFieldEnum.values()) {
      ArchiveField archiveField = new ArchiveField();
      archiveField.setFieldType(ArchiveFieldTypeEnum.STRING.getValue());
      archiveField.setFixedFieldName(fixedField.getValue());
      archiveField.setFieldName(fixedField.getDefaultFieldName());
      archiveField.setFieldOrdinal(fixedField.getFieldOrdinal());
      archiveField.setFieldWidth(null);
      archiveField.setShowList(fixedField.getShowList());
      archiveField.setInput(fixedField.getInput());
      archiveField.setRequired(fixedField.getRequired());
      archiveField.setSearch(fixedField.getSearch());
      archiveField.setAdSearch(fixedField.getAdSearch());
      archiveFieldList.add(archiveField);
    }
    return archiveFieldList;
  }
}
