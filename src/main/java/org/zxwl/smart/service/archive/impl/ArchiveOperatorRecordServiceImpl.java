package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.common.convert.archive.ArchiveConvert;
import org.zxwl.smart.common.utils.ResponseUtil;
import org.zxwl.smart.constant.enums.archive.ArchiveOperatorTypeEnum;
import org.zxwl.smart.domain.request.archive.OperatorRecordShelveExcelRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordShelveListRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordStockExcelRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordStockListRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.archive.ArchiveOperateRecordExcelVO;
import org.zxwl.smart.domain.response.archive.OperatorRecordListVO;
import org.zxwl.smart.mybatis.entity.archive.ArchiveOperatorRecord;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.ext.archive.ArchiveOperatorRecordExt;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveOperatorRecordMapper;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.service.archive.ArchiveOperatorRecordService;

@Service
@AllArgsConstructor
public class ArchiveOperatorRecordServiceImpl implements ArchiveOperatorRecordService {

  private final ArchiveOperatorRecordMapper archiveOperatorRecordMapper;
  private final UserMapper userMapper;

  @Override
  public PageResult<OperatorRecordListVO> getArchiveOperateStockRecordList(
      OperatorRecordStockListRequest request) {
    String archiveName = request.getArchiveName();
    String archiveNameLike = request.getArchiveNameLike();
    String archiveNo = request.getArchiveNo();
    String archiveNoLike = request.getArchiveNoLike();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();

    ArchiveOperatorRecordExt ext = new ArchiveOperatorRecordExt();
    ext.setArchiveName(archiveName);
    ext.setArchiveNameLike(archiveNameLike);
    ext.setArchiveNo(archiveNo);
    ext.setArchiveNoLike(archiveNoLike);
    List<String> operatorTypeList = new ArrayList<>();
    operatorTypeList.add(ArchiveOperatorTypeEnum.STOCK_IN.getValue());
    operatorTypeList.add(ArchiveOperatorTypeEnum.STOCK_OUT.getValue());
    ext.setOperatorTypeList(operatorTypeList);
    PageHelper.startPage(pageNum, pageSize);

    List<ArchiveOperatorRecord> operatorRecordList =
        archiveOperatorRecordMapper.selectList(ext);
    List<OperatorRecordListVO> operatorRecordListVOList =
        ArchiveConvert.getOperatorRecordListVOList(operatorRecordList);
    List<Long> operatorIdList =
        operatorRecordListVOList.stream()
            .map(OperatorRecordListVO::getOperatorId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
    if (CollUtil.isNotEmpty(operatorIdList)) {
      List<User> userList = userMapper.selectNameListByIdList(operatorIdList, true);
      Map<Long, String> userMap =
          userList.stream().collect(Collectors.toMap(User::getId, User::getUserName));
      for (OperatorRecordListVO vo : operatorRecordListVOList) {
        vo.setOperatorName(userMap.get(vo.getOperatorId()));
      }
    }
    return new PageResult<>(operatorRecordListVOList, operatorRecordList);
  }

  @Override
  public PageResult<OperatorRecordListVO> getArchiveOperateShelveRecordList(
      OperatorRecordShelveListRequest request) {
    String archiveName = request.getArchiveName();
    String archiveNameLike = request.getArchiveNameLike();
    String archiveNo = request.getArchiveNo();
    String archiveNoLike = request.getArchiveNoLike();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();

    ArchiveOperatorRecordExt ext = new ArchiveOperatorRecordExt();
    ext.setArchiveName(archiveName);
    ext.setArchiveNameLike(archiveNameLike);
    ext.setArchiveNo(archiveNo);
    ext.setArchiveNoLike(archiveNoLike);
    List<String> operatorTypeList = new ArrayList<>();
    operatorTypeList.add(ArchiveOperatorTypeEnum.SHELVE.getValue());
    operatorTypeList.add(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    ext.setOperatorTypeList(operatorTypeList);
    PageHelper.startPage(pageNum, pageSize);

    List<ArchiveOperatorRecord> operatorRecordList =
        archiveOperatorRecordMapper.selectList(ext);
    List<OperatorRecordListVO> operatorRecordListVOList =
        ArchiveConvert.getOperatorRecordListVOList(operatorRecordList);
    List<Long> operatorIdList =
        operatorRecordListVOList.stream()
            .map(OperatorRecordListVO::getOperatorId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
    if (CollUtil.isNotEmpty(operatorIdList)) {
      List<User> userList = userMapper.selectNameListByIdList(operatorIdList, true);
      Map<Long, String> userMap =
          userList.stream().collect(Collectors.toMap(User::getId, User::getUserName));
      for (OperatorRecordListVO vo : operatorRecordListVOList) {
        vo.setOperatorName(userMap.get(vo.getOperatorId()));
      }
    }
    return new PageResult<>(operatorRecordListVOList, operatorRecordList);
  }

  @Override
  public void getArchiveOperateStockRecordExcel(
      OperatorRecordStockExcelRequest request, HttpServletResponse response) {
    String archiveName = request.getArchiveName();
    String archiveNameLike = request.getArchiveNameLike();
    String archiveNo = request.getArchiveNo();
    String archiveNoLike = request.getArchiveNoLike();

    ArchiveOperatorRecordExt ext = new ArchiveOperatorRecordExt();
    ext.setArchiveName(archiveName);
    ext.setArchiveNameLike(archiveNameLike);
    ext.setArchiveNo(archiveNo);
    ext.setArchiveNoLike(archiveNoLike);
    List<String> operatorTypeList = new ArrayList<>();
    operatorTypeList.add(ArchiveOperatorTypeEnum.STOCK_IN.getValue());
    operatorTypeList.add(ArchiveOperatorTypeEnum.STOCK_OUT.getValue());
    ext.setOperatorTypeList(operatorTypeList);

    List<ArchiveOperatorRecord> operatorRecordList =
        archiveOperatorRecordMapper.selectList(ext);
    List<ArchiveOperateRecordExcelVO> voList =
        getArchiveOperateRecordExcelVOList(operatorRecordList);
    if (CollUtil.isNotEmpty(voList)) {
      List<Long> operatorIdList =
          voList.stream()
              .map(ArchiveOperateRecordExcelVO::getOperatorId)
              .filter(Objects::nonNull)
              .distinct()
              .toList();
      if (CollUtil.isNotEmpty(operatorIdList)) {
        List<User> userList = userMapper.selectNameListByIdList(operatorIdList, false);
        Map<Long, String> userMap =
            userList.stream().collect(Collectors.toMap(User::getId, User::getUserName));
        for (ArchiveOperateRecordExcelVO vo : voList) {
          vo.setOperatorName(userMap.get(vo.getOperatorId()));
        }
      }
    }
    ResponseUtil.exportExcel(response, voList, ArchiveOperateRecordExcelVO.class, "档案出入库操作日志表");
  }

  @Override
  public void getArchiveOperateShelveRecordExcel(
      OperatorRecordShelveExcelRequest request, HttpServletResponse response) {
    String archiveName = request.getArchiveName();
    String archiveNameLike = request.getArchiveNameLike();
    String archiveNo = request.getArchiveNo();
    String archiveNoLike = request.getArchiveNoLike();

    ArchiveOperatorRecordExt ext = new ArchiveOperatorRecordExt();
    ext.setArchiveName(archiveName);
    ext.setArchiveNameLike(archiveNameLike);
    ext.setArchiveNo(archiveNo);
    ext.setArchiveNoLike(archiveNoLike);
    List<String> operatorTypeList = new ArrayList<>();
    operatorTypeList.add(ArchiveOperatorTypeEnum.SHELVE.getValue());
    operatorTypeList.add(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    ext.setOperatorTypeList(operatorTypeList);

    List<ArchiveOperatorRecord> operatorRecordList =
        archiveOperatorRecordMapper.selectList(ext);
    List<ArchiveOperateRecordExcelVO> voList =
        getArchiveOperateRecordExcelVOList(operatorRecordList);
    if (CollUtil.isNotEmpty(voList)) {
      List<Long> operatorIdList =
          voList.stream()
              .map(ArchiveOperateRecordExcelVO::getOperatorId)
              .filter(Objects::nonNull)
              .distinct()
              .toList();
      if (CollUtil.isNotEmpty(operatorIdList)) {
        List<User> userList = userMapper.selectNameListByIdList(operatorIdList, false);
        Map<Long, String> userMap =
            userList.stream().collect(Collectors.toMap(User::getId, User::getUserName));
        for (ArchiveOperateRecordExcelVO vo : voList) {
          vo.setOperatorName(userMap.get(vo.getOperatorId()));
        }
      }
    }
    ResponseUtil.exportExcel(response, voList, ArchiveOperateRecordExcelVO.class, "档案上下架操作日志表");
  }

  private List<ArchiveOperateRecordExcelVO> getArchiveOperateRecordExcelVOList(
      List<ArchiveOperatorRecord> recordList) {
    if (CollUtil.isEmpty(recordList)) {
      return new ArrayList<>();
    }
    return ArchiveConvert.getArchiveOperateRecordExcelVOList(recordList);
  }
}
