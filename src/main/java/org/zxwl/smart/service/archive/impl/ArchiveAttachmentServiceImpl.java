package org.zxwl.smart.service.archive.impl;

import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ArchiveConvert;
import org.zxwl.smart.common.utils.OSSUtil;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentRequest;
import org.zxwl.smart.domain.request.archive.ArchiveAttachmentUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.archive.ArchiveAttachmentVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.archive.ArchiveAttachment;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveAttachmentMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.service.archive.ArchiveAttachmentService;

@Service
@AllArgsConstructor
public class ArchiveAttachmentServiceImpl implements ArchiveAttachmentService {

  private final ArchiveAttachmentMapper archiveAttachmentMapper;
  private final ArchiveMapper archiveMapper;

  @Override
  public ListResult<ArchiveAttachmentVO> getArchiveAttachmentList(
      ArchiveAttachmentRequest request) {
    Long archiveId = request.getArchiveId();
    List<ArchiveAttachment> list = archiveAttachmentMapper.selectListByArchiveId(archiveId);
    List<ArchiveAttachmentVO> voList = ArchiveConvert.getArchiveAttachmentVOList(list);
    return new ListResult<>(voList);
  }

  @Override
  public BaseResult createArchiveAttachment(ArchiveAttachmentCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long archiveId = request.getArchiveId();
    Archive archive = archiveMapper.selectByIdOrTid(archiveId, null, false);
    if (Objects.isNull(archive)) {
      return BaseResult.failed("档案不存在");
    }
    ArchiveAttachment archiveAttachment = ArchiveConvert.getArchiveAttachment(request);
    archiveAttachment.setCreatedId(userId);
    archiveAttachment.setUpdatedId(userId);
    archiveAttachmentMapper.insert(archiveAttachment);
    return BaseResult.success();
  }

  @Override
  public BaseResult updateArchiveAttachment(ArchiveAttachmentUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    ArchiveAttachment archiveAttachment = archiveAttachmentMapper.selectById(id);
    if (Objects.isNull(archiveAttachment)) {
      return BaseResult.failed("附件不存在");
    }
    archiveAttachment.setAttachmentName(request.getAttachmentName());
    archiveAttachment.setAttachmentPath(request.getAttachmentPath());
    archiveAttachment.setUpdatedId(userId);
    archiveAttachmentMapper.updateById(archiveAttachment);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArchiveAttachment(ArchiveAttachmentDeleteRequest request) {
    Long id = request.getId();
    ArchiveAttachment archiveAttachment = archiveAttachmentMapper.selectById(id);
    if (Objects.isNull(archiveAttachment)) {
      return BaseResult.failed("附件不存在");
    }
    archiveAttachmentMapper.deleteById(id);
    OSSUtil.deleteFile(archiveAttachment.getAttachmentPath());
    return BaseResult.success();
  }
}
