package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.ApplicantListVO;
import org.zxwl.smart.domain.response.pickup.ApplicantVO;

@Validated
public interface ArchiveApplicantService {

  PageResult<ApplicantListVO> getArchiveApplicantList(@NotNull @Valid ApplicantListRequest request);

  SimpleResult<ApplicantVO> getArchiveApplicant(@NotNull @Valid ApplicantRequest request);

  SimpleResult<BaseCreateListVO> createArchiveApplicant(
      @NotNull @Valid ApplicantCreateRequest request);

  BaseResult revokeArchiveApplicant(@NotNull @Valid ApplicantRevokeRequest request);

  BaseResult deleteArchiveApplicant(@NotNull @Valid ApplicantDeleteRequest request);
}
