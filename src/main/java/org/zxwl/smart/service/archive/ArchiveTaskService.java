package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskListCompleteManualRequest;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskListRequest;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskProcessRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.pickup.ArchiveTaskListVO;
import org.zxwl.smart.domain.response.pickup.ArchiveTaskProcessVO;
import org.zxwl.smart.mybatis.entity.archive.ArchiveTask;

@Validated
public interface ArchiveTaskService {

  PageResult<ArchiveTaskListVO> getArchiveTaskList(@NotNull @Valid ArchiveTaskListRequest request);

  ListResult<ArchiveTaskProcessVO> getArchiveTaskProcess(@NotNull @Valid ArchiveTaskProcessRequest request);

  void createArchiveTask(@NotNull @Valid List<ArchiveTask> archiveTaskList);

  BaseResult completeArchiveTaskListManual(@NotNull @Valid ArchiveTaskListCompleteManualRequest request);
}
