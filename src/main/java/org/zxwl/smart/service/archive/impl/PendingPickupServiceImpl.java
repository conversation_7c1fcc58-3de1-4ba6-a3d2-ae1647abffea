package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.PendingPickupConvert;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.constant.enums.archive.ArchiveTypeEnum;
import org.zxwl.smart.domain.request.pickup.PendingPickupCreateRequest;
import org.zxwl.smart.domain.request.pickup.PendingPickupDeleteRequest;
import org.zxwl.smart.domain.request.pickup.PendingPickupListRequest;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.PendingPickupVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.archive.ArchivePendingPickup;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;
import org.zxwl.smart.mybatis.ext.archive.ArchivePendingPickupExt;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchivePendingPickupMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper;
import org.zxwl.smart.service.archive.PendingPickupService;

@Service
@AllArgsConstructor
public class PendingPickupServiceImpl implements PendingPickupService {

  private final ArchivePendingPickupMapper pendingPickupMapper;
  private final ArchiveMapper archiveMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final TransactionTemplate transactionTemplate;

  @Override
  public PageResult<PendingPickupVO> getArchivePendingPickupList(PendingPickupListRequest request) {
    Long userId = AccountLocal.getUserId();
    String archiveName = request.getArchiveName();
    String archiveNameLike = request.getArchiveNameLike();
    String archiveNo = request.getArchiveNo();
    String archiveNoLike = request.getArchiveNoLike();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<ArchivePendingPickupExt> list =
        pendingPickupMapper.selectList(
            archiveName, archiveNameLike, archiveNo, archiveNoLike, userId);
    List<PendingPickupVO> voList = getPendingPickupVOList(list);
    return new PageResult<>(voList, list);
  }

  @Override
  public SimpleResult<BaseCreateListVO> createArchivePendingPickup(
      PendingPickupCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Long> archiveIdList = request.getArchiveIdList();
    List<Archive> archiveList = archiveMapper.selectListByIdList(archiveIdList, false);
    Map<Long, Archive> archiveMap =
        archiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (Long archiveId : archiveIdList) {
      Archive archive = archiveMap.get(archiveId);
      if (Objects.isNull(archive)) {
        return new SimpleResult<>("档案不存在，id 为" + archiveId);
      }
      if (ArchiveTypeEnum.VOLUME.equalsValue(archive.getType())) {
        return new SimpleResult<>("卷内档案暂不支持单独取出");
      }
    }
    List<ArchivePendingPickup> pendingPickupList =
        archiveList.stream()
            .map(
                archive -> {
                  ArchivePendingPickup pendingPickup = new ArchivePendingPickup();
                  pendingPickup.setArchiveId(archive.getId());
                  pendingPickup.setUserId(userId);
                  pendingPickup.setCreatedId(userId);
                  return pendingPickup;
                })
            .collect(Collectors.toList());
    transactionTemplate.execute(
        status -> {
          pendingPickupMapper.deleteByArchiveIdListAndUserId(archiveIdList, userId);
          pendingPickupMapper.insertList(pendingPickupList);
          return true;
        });
    List<Long> idList =
        pendingPickupList.stream().map(ArchivePendingPickup::getId).collect(Collectors.toList());
    return new SimpleResult<>(new BaseCreateListVO(idList));
  }

  @Override
  public BaseResult deleteArchivePendingPickup(PendingPickupDeleteRequest request) {
    List<Long> idList = request.getIdList();
    pendingPickupMapper.deleteByIdList(idList);
    return BaseResult.success();
  }

  private List<PendingPickupVO> getPendingPickupVOList(List<ArchivePendingPickupExt> list) {
    List<PendingPickupVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(list)) {
      return voList;
    }
    List<Long> archiveTypeIdList =
        list.stream()
            .map(ArchivePendingPickupExt::getArchiveTypeId)
            .distinct()
            .collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchivePendingPickupExt pendingPickup : list) {
      Long archiveTypeId = pendingPickup.getArchiveTypeId();
      ArchiveType archiveType = archiveTypeMap.get(archiveTypeId);
      PendingPickupVO vo = PendingPickupConvert.getPendingPickupVO(pendingPickup);
      vo.setArchiveTypeName(archiveType.getTypeName());
      if (Objects.nonNull(pendingPickup.getRackGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForRack(
                pendingPickup.getRackGroupNo(),
                pendingPickup.getRackColumnNo(),
                pendingPickup.getRackPanelNo(),
                pendingPickup.getRackSectionNo(),
                pendingPickup.getRackLayerNo(),
                pendingPickup.getRackGridNo()));
      }
      if (Objects.nonNull(pendingPickup.getCabinetGroupNo())) {
        vo.setLocation(
            LocationUtil.locationForCabinet(
                pendingPickup.getCabinetGroupNo(),
                pendingPickup.getCabinetNo(),
                pendingPickup.getCabinetGridNo()));
      }
      voList.add(vo);
    }
    return voList;
  }
}
