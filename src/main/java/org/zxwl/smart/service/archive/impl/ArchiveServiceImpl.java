package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.archive.ArchiveConvert;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.lang.Triple;
import org.zxwl.smart.common.utils.ArchiveUtil;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.constant.consist.ArchiveConstant;
import org.zxwl.smart.constant.consist.AuthorizeConstant;
import org.zxwl.smart.constant.consist.GlobalConstant;
import org.zxwl.smart.constant.enums.archive.*;
import org.zxwl.smart.domain.request.archive.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.*;
import org.zxwl.smart.mybatis.entity.archive.*;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGroup;
import org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.ext.archive.*;
import org.zxwl.smart.mybatis.mapper.archive.*;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.archive.ArchiveService;
import org.zxwl.smart.service.cabinet.CabinetService;
import org.zxwl.smart.service.cabinet.HrCabinetService;
import org.zxwl.smart.service.rack.RackGroupService;

@Service
@AllArgsConstructor
public class ArchiveServiceImpl implements ArchiveService {

  private final ArchiveMapper archiveMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final ArchiveFieldMapper archiveFieldMapper;
  private final ArchiveFieldValueMapper archiveFieldValueMapper;
  private final ArchiveOperatorRecordMapper archiveOperatorRecordMapper;
  private final WarehouseMapper warehouseMapper;
  private final UserMapper userMapper;
  private final ArchiveApplicantMapper archiveApplicantMapper;
  private final RackGroupMapper rackGroupMapper;
  private final HrCabinetGroupMapper hrCabinetGroupMapper;
  private final CabinetGroupMapper cabinetGroupMapper;
  private final RackGroupService rackGroupService;
  private final HrCabinetService hrCabinetService;
  private final CabinetService cabinetService;
  private final TransactionTemplate transactionTemplate;

  @Override
  public PageResult<ArchiveListVO> getArchiveList(ArchiveListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    ArchiveListWrapper wrapper = ArchiveConvert.getArchiveListWrapper(request);
    wrapper.setOrganizationId(organizationId);
    List<Archive> archiveList = archiveMapper.selectList(wrapper);
    List<ArchiveListVO> voList = getArchiveListVOList(archiveList);
    return new PageResult<>(voList, archiveList);
  }

  @Override
  public PageResult<ArchivePendingOutListVO> getArchivePendingOutListByAccountLocal(
      ArchivePendingOutListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    ArchivePendingOutListWrapper wrapper = ArchiveConvert.getArchivePendingOutListWrapper(request);
    wrapper.setOrganizationId(organizationId);
    wrapper.setUserId(userId);
    PageHelper.startPage(pageNum, pageSize);
    // 查出待取档案，转为archiveIdList，转为archiveIdWithApplicantIdMap
    List<ArchiveIdWithApplicantIdExt> archiveIdWithApplicantIdExtList =
        archiveMapper.selectPendingOutListByAccountLocal(wrapper);
    List<Long> archiveIdList =
        archiveIdWithApplicantIdExtList.stream()
            .map(ArchiveIdWithApplicantIdExt::getArchiveId)
            .distinct()
            .toList();
    List<Long> applicantIdList =
        archiveIdWithApplicantIdExtList.stream()
            .map(ArchiveIdWithApplicantIdExt::getApplicantId)
            .distinct()
            .toList();
    List<ArchivePendingOutListVO> voList = new ArrayList<>();
    if (CollUtil.isNotEmpty(archiveIdWithApplicantIdExtList)) {
      // 查询档案字段
      List<Archive> archiveList = archiveMapper.selectListByIdList(archiveIdList, false);
      voList = getArchivePendingOutListVOList(archiveList);
      // 查询申请记录字段
      List<ArchiveApplicant> applicantList =
          archiveApplicantMapper.selectListByIdList(applicantIdList, false);
      Map<Long, ArchiveApplicant> applicantMap =
          applicantList.stream().collect(Collectors.toMap(ArchiveApplicant::getArchiveId, v -> v));
      for (ArchivePendingOutListVO vo : voList) {
        ArchiveApplicant applicant = applicantMap.get(vo.getId());
        vo.setApplicantCreatedId(applicant.getCreatedId());
        vo.setApplicantCreatedAt(applicant.getCreatedAt());
      }
    }
    return new PageResult<>(voList, archiveIdWithApplicantIdExtList);
  }

  @Override
  public SimpleResult<ArchiveVO> getArchive(ArchiveRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    String tid = request.getTid();
    if (Objects.isNull(id) && StrUtil.isBlank(tid)) {
      return new SimpleResult<>("id或者tid不能同时为空");
    }
    Archive archive = archiveMapper.selectByIdOrTid(id, tid, false);
    if (Objects.isNull(archive)) {
      return new SimpleResult<>("档案或档案盒不存在");
    }
    /*
    if (!Objects.equals(organizationId, archive.getOrganizationId())) {
      return new SimpleResult<>("档案或档案盒不属于当前机构");
    }
    */
    Long archiveTypeId = archive.getArchiveTypeId();
    ArchiveType archiveType = archiveTypeMapper.selectById(archiveTypeId, true);
    Long archiveId = archive.getId();
    List<ArchiveFieldValue> archiveFieldValueList =
        archiveFieldValueMapper.selectListByArchiveId(archiveId);
    ArchiveVO vo = ArchiveConvert.getArchiveVO(archive);
    if (Objects.nonNull(archiveType)) {
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    vo.setBoxStatus(ArchiveUtil.getArchiveBoxStatus(archive));
    vo.setStorageType(ArchiveUtil.getArchiveStorageType(archive));
    vo.setLocation(LocationUtil.location(archive));
    vo.setFieldValueList(ArchiveConvert.getArchiveFieldValueList(archiveFieldValueList));
    return new SimpleResult<>(vo);
  }

  @Override
  public PageResult<ArchivePendingStorageListVO> getArchivePendingStorageList(
      ArchivePendingStorageListRequest request) {
    List<Date> pickupAtRange = request.getPickupAtRange();
    ArchivePendingStorageListWrapper wrapper = new ArchivePendingStorageListWrapper();
    wrapper.setArchiveName(request.getArchiveName());
    wrapper.setArchiveNameLike(request.getArchiveNameLike());
    wrapper.setArchiveNo(request.getArchiveNo());
    wrapper.setArchiveNoLike(request.getArchiveNoLike());
    if (CollUtil.isNotEmpty(pickupAtRange)) {
      wrapper.setBeginCreateAt(pickupAtRange.get(0));
      wrapper.setEndCreateAt(pickupAtRange.get(1));
    }
    PageHelper.startPage(request.getPageNum(), request.getPageSize());
    // 通过过滤条件查询所有出库状态档案
    List<Archive> archivePendingStorageList =
        archiveMapper.selectArchivePendingStorageList(wrapper);
    List<Long> archivePendingStorageIdList =
        archivePendingStorageList.stream().map(Archive::getId).toList();
    // 构造vo
    List<ArchivePendingStorageListVO> voList =
        ArchiveConvert.getArchivePendingStorageListVO(archivePendingStorageList);

    // 先用出库操作记录填充
    ArchiveOperatorRecordWrapper recordWrapper = new ArchiveOperatorRecordWrapper();
    List<Long> archiveIdList = voList.stream().map(ArchivePendingStorageListVO::getId).toList();
    recordWrapper.setArchiveIdList(archiveIdList);
    recordWrapper.setOperatorType(ArchiveOperatorTypeEnum.STOCK_OUT.getValue());
    List<ArchiveOperatorRecord> operatorRecordListOnlyId =
        archiveOperatorRecordMapper.selectLastArchiveOperatorRecord(recordWrapper);
    List<Long> recordIdList =
        operatorRecordListOnlyId.stream().map(ArchiveOperatorRecord::getId).toList();
    if (CollUtil.isNotEmpty(recordIdList)) {
      List<ArchiveOperatorRecord> operatorRecordList =
          archiveOperatorRecordMapper.selectListByIdList(recordIdList);
      if (CollUtil.isNotEmpty(operatorRecordList)) {
        Map<Long, ArchiveOperatorRecord> operatorRecordMap =
            operatorRecordList.stream()
                .collect(Collectors.toMap(ArchiveOperatorRecord::getArchiveId, v -> v));
        for (ArchivePendingStorageListVO vo : voList) {
          ArchiveOperatorRecord operatorRecord = operatorRecordMap.get(vo.getId());
          if (Objects.nonNull(operatorRecord)) {
            vo.setPickupCreatedId(operatorRecord.getOperatorId());
            vo.setPickupOperatorId(operatorRecord.getOperatorId());
            vo.setPickupTime(operatorRecord.getCreatedAt());
          }
        }
      }
    }

    // 查询出库状态档案最近一条任务和申请记录
    if (CollUtil.isNotEmpty(archivePendingStorageIdList)) {
      // 先找到档案对应的最近一条已完成出库任务id
      List<ArchiveTask> archiveLastCompletedOutTaskList =
          archiveMapper.selectArchiveLastCompletedOutTaskList(archivePendingStorageIdList);
      List<Long> taskIdList =
          archiveLastCompletedOutTaskList.stream().map(ArchiveTask::getId).toList();
      if (CollUtil.isNotEmpty(taskIdList)) {
        // 根据id查询其他字段信息
        List<ArchivePendingStorageExt> archivePendingStorageExtList =
            archiveMapper.selectArchivePendingStorageExtList(taskIdList);
        Map<Long, ArchivePendingStorageExt> archivePendingStorageExtMap =
            archivePendingStorageExtList.stream()
                .collect(Collectors.toMap(ArchivePendingStorageExt::getArchiveId, v -> v));
        for (ArchivePendingStorageListVO vo : voList) {
          ArchivePendingStorageExt ext = archivePendingStorageExtMap.get(vo.getId());
          // todo 这个时间是数据库生产的，得有个相同的时间或什么字段用来关联才行
          // 如果和操作记录是同时的，认为找到了对应的申请任务，覆盖字段
          if (Objects.nonNull(ext) && Objects.equals(ext.getPickupTime(), vo.getPickupTime())) {
            vo.setPickupCreatedId(ext.getPickupCreatedId());
            vo.setPickupOperatorId(ext.getPickupOperatorId());
            vo.setApplicantId(ext.getApplicantId());
          }
        }
      }
    }

    // 赋值ArchiveTypeName
    List<Long> archiveTypeIdList =
        voList.stream()
            .map(ArchivePendingStorageListVO::getArchiveTypeId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
    if (CollUtil.isNotEmpty(archiveTypeIdList)) {
      List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
      Map<Long, String> archiveTypeMap =
          archiveTypeList.stream()
              .collect(Collectors.toMap(ArchiveType::getId, ArchiveType::getTypeName));
      for (ArchivePendingStorageListVO vo : voList) {
        vo.setArchiveTypeName(archiveTypeMap.get(vo.getArchiveTypeId()));
      }
    }
    // 赋值CreatedName, OperatorName
    List<Long> userIdList =
        Stream.concat(
                voList.stream().map(ArchivePendingStorageListVO::getPickupCreatedId),
                voList.stream().map(ArchivePendingStorageListVO::getPickupOperatorId))
            .filter(Objects::nonNull)
            .distinct()
            .toList();
    if (CollUtil.isNotEmpty(userIdList)) {
      List<User> userList = userMapper.selectNameListByIdList(userIdList, true);
      Map<Long, String> userMap =
          userList.stream().collect(Collectors.toMap(User::getId, User::getUserName));
      for (ArchivePendingStorageListVO vo : voList) {
        vo.setPickupCreatedName(userMap.get(vo.getPickupCreatedId()));
        vo.setPickupOperatorName(userMap.get(vo.getPickupOperatorId()));
      }
    }
    return new PageResult<>(voList, archivePendingStorageList);
  }

  @Override
  public PageResult<ArchiveDeletedListVO> getDeletedArchiveList(ArchiveDeletedListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    ArchiveDeletedListWrapper wrapper = ArchiveConvert.getArchiveDeletedListWrapper(request);
    wrapper.setOrganizationId(organizationId);
    PageHelper.startPage(pageNum, pageSize);
    List<Archive> archiveList = archiveMapper.selectListForDeleted(wrapper);
    List<ArchiveDeletedListVO> voList = getArchiveDeletedListVOList(archiveList);
    return new PageResult<>(voList, archiveList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createArchive(ArchiveCreateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long archiveTypeId = request.getArchiveTypeId();
    Long parentId = request.getParentId();
    String archiveName = request.getArchiveName();
    String archiveNo = request.getArchiveNo();
    List<ArchiveFieldValueRequest> fieldValueRequestList = request.getFieldValueList();
    // 数据查询
    ArchiveType archiveType = archiveTypeMapper.selectById(archiveTypeId, false);
    List<ArchiveField> archiveFieldList =
        archiveFieldMapper.selectListByArchiveTypeId(archiveTypeId);
    // 判断
    SimpleResult<BaseCreateVO> baseResult = checkPreCreate(request, organizationId, archiveType);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    BaseResult result =
        checkArchiveFieldValueRequest(
            archiveFieldList, archiveName, archiveNo, fieldValueRequestList);
    if (Objects.nonNull(result)) {
      return new SimpleResult<>(result);
    }
    // 转换
    Archive archive = ArchiveConvert.getArchive(request);
    archive.setOrganizationId(organizationId);
    archive.setStockStatus(ArchiveStockStatusEnum.OUT.getValue());
    archive.setShelvingStatus(ArchiveShelvingStatusEnum.OUT.getValue());
    archive.setCreatedId(userId);
    archive.setUpdatedId(userId);
    List<ArchiveFieldValue> archiveFieldValueList = new ArrayList<>();
    for (ArchiveFieldValueRequest fieldValueRequest : fieldValueRequestList) {
      Long fieldId = fieldValueRequest.getFieldId();
      ArchiveFieldValue archiveFieldValue = new ArchiveFieldValue();
      archiveFieldValue.setArchivesFieldId(fieldId);
      archiveFieldValue.setFieldName(fieldValueRequest.getFieldName());
      archiveFieldValue.setFieldValue(fieldValueRequest.getFieldValue());
      archiveFieldValueList.add(archiveFieldValue);
    }
    ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
    operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.CREATE.getValue());
    operatorRecord.setOperatorId(userId);
    // 入库
    transactionTemplate.execute(
        status -> {
          archiveMapper.insert(archive);
          Long archiveId = archive.getId();
          archiveFieldValueList.forEach(v -> v.setArchiveId(archiveId));
          if (CollUtil.isNotEmpty(archiveFieldValueList)) {
            archiveFieldValueMapper.insertList(archiveFieldValueList);
          }
          operatorRecord.setArchiveId(archiveId);
          archiveOperatorRecordMapper.insert(operatorRecord);
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(archive.getId()));
  }

  @Override
  public BaseResult updateArchive(ArchiveUpdateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Long archiveTypeId = request.getArchiveTypeId();
    String archiveName = request.getArchiveName();
    String archiveNo = request.getArchiveNo();
    List<ArchiveFieldValueRequest> fieldValueRequestList = request.getFieldValueList();
    Archive orgArchive = archiveMapper.selectByIdOrTid(id, null, false);
    List<ArchiveField> archiveFieldList =
        archiveFieldMapper.selectListByArchiveTypeId(archiveTypeId);
    List<Archive> orgSubArchiveList = archiveMapper.selectListByParentId(id);
    boolean isUpdateLocation =
        Objects.nonNull(orgArchive)
            && (!Objects.equals(request.getRackGroupNo(), orgArchive.getRackGroupNo())
                || !Objects.equals(request.getRackColumnNo(), orgArchive.getRackColumnNo())
                || !Objects.equals(request.getRackPanelNo(), orgArchive.getRackPanelNo())
                || !Objects.equals(request.getRackSectionNo(), orgArchive.getRackSectionNo())
                || !Objects.equals(request.getRackLayerNo(), orgArchive.getRackLayerNo())
                || !Objects.equals(request.getRackGridNo(), orgArchive.getRackGridNo())
                || !Objects.equals(request.getHrCabinetGroupNo(), orgArchive.getHrCabinetGroupNo())
                || !Objects.equals(request.getHrCabinetNo(), orgArchive.getHrCabinetNo())
                || !Objects.equals(request.getHrCabinetLayerNo(), orgArchive.getHrCabinetLayerNo())
                || !Objects.equals(request.getHrCabinetGridNo(), orgArchive.getHrCabinetGridNo())
                || !Objects.equals(request.getCabinetGroupNo(), orgArchive.getCabinetGroupNo())
                || !Objects.equals(request.getCabinetNo(), orgArchive.getCabinetNo())
                || !Objects.equals(request.getCabinetGridNo(), orgArchive.getCabinetGridNo()));
    // 判断
    BaseResult baseResult = checkPreUpdate(request, organizationId, orgArchive, isUpdateLocation);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    BaseResult result =
        checkArchiveFieldValueRequest(
            archiveFieldList, archiveName, archiveNo, fieldValueRequestList);
    if (Objects.nonNull(result)) {
      return result;
    }
    // 转换
    Pair<Archive, List<Archive>> pair =
        buildArchiveAndSubArchiveListForUpdate(
            request, orgSubArchiveList, userId, isUpdateLocation);
    Archive archive = pair.getLeft();
    List<Archive> subArchiveList = pair.getRight();
    List<ArchiveFieldValue> archiveFieldValueList = new ArrayList<>();
    for (ArchiveFieldValueRequest fieldValueRequest : fieldValueRequestList) {
      Long fieldId = fieldValueRequest.getFieldId();
      ArchiveFieldValue archiveFieldValue = new ArchiveFieldValue();
      archiveFieldValue.setArchivesFieldId(fieldId);
      archiveFieldValue.setFieldName(fieldValueRequest.getFieldName());
      archiveFieldValue.setFieldValue(fieldValueRequest.getFieldValue());
      archiveFieldValueList.add(archiveFieldValue);
    }
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForUpdate(
            archive, orgArchive, subArchiveList, orgSubArchiveList, userId, isUpdateLocation);
    // 入库
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateById(archive);
          if (CollUtil.isNotEmpty(subArchiveList)) {
            archiveMapper.updateListForLocation(subArchiveList);
          }
          archiveFieldValueMapper.deleteByArchiveId(id);
          archiveFieldValueList.forEach(v -> v.setArchiveId(id));
          if (CollUtil.isNotEmpty(archiveFieldValueList)) {
            archiveFieldValueMapper.insertList(archiveFieldValueList);
          }
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult updateArchiveStockStatus(UpdateStockStatusRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    List<Long> idList = request.getIdList();
    String stockStatus = request.getStockStatus();
    Long warehouseId = request.getWarehouseId();
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(idList, false);
    List<Archive> orgSubArchiveList = archiveMapper.selectListByParentIdList(idList);
    // 判断
    BaseResult baseResult =
        checkPreUpdateStockStatus(idList, stockStatus, warehouseId, organizationId, orgArchiveList);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForUpdateStockStatus(
            orgArchiveList, orgSubArchiveList, warehouseId, stockStatus, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateStockStatusByIdList(idList, warehouseId, stockStatus, userId);
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult updateDeletedArchiveRestored(ArchiveRestoreRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Long> idList = request.getIdList();
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(idList, true);
    // 判断
    BaseResult baseResult = checkPreDeleteRestore(idList, orgArchiveList);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForDeleteRestore(orgArchiveList, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateDeleteAtByIdList(idList, GlobalConstant.LOGIC_UNDELETE);
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArchive(ArchiveDeleteRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Long> idList = request.getIdList();
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(idList, false);
    List<Archive> orgSubArchiveList = archiveMapper.selectListByParentIdList(idList);
    Map<Long, Archive> orgSubArchiveMap =
        orgSubArchiveList.stream().collect(Collectors.toMap(Archive::getParentId, v -> v));
    // 判断
    BaseResult baseResult = checkPreDelete(idList, orgArchiveList, orgSubArchiveMap);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForDelete(orgArchiveList, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateDeleteAtByIdList(idList, System.currentTimeMillis());
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          // todo 删除档案待取表（购物车） 、可能也要考虑是否在申请中
          return true;
        });

    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArchivePhysically(ArchiveDeleteRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    List<Long> idList = request.getIdList();
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(idList, true);
    // 判断
    BaseResult baseResult = checkPreDeletePhysically(idList, organizationId, orgArchiveList);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForDeletePhysically(orgArchiveList, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.deletePhysicallyByIdList(idList);
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult archiveBindTID(ArchiveBindTIDRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    String tid = request.getTid();
    Archive orgArchive = archiveMapper.selectByIdOrTid(id, null, false);
    if (Objects.isNull(orgArchive)) {
      return BaseResult.failed("档案或档案盒不存在");
    }
    if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
      return BaseResult.failed("档案或档案盒不属于当前机构");
    }
    Archive tidArchive = archiveMapper.selectByIdOrTid(null, tid, false);
    if (Objects.nonNull(tidArchive)) {
      return BaseResult.failed("该 TID 已被其他档案或档案盒绑定");
    }
    ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(orgArchive);
    operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.BIND_TID.getValue());
    operatorRecord.setOperatorId(userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateTidById(id, tid);
          archiveOperatorRecordMapper.insert(operatorRecord);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult updateArchiveTID(ArchiveUpdateTIDRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    String tid = request.getTid();
    Archive orgArchive = archiveMapper.selectByIdOrTid(id, null, false);
    if (Objects.isNull(orgArchive)) {
      return BaseResult.failed("档案或档案盒不存在");
    }
    if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
      return BaseResult.failed("档案或档案盒不属于当前机构");
    }
    if (!Objects.equals(tid, orgArchive.getTid())) {
      Archive tidArchive = archiveMapper.selectByIdOrTid(null, tid, false);
      if (Objects.nonNull(tidArchive)) {
        return BaseResult.failed("该 TID 已被其他档案或档案盒绑定");
      }
    }
    ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(orgArchive);
    operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UPDATE_TID.getValue());
    operatorRecord.setOperatorId(userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateTidById(id, tid);
          archiveOperatorRecordMapper.insert(operatorRecord);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult unbindArchiveTID(ArchiveUnbindTIDRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    List<Long> idList = request.getIdList();
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(idList, false);
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (Long id : idList) {
      Archive archive = orgArchiveMap.get(id);
      if (Objects.isNull(archive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
      if (!Objects.equals(organizationId, archive.getOrganizationId())) {
        return BaseResult.failed("档案或档案盒不属于当前机构，id 为 " + id);
      }
      if (Objects.isNull(archive.getTid())) {
        return BaseResult.failed("档案或档案盒没有绑定 TID，id 为 " + id);
      }
    }
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive orgArchive : orgArchiveMap.values()) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(orgArchive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UNBIND_TID.getValue());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateTidByIdList(idList, null);
          archiveOperatorRecordMapper.insertList(operatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult updateArchiveLocation(UpdateLocationRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    List<UpdateLocationRequest.ArchiveRequest> archiveRequestList = request.getArchiveList();
    List<Long> archiveIdList =
        archiveRequestList.stream()
            .map(UpdateLocationRequest.ArchiveRequest::getId)
            .collect(Collectors.toList());
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(archiveIdList, false);
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (UpdateLocationRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      Long id = archiveRequest.getId();
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案或档案盒不属于当前机构，id 为 " + id);
      }
      if (ArchiveShelvingStatusEnum.IN.getValue().equals(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案或档案盒已上架，不允许修改在架信息");
      }
    }
    List<Archive> archiveList = new ArrayList<>();
    for (UpdateLocationRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      Archive archive = ArchiveConvert.getArchive(archiveRequest);
      archive.setShelvingStatus(ArchiveShelvingStatusEnum.PENDING_IN.getValue());
      archiveList.add(archive);
    }
    List<ArchiveOperatorRecord> operatorRecordList =
        buildArchiveOperatorRecordListForUpdateLocation(archiveRequestList, orgArchiveMap, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateListForLocation(archiveList);
          archiveOperatorRecordMapper.insertList(operatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult archiveShelve(ArchiveShelveRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    List<ArchiveShelveRequest.ArchiveRequest> archiveRequestList = request.getArchiveList();
    // 必要参数查询
    // 档案信息查询
    List<Long> archiveIdList =
        archiveRequestList.stream()
            .map(ArchiveShelveRequest.ArchiveRequest::getId)
            .collect(Collectors.toList());
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(archiveIdList, false);
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    // 子档案信息查询
    List<Archive> orgSubArchiveList = archiveMapper.selectListByParentIdList(archiveIdList);
    // 载具查询
    Triple<Map<Integer, RackGroup>, Map<Integer, HrCabinetGroup>, Map<Integer, CabinetGroup>>
        vehicleTriple = queryVehicleForShelve(archiveRequestList);
    Map<Integer, RackGroup> rackGroupMap = vehicleTriple.getLeft();
    Map<Integer, HrCabinetGroup> hrCabinetGroupMap = vehicleTriple.getMiddle();
    Map<Integer, CabinetGroup> cabinetGroupMap = vehicleTriple.getRight();
    // 信息判断及检查
    BaseResult baseResult =
        checkPreArchiveShelve(
            archiveRequestList,
            organizationId,
            orgArchiveMap,
            rackGroupMap,
            hrCabinetGroupMap,
            cabinetGroupMap);
    if (Objects.nonNull(baseResult)) {
      return baseResult;
    }
    // DO 构建
    // 档案更新DO构建
    List<Archive> archiveList =
        buildArchiveListForArchiveShelve(
            archiveRequestList,
            orgSubArchiveList,
            rackGroupMap,
            hrCabinetGroupMap,
            cabinetGroupMap,
            userId);
    // 构建档案操作记录
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForShelve(
            archiveList, orgArchiveMap, orgSubArchiveList, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateListForShelving(archiveList);
          // 增加载具已用容量
          for (Archive archive : archiveList) {
            if (Objects.nonNull(archive.getRackGroupNo())) {
              rackGroupService.updateRackUsedCapacityAsync(archive.getRackGroupNo());
            } else if (Objects.nonNull(archive.getHrCabinetGroupNo())) {
              hrCabinetService.updateHrCabinetUsedCapacityAsync(archive.getHrCabinetGroupNo());
            } else if (Objects.nonNull(archive.getCabinetGroupNo())) {
              cabinetService.updateCabinetUsedCapacityAsync(archive.getCabinetGroupNo());
            }
          }
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult archiveUnshelve(ArchiveUnshelveRequest request) {
    String terminal = AccountLocal.getTerminal();
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    List<Long> idList = request.getIdList().stream().distinct().collect(Collectors.toList());
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(idList, false);
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    List<Archive> orgSubArchiveList = archiveMapper.selectListByParentIdList(idList);
    for (Long id : idList) {
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
      String archiveName = orgArchive.getArchiveName();
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）不属于当前机构");
      }
      if (ArchiveShelvingStatusEnum.OUT.getValue().equals(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）已下架，不允许重复下架");
      }
      if (Objects.nonNull(orgArchive.getParentId())) {
        return BaseResult.failed("盒内或卷内（" + archiveName + "）不允许单独上架");
      }
    }
    List<Long> subArchiveIdList =
        orgSubArchiveList.stream().map(Archive::getId).collect(Collectors.toList());
    List<Long> updateArchiveIdList = new ArrayList<>(idList);
    if (CollUtil.isNotEmpty(subArchiveIdList)) {
      updateArchiveIdList.addAll(subArchiveIdList);
    }
    // 智能载具下架即出库
    String stockStatus =
        AuthorizeConstant.isSmartVehicle(terminal)
            ? ArchiveStockStatusEnum.OUT.getValue()
            : ArchiveStockStatusEnum.IN.getValue();
    String shelvingStatus = ArchiveShelvingStatusEnum.OUT.getValue();
    // 构建档案操作记录
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForUnshelve(
            orgArchiveList, orgSubArchiveList, userId, stockStatus);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateStatusByIdList(
              updateArchiveIdList, stockStatus, shelvingStatus, userId);
          // 扣减载具已用容量
          for (Archive archive : orgArchiveList) {
            if (Objects.nonNull(archive.getRackGroupNo())) {
              rackGroupService.updateRackUsedCapacityAsync(archive.getRackGroupNo());
            } else if (Objects.nonNull(archive.getHrCabinetGroupNo())) {
              hrCabinetService.updateHrCabinetUsedCapacityAsync(archive.getHrCabinetGroupNo());
            } else if (Objects.nonNull(archive.getCabinetGroupNo())) {
              cabinetService.updateCabinetUsedCapacityAsync(archive.getCabinetGroupNo());
            }
          }
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult archiveBoxing(ArchiveBoxingRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    List<Long> archiveIdList = request.getArchiveIdList();
    Archive orgArchiveBox = archiveMapper.selectByIdOrTid(id, null, false);
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(archiveIdList, false);
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    if (Objects.isNull(orgArchiveBox)
        || !ArchiveTypeEnum.BOX.equalsValue(orgArchiveBox.getType())) {
      return BaseResult.failed("档案盒不存在");
    }
    if (ArchiveShelvingStatusEnum.IN.equalsValue(orgArchiveBox.getShelvingStatus())) {
      return BaseResult.failed("档案盒（" + orgArchiveBox.getArchiveName() + "）已上架，不允许进行装盒");
    }
    if (!Objects.equals(organizationId, orgArchiveBox.getOrganizationId())) {
      return BaseResult.failed("档案盒不属于当前机构");
    }
    for (Long archiveId : archiveIdList) {
      Archive orgArchive = orgArchiveMap.get(archiveId);
      if (Objects.isNull(orgArchive)
          || !ArchiveTypeEnum.ARCHIVE.equalsValue(orgArchive.getType())) {
        return BaseResult.failed("档案不存在，id 为 " + archiveId);
      }
      String archiveName = orgArchive.getArchiveName();
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案（" + archiveName + "）不属于当前机构");
      }
      if (ArchiveShelvingStatusEnum.IN.equalsValue(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案（" + archiveName + "）已上架，不允许进行装盒");
      }
    }
    // 档案位置更新
    List<Archive> archiveList = new ArrayList<>();
    for (Archive orgArchive : orgArchiveList) {
      Archive archive = new Archive();
      archive.setId(orgArchive.getId());
      archive.setWarehouseId(orgArchiveBox.getWarehouseId());
      archive.setParentId(orgArchiveBox.getId());
      archive.setRackGroupNo(orgArchiveBox.getRackGroupNo());
      archive.setRackColumnNo(orgArchiveBox.getRackColumnNo());
      archive.setRackPanelNo(orgArchiveBox.getRackPanelNo());
      archive.setRackSectionNo(orgArchiveBox.getRackSectionNo());
      archive.setRackLayerNo(orgArchiveBox.getRackLayerNo());
      archive.setRackGridNo(orgArchiveBox.getRackGridNo());
      archive.setHrCabinetGroupNo(orgArchiveBox.getHrCabinetGroupNo());
      archive.setHrCabinetNo(orgArchiveBox.getHrCabinetNo());
      archive.setHrCabinetLayerNo(orgArchiveBox.getHrCabinetLayerNo());
      archive.setHrCabinetGridNo(orgArchiveBox.getHrCabinetGridNo());
      archive.setCabinetGroupNo(orgArchiveBox.getCabinetGroupNo());
      archive.setCabinetNo(orgArchiveBox.getCabinetNo());
      archive.setCabinetGridNo(orgArchiveBox.getCabinetGridNo());
      archive.setUpdatedId(userId);
      archiveList.add(archive);
    }
    // 档案装盒记录生成
    List<ArchiveOperatorRecord> archiveOperatorRecordList =
        buildArchiveOperatorRecordListForBoxing(archiveList, orgArchiveMap, userId);
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateListForBoxing(archiveList);
          archiveOperatorRecordMapper.insertList(archiveOperatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  @Override
  public BaseResult archiveUnboxing(ArchiveUnboxingRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    List<Long> archiveIdList = request.getArchiveIdList();
    List<Archive> orgArchiveList = archiveMapper.selectListByIdList(archiveIdList, false);
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (Long archiveId : archiveIdList) {
      Archive orgArchive = orgArchiveMap.get(archiveId);
      if (Objects.isNull(orgArchive)
          || !ArchiveTypeEnum.ARCHIVE.equalsValue(orgArchive.getType())) {
        return BaseResult.failed("档案不存在，id 为 " + archiveId);
      }
      String archiveName = orgArchive.getArchiveName();
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案（" + archiveName + "）不属于当前机构");
      }
      if (ArchiveShelvingStatusEnum.IN.equalsValue(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案（" + archiveName + "）已上架，不允许进行拆盒");
      }
    }
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive orgArchive : orgArchiveList) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(orgArchive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UNBOXING.getValue());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    transactionTemplate.execute(
        status -> {
          archiveMapper.updateParentIdByIdList(archiveIdList, null, userId);
          archiveOperatorRecordMapper.insertList(operatorRecordList);
          return true;
        });
    return BaseResult.success();
  }

  private Triple<Map<Integer, RackGroup>, Map<Integer, HrCabinetGroup>, Map<Integer, CabinetGroup>>
      queryVehicleForShelve(List<ArchiveShelveRequest.ArchiveRequest> archiveRequestList) {
    List<Integer> rackGroupNoList =
        archiveRequestList.stream()
            .map(ArchiveShelveRequest.ArchiveRequest::getRackGroupNo)
            .distinct()
            .filter(Objects::nonNull)
            .toList();
    List<Integer> hrCabinetGroupNoList =
        archiveRequestList.stream()
            .map(ArchiveShelveRequest.ArchiveRequest::getHrCabinetGroupNo)
            .distinct()
            .filter(Objects::nonNull)
            .toList();
    List<Integer> cabinetGroupNoList =
        archiveRequestList.stream()
            .map(ArchiveShelveRequest.ArchiveRequest::getCabinetGroupNo)
            .distinct()
            .filter(Objects::nonNull)
            .toList();
    Map<Integer, RackGroup> rackGroupMap = Collections.emptyMap();
    Map<Integer, HrCabinetGroup> hrCabinetGroupMap = Collections.emptyMap();
    Map<Integer, CabinetGroup> cabinetGroupMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(rackGroupNoList)) {
      List<RackGroup> rackGroupList = rackGroupMapper.selectListByGroupNoList(rackGroupNoList);
      rackGroupMap =
          rackGroupList.stream().collect(Collectors.toMap(RackGroup::getGroupNo, r -> r));
    }
    if (CollUtil.isNotEmpty(hrCabinetGroupNoList)) {
      List<HrCabinetGroup> hrCabinetGroupList =
          hrCabinetGroupMapper.selectListByGroupNoList(hrCabinetGroupNoList);
      hrCabinetGroupMap =
          hrCabinetGroupList.stream().collect(Collectors.toMap(HrCabinetGroup::getGroupNo, r -> r));
    }
    if (CollUtil.isNotEmpty(cabinetGroupNoList)) {
      List<CabinetGroup> cabinetGroupList =
          cabinetGroupMapper.selectListByGroupNoList(cabinetGroupNoList);
      cabinetGroupMap =
          cabinetGroupList.stream().collect(Collectors.toMap(CabinetGroup::getGroupNo, r -> r));
    }
    return Triple.of(rackGroupMap, hrCabinetGroupMap, cabinetGroupMap);
  }

  private BaseResult checkArchiveFieldValueRequest(
      List<ArchiveField> archiveFieldList,
      String archiveName,
      String archiveNo,
      List<ArchiveFieldValueRequest> fieldValueRequestList) {
    Map<Long, ArchiveFieldValueRequest> fieldValueRequestMap =
        fieldValueRequestList.stream()
            .collect(Collectors.toMap(ArchiveFieldValueRequest::getFieldId, v -> v, (v, v2) -> v));
    for (ArchiveField archiveField : archiveFieldList) {
      Long fieldId = archiveField.getId();
      String fieldName = archiveField.getFieldName();
      String fixedFieldName = archiveField.getFixedFieldName();
      if (ArchiveFixedFieldEnum.ARCHIVE_NAME.equalsValue(fixedFieldName)) {
        if (archiveField.getRequired() && StrUtil.isBlank(archiveName)) {
          return BaseResult.failed(fieldName + "不能为空");
        }
      } else if (ArchiveFixedFieldEnum.ARCHIVE_NO.equalsValue(fixedFieldName)) {
        if (archiveField.getRequired() && StrUtil.isBlank(archiveNo)) {
          return BaseResult.failed(fieldName + "不能为空");
        }
      } else {
        ArchiveFieldValueRequest fieldValueRequest = fieldValueRequestMap.get(fieldId);
        if (archiveField.getRequired() && Objects.isNull(fieldValueRequest)) {
          return BaseResult.failed(fieldName + "不能为空");
        }
      }
    }
    Map<Long, ArchiveField> archiveFieldMap =
        archiveFieldList.stream().collect(Collectors.toMap(ArchiveField::getId, v -> v));
    for (ArchiveFieldValueRequest fieldValueRequest : fieldValueRequestList) {
      Long fieldId = fieldValueRequest.getFieldId();
      String fieldName = fieldValueRequest.getFieldName();
      ArchiveField archiveField = archiveFieldMap.get(fieldId);
      if (Objects.isNull(archiveField)) {
        return BaseResult.failed(fieldName + "不存在");
      }
      if (!StrUtil.equals(fieldName, archiveField.getFieldName())) {
        return BaseResult.failed(fieldName + "名字不匹配");
      }
    }
    return null;
  }

  private SimpleResult<BaseCreateVO> checkPreCreate(
      ArchiveCreateRequest request, Long organizationId, ArchiveType archiveType) {
    Long warehouseId = request.getWarehouseId();
    Long archiveTypeId = request.getArchiveTypeId();
    Long parentId = request.getParentId();
    String type = request.getType();
    String archiveNo = request.getArchiveNo();
    // 库房信息判断
    if (Objects.nonNull(warehouseId) && warehouseMapper.existById(warehouseId) == 0) {
      return new SimpleResult<>("库房不存在");
    }
    // 档案门类判断
    if (Objects.isNull(archiveType)) {
      return new SimpleResult<>("档案门类不存在");
    }
    if (!Objects.equals(organizationId, archiveType.getOrganizationId())) {
      return new SimpleResult<>("关联档案门类不属于当前机构");
    }
    if (ArchiveTypeEnum.BOX.equalsValue(type)) {
      if (!ArchiveTypeTypeEnum.BOX.equalsValue(archiveType.getType())) {
        return new SimpleResult<>("档案盒只能关联档案盒门类");
      }
    } else {
      if (!ArchiveTypeTypeEnum.ARCHIVE.equalsValue(archiveType.getType())) {
        return new SimpleResult<>("档案只能关联档案门类");
      }
    }
    // 档案相关判断
    if (archiveMapper.existArchiveTypeIdAndArchiveNo(archiveTypeId, archiveNo) > 0) {
      return new SimpleResult<>("该门类下档案编号已存在");
    }
    if (ArchiveTypeEnum.BOX.equalsValue(type) && Objects.nonNull(parentId)) {
      return new SimpleResult<>("档案盒不允许再关联父级");
    }
    if (ArchiveTypeEnum.VOLUME.equalsValue(type) && Objects.isNull(parentId)) {
      return new SimpleResult<>("卷内档案必须关联父档案");
    }
    // 存在父级时相关判断
    if (Objects.nonNull(parentId)) {
      Archive parentArchive = archiveMapper.selectByIdOrTid(parentId, null, false);
      if (Objects.isNull(parentArchive)) {
        return new SimpleResult<>("关联档案盒或父档案不存在");
      }
      if (ArchiveTypeEnum.VOLUME.getValue().equals(parentArchive.getType())) {
        return new SimpleResult<>("卷内档案内部不允许再创建档案");
      }
      if (!Objects.equals(organizationId, parentArchive.getOrganizationId())) {
        return new SimpleResult<>("关联档案盒或父档案不属于当前机构");
      }
      if (archiveMapper.countByParentId(parentId) > ArchiveConstant.MAX_SUB_ARCHIVE_COUNT) {
        return new SimpleResult<>(
            "关联档案盒或父档案内部档案数量已达" + ArchiveConstant.MAX_SUB_ARCHIVE_COUNT + "份上限");
      }
    }
    // 位置信息判断
    Integer rackGroupNo = request.getRackGroupNo();
    Integer rackColumnNo = request.getRackColumnNo();
    Integer rackPanelNo = request.getRackPanelNo();
    Integer rackSectionNo = request.getRackSectionNo();
    Integer rackLayerNo = request.getRackLayerNo();
    Integer rackGridNo = request.getRackGridNo();
    Integer hrCabinetGroupNo = request.getHrCabinetGroupNo();
    Integer hrCabinetNo = request.getHrCabinetNo();
    Integer hrCabinetLayerNo = request.getHrCabinetLayerNo();
    Integer hrCabinetGridNo = request.getHrCabinetGridNo();
    Integer cabinetGroupNo = request.getCabinetGroupNo();
    Integer cabinetNo = request.getCabinetNo();
    Integer cabinetGridNo = request.getCabinetGridNo();
    boolean hasRack =
        Stream.of(rackGroupNo, rackColumnNo, rackPanelNo, rackSectionNo, rackLayerNo, rackGridNo)
            .anyMatch(Objects::nonNull);
    boolean hasHrCabinet =
        Stream.of(hrCabinetGroupNo, hrCabinetNo, hrCabinetLayerNo, hrCabinetGridNo)
            .anyMatch(Objects::nonNull);
    boolean hasCabinet =
        Stream.of(cabinetGroupNo, cabinetNo, cabinetGridNo).anyMatch(Objects::nonNull);
    if ((hasRack && hasHrCabinet) || (hasHrCabinet && hasCabinet) || (hasRack && hasCabinet)) {
      return new SimpleResult<>("档案位置信息互斥");
    }
    if (Objects.nonNull(parentId) && (hasRack || hasHrCabinet || hasCabinet)) {
      return new SimpleResult<>("盒内或卷内档案不允许携带位置信息");
    }
    return null;
  }

  private BaseResult checkPreUpdate(
      ArchiveUpdateRequest request,
      Long organizationId,
      Archive orgArchive,
      boolean isUpdateLocation) {
    Long warehouseId = request.getWarehouseId();
    Long archiveTypeId = request.getArchiveTypeId();
    Long parentId = request.getParentId();
    String archiveNo = request.getArchiveNo();
    Integer rackGroupNo = request.getRackGroupNo();
    Integer rackColumnNo = request.getRackColumnNo();
    Integer rackPanelNo = request.getRackPanelNo();
    Integer rackSectionNo = request.getRackSectionNo();
    Integer rackLayerNo = request.getRackLayerNo();
    Integer rackGridNo = request.getRackGridNo();
    Integer hrCabinetGroupNo = request.getHrCabinetGroupNo();
    Integer hrCabinetNo = request.getHrCabinetNo();
    Integer hrCabinetLayerNo = request.getHrCabinetLayerNo();
    Integer hrCabinetGridNo = request.getHrCabinetGridNo();
    Integer cabinetGroupNo = request.getCabinetGroupNo();
    Integer cabinetNo = request.getCabinetNo();
    Integer cabinetGridNo = request.getCabinetGridNo();
    // 档案基础信息判断
    if (Objects.nonNull(warehouseId) && warehouseMapper.existById(warehouseId) == 0) {
      return BaseResult.failed("库房不存在");
    }
    if (Objects.isNull(orgArchive)) {
      return BaseResult.failed("档案或档案盒不存在");
    }
    if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
      return BaseResult.failed("该档案或档案盒不属于当前机构");
    }
    // 档案门类信息判断
    String type = orgArchive.getType();
    if (!archiveTypeId.equals(orgArchive.getArchiveTypeId())) {
      ArchiveType archiveType = archiveTypeMapper.selectById(archiveTypeId, false);
      if (Objects.isNull(archiveType)) {
        return BaseResult.failed("档案门类不存在");
      }
      if (ArchiveTypeEnum.BOX.equalsValue(type)) {
        if (!ArchiveTypeTypeEnum.BOX.equalsValue(archiveType.getType())) {
          return BaseResult.failed("档案盒只能关联档案盒门类");
        }
      } else {
        if (!ArchiveTypeTypeEnum.ARCHIVE.equalsValue(archiveType.getType())) {
          return BaseResult.failed("档案只能关联档案门类");
        }
      }
      if (!Objects.equals(organizationId, archiveType.getOrganizationId())) {
        return BaseResult.failed("关联门类不属于当前机构");
      }
    }
    if (!Objects.equals(archiveTypeId, orgArchive.getArchiveTypeId())
        || !StrUtil.equals(archiveNo, orgArchive.getArchiveNo())) {
      if (archiveMapper.existArchiveTypeIdAndArchiveNo(archiveTypeId, archiveNo) > 0) {
        return BaseResult.failed("该门类下档案编号已存在");
      }
    }
    // 档案父级信息判断
    if (ArchiveTypeEnum.BOX.equalsValue(type) && Objects.nonNull(parentId)) {
      return BaseResult.failed("档案盒不允许再关联父级");
    }
    if (ArchiveTypeEnum.VOLUME.equalsValue(type) && Objects.isNull(parentId)) {
      return BaseResult.failed("卷内档案必须关联父档案");
    }
    if (Objects.nonNull(parentId) && Objects.equals(parentId, orgArchive.getParentId())) {
      Archive parentArchive = archiveMapper.selectByIdOrTid(parentId, null, false);
      if (Objects.isNull(parentArchive)) {
        return BaseResult.failed("档案盒或父档案不存在");
      }
      if (ArchiveTypeEnum.VOLUME.getValue().equals(parentArchive.getType())) {
        return BaseResult.failed("卷内档案内部不允许再创建档案");
      }
      if (!Objects.equals(organizationId, parentArchive.getOrganizationId())) {
        return BaseResult.failed("关联档案盒或父档案不属于当前机构");
      }
    }
    // 档案位置信息判断
    if (isUpdateLocation) {
      if (Objects.nonNull(parentId)) {
        return new SimpleResult<>("盒内或卷内档案不允许更改位置信息");
      }
      if (ArchiveShelvingStatusEnum.IN.getValue().equals(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案已上架，不允许修改在架信息");
      }
      boolean hasRack =
          Stream.of(rackGroupNo, rackColumnNo, rackPanelNo, rackSectionNo, rackLayerNo, rackGridNo)
              .anyMatch(Objects::nonNull);
      boolean hasHrCabinet =
          Stream.of(hrCabinetGroupNo, hrCabinetNo, hrCabinetLayerNo, hrCabinetGridNo)
              .anyMatch(Objects::nonNull);
      boolean hasCabinet =
          Stream.of(cabinetGroupNo, cabinetNo, cabinetGridNo).anyMatch(Objects::nonNull);
      if ((hasRack && hasHrCabinet) || (hasHrCabinet && hasCabinet) || (hasRack && hasCabinet)) {
        return new SimpleResult<>("档案位置信息互斥");
      }
    }
    return null;
  }

  private BaseResult checkPreUpdateStockStatus(
      List<Long> idList,
      String stockStatus,
      Long warehouseId,
      Long organizationId,
      List<Archive> orgArchiveList) {
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    // 库房判断
    if (Objects.isNull(warehouseId)) {
      return BaseResult.failed("必须指定入库的库房");
    }
    if (warehouseMapper.existById(warehouseId) == 0) {
      return BaseResult.failed("库房不存在");
    }
    // 档案信息判断
    for (Long id : idList) {
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
      String archiveName = orgArchive.getArchiveName();
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）不属于当前机构");
      }
      if (ArchiveStockStatusEnum.IN.getValue().equals(stockStatus)
          && ArchiveStockStatusEnum.IN.getValue().equals(orgArchive.getStockStatus())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）目前是在库状态，不允许重复入库");
      }
      if (ArchiveStockStatusEnum.OUT.getValue().equals(stockStatus)
          && ArchiveStockStatusEnum.OUT.getValue().equals(orgArchive.getStockStatus())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）目前是不在库状态，不允许重复出库");
      }
      if (ArchiveStockStatusEnum.OUT.getValue().equals(stockStatus)
          && ArchiveShelvingStatusEnum.IN.getValue().equals(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）目前是在架状态，不允许直接出库");
      }
      if (Objects.nonNull(orgArchive.getParentId())) {
        return BaseResult.failed("盒内或卷内（" + archiveName + "）不允许直接出入库");
      }
    }
    return null;
  }

  private BaseResult checkPreDeleteRestore(List<Long> idList, List<Archive> orgArchiveList) {
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (Long id : idList) {
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
    }
    return null;
  }

  private BaseResult checkPreDelete(
      List<Long> idList, List<Archive> orgArchiveList, Map<Long, Archive> orgSubArchiveMap) {
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (Long id : idList) {
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
      String archiveName = orgArchive.getArchiveName();
      if (ArchiveStockStatusEnum.IN.getValue().equals(orgArchive.getStockStatus())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）目前是在库状态，不允许直接删除");
      }
      if (ArchiveShelvingStatusEnum.IN.getValue().equals(orgArchive.getShelvingStatus())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）目前是在架状态，不允许直接删除");
      }
      if (orgSubArchiveMap.containsKey(id)) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）下存在子档案，不允许直接删除");
      }
      // todo: 是否存在待取、档案是否在取档申请中、档案是否在存取任务中
    }
    return null;
  }

  private BaseResult checkPreDeletePhysically(
      List<Long> idList, Long organizationId, List<Archive> orgArchiveList) {
    Map<Long, Archive> orgArchiveMap =
        orgArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    for (Long id : idList) {
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒不存在，id 为 " + id);
      }
      String archiveName = orgArchive.getArchiveName();
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）不属于当前机构");
      }
      if (ObjectUtil.equals(GlobalConstant.LOGIC_UNDELETE, orgArchive.getDeleteAt())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）不位于回收站，不允许物理删除");
      }
    }
    return null;
  }

  private BaseResult checkPreArchiveShelve(
      List<ArchiveShelveRequest.ArchiveRequest> archiveRequestList,
      Long organizationId,
      Map<Long, Archive> orgArchiveMap,
      Map<Integer, RackGroup> rackGroupMap,
      Map<Integer, HrCabinetGroup> hrCabinetGroupMap,
      Map<Integer, CabinetGroup> cabinetGroupMap) {
    // 请求参数自身判断
    Set<Long> requestArchiveIdSet = new HashSet<>();
    for (ArchiveShelveRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      Long id = archiveRequest.getId();
      String archiveName = archiveRequest.getArchiveName();
      if (requestArchiveIdSet.contains(id)) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）重复");
      } else {
        requestArchiveIdSet.add(id);
      }
    }
    // 档案信息判断
    for (ArchiveShelveRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      Long id = archiveRequest.getId();
      String archiveName = archiveRequest.getArchiveName();
      Archive orgArchive = orgArchiveMap.get(id);
      if (Objects.isNull(orgArchive)) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）不存在");
      }
      if (!Objects.equals(organizationId, orgArchive.getOrganizationId())) {
        return BaseResult.failed("档案或档案盒（" + archiveName + "）不属于当前机构");
      }
      if (Objects.nonNull(orgArchive.getParentId())) {
        return BaseResult.failed("盒内或卷内（" + archiveName + "）不允许单独上架");
      }
    }
    // 位置信息判断
    for (ArchiveShelveRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      String archiveName = archiveRequest.getArchiveName();
      Integer rackGroupNo = archiveRequest.getRackGroupNo();
      Integer rackColumnNo = archiveRequest.getRackColumnNo();
      Integer rackPanelNo = archiveRequest.getRackPanelNo();
      Integer rackSectionNo = archiveRequest.getRackSectionNo();
      Integer rackLayerNo = archiveRequest.getRackLayerNo();
      Integer rackGridNo = archiveRequest.getRackGridNo();
      Integer hrCabinetGroupNo = archiveRequest.getHrCabinetGroupNo();
      Integer hrCabinetNo = archiveRequest.getHrCabinetNo();
      Integer hrCabinetLayerNo = archiveRequest.getHrCabinetLayerNo();
      Integer hrCabinetGridNo = archiveRequest.getHrCabinetGridNo();
      Integer cabinetGroupNo = archiveRequest.getCabinetGroupNo();
      Integer cabinetNo = archiveRequest.getCabinetNo();
      Integer cabinetGridNo = archiveRequest.getCabinetGridNo();
      boolean hasRack =
          Stream.of(rackGroupNo, rackColumnNo, rackPanelNo, rackSectionNo, rackLayerNo, rackGridNo)
              .anyMatch(Objects::nonNull);
      boolean hasHrCabinet =
          Stream.of(hrCabinetGroupNo, hrCabinetNo, hrCabinetLayerNo, hrCabinetGridNo)
              .anyMatch(Objects::nonNull);
      boolean hasCabinet =
          Stream.of(cabinetGroupNo, cabinetNo, cabinetGridNo).anyMatch(Objects::nonNull);
      if ((hasRack && hasHrCabinet) || (hasHrCabinet && hasCabinet) || (hasRack && hasCabinet)) {
        return new SimpleResult<>("档案 " + archiveName + " 位置信息互斥");
      }
      if (hasRack) {
        boolean hasCompleteLocation =
            Stream.of(rackGroupNo, rackColumnNo, rackPanelNo, rackSectionNo, rackLayerNo)
                .allMatch(Objects::nonNull);
        if (!hasCompleteLocation) {
          return new SimpleResult<>("档案 " + archiveName + " 位置信息不完整");
        }
        RackGroup rackGroup = rackGroupMap.get(rackGroupNo);
        if (Objects.isNull(rackGroup)) {
          return new SimpleResult<>("档案 " + archiveName + " 所选密集架组不存在");
        }
      }
      if (hasHrCabinet) {
        boolean hasCompleteLocation =
            Stream.of(hrCabinetGroupNo, hrCabinetNo, hrCabinetLayerNo).allMatch(Objects::nonNull);
        if (!hasCompleteLocation) {
          return new SimpleResult<>("档案 " + archiveName + " 位置信息不完整");
        }
        HrCabinetGroup hrCabinetGroup = hrCabinetGroupMap.get(hrCabinetGroupNo);
        if (Objects.isNull(hrCabinetGroup)) {
          return new SimpleResult<>("档案 " + archiveName + " 所选人事柜组不存在");
        }
      }
      if (hasCabinet) {
        boolean hasCompleteLocation =
            Stream.of(cabinetGroupNo, cabinetNo, cabinetGridNo).allMatch(Objects::nonNull);
        if (!hasCompleteLocation) {
          return new SimpleResult<>("档案 " + archiveName + " 位置信息不完整");
        }
        CabinetGroup cabinetGroup = cabinetGroupMap.get(cabinetGroupNo);
        if (Objects.isNull(cabinetGroup)) {
          return new SimpleResult<>("档案 " + archiveName + " 所选档案柜组不存在");
        }
      }
    }
    return null;
  }

  private Pair<Archive, List<Archive>> buildArchiveAndSubArchiveListForUpdate(
      ArchiveUpdateRequest request,
      List<Archive> orgSubArchiveList,
      Long userId,
      boolean isUpdateLocation) {
    Archive archive = ArchiveConvert.getArchive(request);
    archive.setUpdatedId(userId);
    List<Archive> subArchiveList = new ArrayList<>();
    if (isUpdateLocation) {
      // 位置发生了改变
      for (Archive orgSubArchive : orgSubArchiveList) {
        Archive subArchive = new Archive();
        subArchive.setId(orgSubArchive.getId());
        subArchive.setWarehouseId(request.getWarehouseId());
        subArchive.setShelvingStatus(orgSubArchive.getShelvingStatus());
        subArchive.setRackGroupNo(request.getRackGroupNo());
        subArchive.setRackColumnNo(request.getRackColumnNo());
        subArchive.setRackPanelNo(request.getRackLayerNo());
        subArchive.setRackSectionNo(request.getRackSectionNo());
        subArchive.setRackLayerNo(request.getRackLayerNo());
        subArchive.setRackGridNo(request.getRackGridNo());
        subArchive.setHrCabinetGroupNo(request.getHrCabinetGroupNo());
        subArchive.setHrCabinetNo(request.getHrCabinetNo());
        subArchive.setHrCabinetLayerNo(request.getHrCabinetLayerNo());
        subArchive.setHrCabinetGridNo(request.getHrCabinetGridNo());
        subArchive.setCabinetGroupNo(request.getCabinetGroupNo());
        subArchive.setCabinetNo(request.getCabinetNo());
        subArchive.setCabinetGridNo(request.getCabinetGridNo());
        subArchive.setUpdatedId(userId);
        subArchiveList.add(subArchive);
      }
    }
    return Pair.of(archive, subArchiveList);
  }

  private List<Archive> buildArchiveListForArchiveShelve(
      List<ArchiveShelveRequest.ArchiveRequest> archiveRequestList,
      List<Archive> orgSubArchiveList,
      Map<Integer, RackGroup> rackGroupMap,
      Map<Integer, HrCabinetGroup> hrCabinetGroupMap,
      Map<Integer, CabinetGroup> cabinetGroupMap,
      Long userId) {
    Map<Long, List<Long>> orgSubArchiveIdListMap =
        orgSubArchiveList.stream()
            .collect(
                Collectors.groupingBy(
                    Archive::getParentId, Collectors.mapping(Archive::getId, Collectors.toList())));
    Date now = new Date();
    List<Archive> archiveList = new ArrayList<>();
    for (ArchiveShelveRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      List<Long> subArchiveIdList =
          orgSubArchiveIdListMap.getOrDefault(archiveRequest.getId(), Collections.emptyList());
      // 获取载具所在库房id
      Integer rackGroupNo = archiveRequest.getRackGroupNo();
      Integer hrCabinetGroupNo = archiveRequest.getHrCabinetGroupNo();
      Integer cabinetGroupNo = archiveRequest.getCabinetGroupNo();
      Long warehouseId = null;
      if (Objects.nonNull(rackGroupNo)) {
        RackGroup rackGroup = rackGroupMap.get(rackGroupNo);
        warehouseId = rackGroup.getWarehouseId();
      } else if (Objects.nonNull(hrCabinetGroupNo)) {
        HrCabinetGroup hrCabinetGroup = hrCabinetGroupMap.get(hrCabinetGroupNo);
        warehouseId = hrCabinetGroup.getWarehouseId();
      } else if (Objects.nonNull(cabinetGroupNo)) {
        CabinetGroup cabinetGroup = cabinetGroupMap.get(cabinetGroupNo);
        warehouseId = cabinetGroup.getWarehouseId();
      }
      Archive archive = new Archive();
      archive.setId(archiveRequest.getId());
      archive.setWarehouseId(warehouseId);
      archive.setStockStatus(ArchiveStockStatusEnum.IN.getValue());
      archive.setShelvingStatus(ArchiveShelvingStatusEnum.IN.getValue());
      archive.setRackGroupNo(rackGroupNo);
      archive.setRackColumnNo(archiveRequest.getRackColumnNo());
      archive.setRackPanelNo(archiveRequest.getRackPanelNo());
      archive.setRackSectionNo(archiveRequest.getRackSectionNo());
      archive.setRackLayerNo(archiveRequest.getRackLayerNo());
      archive.setRackGridNo(archiveRequest.getRackGridNo());
      archive.setHrCabinetGroupNo(archiveRequest.getHrCabinetGroupNo());
      archive.setHrCabinetNo(archiveRequest.getHrCabinetNo());
      archive.setHrCabinetLayerNo(archiveRequest.getHrCabinetLayerNo());
      archive.setHrCabinetGridNo(archiveRequest.getHrCabinetGridNo());
      archive.setCabinetGroupNo(archiveRequest.getCabinetGroupNo());
      archive.setCabinetNo(archiveRequest.getCabinetNo());
      archive.setCabinetGridNo(archiveRequest.getCabinetGridNo());
      archive.setShelvingId(userId);
      archive.setShelvingAt(now);
      archive.setUpdatedId(userId);
      archiveList.add(archive);
      for (Long subArchiveId : subArchiveIdList) {
        Archive subArchive = new Archive();
        subArchive.setId(subArchiveId);
        subArchive.setWarehouseId(warehouseId);
        subArchive.setStockStatus(ArchiveStockStatusEnum.IN.getValue());
        subArchive.setShelvingStatus(ArchiveShelvingStatusEnum.IN.getValue());
        subArchive.setRackGroupNo(rackGroupNo);
        subArchive.setRackColumnNo(archiveRequest.getRackColumnNo());
        subArchive.setRackPanelNo(archiveRequest.getRackPanelNo());
        subArchive.setRackSectionNo(archiveRequest.getRackSectionNo());
        subArchive.setRackLayerNo(archiveRequest.getRackLayerNo());
        subArchive.setRackGridNo(archiveRequest.getRackGridNo());
        subArchive.setHrCabinetGroupNo(archiveRequest.getHrCabinetGroupNo());
        subArchive.setHrCabinetNo(archiveRequest.getHrCabinetNo());
        subArchive.setHrCabinetLayerNo(archiveRequest.getHrCabinetLayerNo());
        subArchive.setHrCabinetGridNo(archiveRequest.getHrCabinetGridNo());
        subArchive.setCabinetGroupNo(archiveRequest.getCabinetGroupNo());
        subArchive.setCabinetNo(archiveRequest.getCabinetNo());
        subArchive.setCabinetGridNo(archiveRequest.getCabinetGridNo());
        subArchive.setShelvingId(userId);
        subArchive.setShelvingAt(now);
        subArchive.setUpdatedId(userId);
        archiveList.add(subArchive);
      }
    }
    return archiveList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForUpdate(
      Archive archive,
      Archive orgArchive,
      List<Archive> subArchiveList,
      List<Archive> orgSubArchiveList,
      Long userId,
      boolean isUpdateLocation) {
    Map<Long, Archive> orgSubArchiveMap =
        orgSubArchiveList.stream().collect(Collectors.toMap(Archive::getId, v -> v));
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
    operatorRecord.setOrganizationId(orgArchive.getOrganizationId());
    operatorRecord.setArchiveType(orgArchive.getType());
    operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UPDATE.getValue());
    operatorRecord.setOperatorId(userId);
    operatorRecordList.add(operatorRecord);
    if (isUpdateLocation) {
      for (Archive subArchive : subArchiveList) {
        Archive orgSubArchive = orgSubArchiveMap.get(subArchive.getId());
        ArchiveOperatorRecord subOperatorRecord =
            ArchiveConvert.getArchiveOperatorRecord(subArchive);
        subOperatorRecord.setOrganizationId(orgSubArchive.getOrganizationId());
        subOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UPDATE.getValue());
        subOperatorRecord.setArchiveTypeId(orgSubArchive.getArchiveTypeId());
        subOperatorRecord.setArchiveType(orgSubArchive.getType());
        subOperatorRecord.setArchiveName(orgSubArchive.getArchiveName());
        subOperatorRecord.setArchiveNo(orgSubArchive.getArchiveNo());
        subOperatorRecord.setOperatorId(userId);
        operatorRecordList.add(subOperatorRecord);
      }
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForUpdateStockStatus(
      List<Archive> archiveList,
      List<Archive> orgSubArchiveList,
      Long warehouseId,
      String stockStatus,
      Long userId) {
    String operatorType =
        ArchiveStockStatusEnum.IN.equalsValue(stockStatus)
            ? ArchiveOperatorTypeEnum.STOCK_IN.getValue()
            : ArchiveOperatorTypeEnum.STOCK_OUT.getValue();
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : archiveList) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
      operatorRecord.setWarehouseId(warehouseId);
      operatorRecord.setOperatorType(operatorType);
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    for (Archive subArchive : orgSubArchiveList) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(subArchive);
      operatorRecord.setWarehouseId(warehouseId);
      operatorRecord.setOperatorType(operatorType);
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForDelete(
      List<Archive> orgArchiveList, Long userId) {
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : orgArchiveList) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.DELETE.getValue());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForDeletePhysically(
      List<Archive> orgArchiveList, Long userId) {
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : orgArchiveList) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.DELETE_PHYSICALLY.getValue());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForDeleteRestore(
      List<Archive> orgArchiveList, Long userId) {
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : orgArchiveList) {
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.RESTORE.getValue());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForUpdateLocation(
      List<UpdateLocationRequest.ArchiveRequest> archiveRequestList,
      Map<Long, Archive> orgArchiveMap,
      Long userId) {
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (UpdateLocationRequest.ArchiveRequest archiveRequest : archiveRequestList) {
      Archive orgArchive = orgArchiveMap.get(archiveRequest.getId());
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(orgArchive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UPDATE.getValue());
      operatorRecord.setRackGroupNo(archiveRequest.getRackGroupNo());
      operatorRecord.setRackColumnNo(archiveRequest.getRackColumnNo());
      operatorRecord.setRackPanelNo(archiveRequest.getRackPanelNo());
      operatorRecord.setRackSectionNo(archiveRequest.getRackSectionNo());
      operatorRecord.setRackLayerNo(archiveRequest.getRackLayerNo());
      operatorRecord.setRackGridNo(archiveRequest.getRackGridNo());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForShelve(
      List<Archive> archiveList,
      Map<Long, Archive> orgArchiveMap,
      List<Archive> orgSubArchiveList,
      Long userId) {
    Map<Long, List<Archive>> orgSubArchiveMap =
        orgSubArchiveList.stream().collect(Collectors.groupingBy(Archive::getParentId));
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : archiveList) {
      Long id = archive.getId();
      Archive orgArchive = orgArchiveMap.get(id);
      List<Archive> pairOrgSubArchiveList =
          orgSubArchiveMap.getOrDefault(id, Collections.emptyList());
      // 生成上架操作记录
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
      operatorRecord.setOrganizationId(orgArchive.getOrganizationId());
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.SHELVE.getValue());
      operatorRecord.setArchiveTypeId(orgArchive.getArchiveTypeId());
      operatorRecord.setArchiveType(orgArchive.getType());
      operatorRecord.setArchiveName(orgArchive.getArchiveName());
      operatorRecord.setArchiveNo(orgArchive.getArchiveNo());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
      // 如果入库状态发生变更，这里需要生成入库操作记录
      if (ArchiveStockStatusEnum.OUT.equalsValue(orgArchive.getStockStatus())) {
        ArchiveOperatorRecord stockOperatorRecord =
            ArchiveConvert.getArchiveOperatorRecord(operatorRecord);
        stockOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.STOCK_IN.getValue());
        operatorRecordList.add(stockOperatorRecord);
      }
      for (Archive orgSubArchive : pairOrgSubArchiveList) {
        // 生成上架操作记录
        ArchiveOperatorRecord subOperatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
        subOperatorRecord.setOrganizationId(orgSubArchive.getOrganizationId());
        subOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.SHELVE.getValue());
        subOperatorRecord.setArchiveTypeId(orgSubArchive.getArchiveTypeId());
        subOperatorRecord.setArchiveId(orgSubArchive.getId());
        subOperatorRecord.setArchiveType(orgSubArchive.getType());
        subOperatorRecord.setArchiveName(orgSubArchive.getArchiveName());
        subOperatorRecord.setArchiveNo(orgSubArchive.getArchiveNo());
        subOperatorRecord.setOperatorId(userId);
        operatorRecordList.add(subOperatorRecord);
        // 如果入库状态发生变更，这里需要生成入库操作记录
        if (ArchiveStockStatusEnum.OUT.equalsValue(orgSubArchive.getStockStatus())) {
          ArchiveOperatorRecord stockOperatorRecord =
              ArchiveConvert.getArchiveOperatorRecord(subOperatorRecord);
          stockOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.STOCK_IN.getValue());
          operatorRecordList.add(stockOperatorRecord);
        }
      }
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForUnshelve(
      List<Archive> archiveList, List<Archive> subArchiveList, Long userId, String stockStatus) {
    Map<Long, List<Archive>> subArchiveMap =
        subArchiveList.stream().collect(Collectors.groupingBy(Archive::getParentId));
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : archiveList) {
      List<Archive> pairSubArchiveList =
          subArchiveMap.getOrDefault(archive.getId(), Collections.emptyList());
      // 生成下架操作记录
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(archive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
      // 如果出库状态发生变更，这里需要生成出库操作记录
      if (ArchiveStockStatusEnum.OUT.equalsValue(stockStatus)) {
        ArchiveOperatorRecord stockOperatorRecord =
            ArchiveConvert.getArchiveOperatorRecord(operatorRecord);
        stockOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.STOCK_OUT.getValue());
        operatorRecordList.add(stockOperatorRecord);
      }
      for (Archive subArchive : pairSubArchiveList) {
        // 生成下架操作记录
        ArchiveOperatorRecord subOperatorRecord =
            ArchiveConvert.getArchiveOperatorRecord(subArchive);
        subOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
        subOperatorRecord.setOperatorId(userId);
        operatorRecordList.add(subOperatorRecord);
        // 如果出库状态发生变更，这里需要生成出库操作记录
        if (ArchiveStockStatusEnum.OUT.equalsValue(stockStatus)) {
          ArchiveOperatorRecord stockOperatorRecord =
              ArchiveConvert.getArchiveOperatorRecord(subOperatorRecord);
          stockOperatorRecord.setOperatorType(ArchiveOperatorTypeEnum.STOCK_OUT.getValue());
          operatorRecordList.add(stockOperatorRecord);
        }
      }
    }
    return operatorRecordList;
  }

  private List<ArchiveOperatorRecord> buildArchiveOperatorRecordListForBoxing(
      List<Archive> archiveList, Map<Long, Archive> orgArchiveMap, Long userId) {
    List<ArchiveOperatorRecord> operatorRecordList = new ArrayList<>();
    for (Archive archive : archiveList) {
      Archive orgArchive = orgArchiveMap.get(archive.getId());
      ArchiveOperatorRecord operatorRecord = ArchiveConvert.getArchiveOperatorRecord(orgArchive);
      operatorRecord.setOperatorType(ArchiveOperatorTypeEnum.BOXING.getValue());
      operatorRecord.setRackGroupNo(archive.getRackGroupNo());
      operatorRecord.setRackColumnNo(archive.getRackColumnNo());
      operatorRecord.setRackPanelNo(archive.getRackPanelNo());
      operatorRecord.setRackSectionNo(archive.getRackSectionNo());
      operatorRecord.setRackLayerNo(archive.getRackLayerNo());
      operatorRecord.setRackGridNo(archive.getRackGridNo());
      operatorRecord.setHrCabinetGroupNo(archive.getHrCabinetGroupNo());
      operatorRecord.setHrCabinetNo(archive.getHrCabinetNo());
      operatorRecord.setHrCabinetLayerNo(archive.getHrCabinetLayerNo());
      operatorRecord.setHrCabinetGridNo(archive.getHrCabinetGridNo());
      operatorRecord.setCabinetGroupNo(archive.getCabinetGroupNo());
      operatorRecord.setCabinetNo(archive.getCabinetNo());
      operatorRecord.setCabinetGridNo(archive.getCabinetGridNo());
      operatorRecord.setOperatorId(userId);
      operatorRecordList.add(operatorRecord);
    }
    return operatorRecordList;
  }

  private List<ArchiveListVO> getArchiveListVOList(List<Archive> archiveList) {
    List<ArchiveListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(archiveList)) {
      return voList;
    }
    // 实体转换
    for (Archive archive : archiveList) {
      ArchiveListVO vo = ArchiveConvert.getArchiveListVO(archive);
      vo.setBoxStatus(ArchiveUtil.getArchiveBoxStatus(archive));
      vo.setStorageType(ArchiveUtil.getArchiveStorageType(archive));
      vo.setLocation(LocationUtil.location(archive));
      voList.add(vo);
    }
    // 档案门类信息完善
    List<Long> archiveTypeIdList =
        archiveList.stream().map(Archive::getArchiveTypeId).distinct().collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveListVO vo : voList) {
      ArchiveType archiveType = archiveTypeMap.get(vo.getArchiveTypeId());
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    // 档案字段信息完善
    List<Long> archiveIdList =
        archiveList.stream().map(Archive::getId).collect(Collectors.toList());
    List<ArchiveFieldValue> archiveFieldValueList =
        archiveFieldValueMapper.selectListByArchiveIdList(archiveIdList);
    Map<Long, List<ArchiveFieldValue>> archiveFieldValueMap =
        archiveFieldValueList.stream()
            .collect(Collectors.groupingBy(ArchiveFieldValue::getArchiveId));
    for (ArchiveListVO vo : voList) {
      List<ArchiveFieldValue> fieldValueList = archiveFieldValueMap.get(vo.getId());
      vo.setFieldValueList(ArchiveConvert.getArchiveFieldValueList(fieldValueList));
    }
    // 卷内档案信息完善
    List<ArchiveSubCountExt> subArchiveCountList =
        archiveMapper.selectSubArchiveCountList(archiveIdList);
    Map<Long, Integer> subArchiveCountMap =
        subArchiveCountList.stream()
            .collect(
                Collectors.toMap(
                    ArchiveSubCountExt::getParentId, ArchiveSubCountExt::getSubArchiveCount));
    for (ArchiveListVO vo : voList) {
      vo.setSubArchiveCount(subArchiveCountMap.getOrDefault(vo.getId(), 0));
    }
    return voList;
  }

  private List<ArchivePendingOutListVO> getArchivePendingOutListVOList(List<Archive> archiveList) {
    List<ArchivePendingOutListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(archiveList)) {
      return voList;
    }
    // 实体转换
    for (Archive archive : archiveList) {
      ArchivePendingOutListVO vo = ArchiveConvert.getArchivePendingOutListVO(archive);
      vo.setBoxStatus(ArchiveUtil.getArchiveBoxStatus(archive));
      vo.setStorageType(ArchiveUtil.getArchiveStorageType(archive));
      vo.setLocation(LocationUtil.location(archive));
      voList.add(vo);
    }
    // 档案门类信息完善
    List<Long> archiveTypeIdList =
        archiveList.stream().map(Archive::getArchiveTypeId).distinct().collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveListVO vo : voList) {
      ArchiveType archiveType = archiveTypeMap.get(vo.getArchiveTypeId());
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    // 档案字段信息完善
    List<Long> archiveIdList =
        archiveList.stream().map(Archive::getId).collect(Collectors.toList());
    List<ArchiveFieldValue> archiveFieldValueList =
        archiveFieldValueMapper.selectListByArchiveIdList(archiveIdList);
    Map<Long, List<ArchiveFieldValue>> archiveFieldValueMap =
        archiveFieldValueList.stream()
            .collect(Collectors.groupingBy(ArchiveFieldValue::getArchiveId));
    for (ArchiveListVO vo : voList) {
      List<ArchiveFieldValue> fieldValueList = archiveFieldValueMap.get(vo.getId());
      vo.setFieldValueList(ArchiveConvert.getArchiveFieldValueList(fieldValueList));
    }
    // 卷内档案信息完善
    List<ArchiveSubCountExt> subArchiveCountList =
        archiveMapper.selectSubArchiveCountList(archiveIdList);
    Map<Long, Integer> subArchiveCountMap =
        subArchiveCountList.stream()
            .collect(
                Collectors.toMap(
                    ArchiveSubCountExt::getParentId, ArchiveSubCountExt::getSubArchiveCount));
    for (ArchiveListVO vo : voList) {
      vo.setSubArchiveCount(subArchiveCountMap.getOrDefault(vo.getId(), 0));
    }
    return voList;
  }

  private List<ArchiveDeletedListVO> getArchiveDeletedListVOList(List<Archive> archiveList) {
    List<ArchiveDeletedListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(archiveList)) {
      return voList;
    }
    // 实体转换
    for (Archive archive : archiveList) {
      ArchiveDeletedListVO vo = new ArchiveDeletedListVO();
      vo.setId(archive.getId());
      vo.setArchiveTypeId(archive.getArchiveTypeId());
      vo.setType(archive.getType());
      vo.setArchiveName(archive.getArchiveName());
      vo.setArchiveNo(archive.getArchiveNo());
      vo.setDeleteTime(new Date(archive.getDeleteAt()));
      voList.add(vo);
    }
    // 档案门类信息完善
    List<Long> archiveTypeIdList =
        archiveList.stream().map(Archive::getArchiveTypeId).distinct().collect(Collectors.toList());
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveDeletedListVO vo : voList) {
      ArchiveType archiveType = archiveTypeMap.get(vo.getArchiveTypeId());
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    return voList;
  }
}
