package org.zxwl.smart.service.archive.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.common.convert.archive.ArchiveConvert;
import org.zxwl.smart.common.utils.ColUtil;
import org.zxwl.smart.constant.enums.archive.ArchiveOperatorTypeEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveStockStatusEnum;
import org.zxwl.smart.constant.enums.stats.StatsSummaryTypeEnum;
import org.zxwl.smart.domain.request.archive.*;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.*;
import org.zxwl.smart.mybatis.entity.archive.ArchiveOperatorRecord;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;
import org.zxwl.smart.mybatis.ext.archive.ArchiveShelvingStatusStat;
import org.zxwl.smart.mybatis.ext.archive.ArchiveStockStatusStat;
import org.zxwl.smart.mybatis.ext.archive.ArchiveTypeStat;
import org.zxwl.smart.mybatis.ext.archive.OperatorStat;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveOperatorRecordMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper;
import org.zxwl.smart.service.archive.ArchiveStatsService;

@Service
@AllArgsConstructor
public class ArchiveStatsServiceImpl implements ArchiveStatsService {

  private final ArchiveMapper archiveMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final ArchiveOperatorRecordMapper archiveOperatorRecordMapper;

  @Override
  public SimpleResult<ArchiveStockStatVO> getArchiveStockStat(ArchiveStockStatRequest request) {
    Long warehouseId = request.getWarehouseId();
    // 查询在库和不在库数量
    List<ArchiveStockStatusStat> archiveStockStatList =
        archiveMapper.selectStockStatusStat(warehouseId, null, null, null);
    Map<String, ArchiveStockStatusStat> archiveStockStatMap =
        archiveStockStatList.stream()
            .collect(Collectors.toMap(ArchiveStockStatusStat::getStockStatus, v -> v));
    ArchiveStockStatusStat archiveInStock =
        archiveStockStatMap.get(ArchiveStockStatusEnum.IN.getValue());
    ArchiveStockStatusStat archiveOutStock =
        archiveStockStatMap.get(ArchiveStockStatusEnum.OUT.getValue());
    int inStockCount = Objects.nonNull(archiveInStock) ? archiveInStock.getCount() : 0;
    int outStockCount = Objects.nonNull(archiveOutStock) ? archiveOutStock.getCount() : 0;
    // vo 构建
    ArchiveStockStatVO vo = new ArchiveStockStatVO();
    vo.setInStockCount(inStockCount);
    vo.setOutStockCount(outStockCount);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<ArchiveShelvingStatVO> getArchiveShelvingStat(
      ArchiveShelvingStatRequest request) {
    Long warehouseId = request.getWarehouseId();
    // 查询在架和不在架数量
    List<ArchiveShelvingStatusStat> archiveStatList =
        archiveMapper.selectShelvingStatusStat(warehouseId, null, null, null);
    Map<String, ArchiveShelvingStatusStat> archiveStatMap =
        archiveStatList.stream()
            .collect(Collectors.toMap(ArchiveShelvingStatusStat::getShelvingStatus, v -> v));
    ArchiveShelvingStatusStat archivePendingInShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.PENDING_IN.getValue());
    ArchiveShelvingStatusStat archiveInShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.IN.getValue());
    ArchiveShelvingStatusStat archiveOutShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.OUT.getValue());
    ArchiveShelvingStatusStat archivePendingOutShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    int pendingInCount =
        Objects.nonNull(archivePendingInShelving) ? archivePendingInShelving.getCount() : 0;
    int inCount = Objects.nonNull(archiveInShelving) ? archiveInShelving.getCount() : 0;
    int outCount = Objects.nonNull(archiveOutShelving) ? archiveOutShelving.getCount() : 0;
    int pendingOutCount =
        Objects.nonNull(archivePendingOutShelving) ? archivePendingOutShelving.getCount() : 0;
    // vo 构建
    ArchiveShelvingStatVO vo = new ArchiveShelvingStatVO();
    vo.setInShelvingCount(inCount + pendingOutCount);
    vo.setOutShelvingCount(outCount + pendingInCount);
    return new SimpleResult<>(vo);
  }

  @Override
  public ListResult<ArchiveTypeStatVO> getArchiveTypeStat(ArchiveTypeStatRequest request) {
    Long warehouseId = request.getWarehouseId();
    // 查询分类数量
    List<ArchiveTypeStat> archiveTypeStatList =
        archiveMapper.selectArchiveTypeStat(warehouseId, null, null, null);
    List<ArchiveTypeStatVO> voList = ArchiveConvert.getArchiveTypeStatVOList(archiveTypeStatList);
    // 分类名字补充
    List<Long> archiveTypeIdList =
        voList.stream().map(ArchiveTypeStatVO::getArchiveTypeId).toList();
    List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
    Map<Long, ArchiveType> archiveTypeMap =
        archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    for (ArchiveTypeStatVO vo : voList) {
      ArchiveType archiveType = archiveTypeMap.get(vo.getArchiveTypeId());
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<ArchiveAccessStatVO> getArchiveAccessStat(ArchiveAccessStatRequest request) {
    Long warehouseId = request.getWarehouseId();
    // 查询档案总数量
    int totalCount = archiveMapper.countByWarehouseId(warehouseId);
    // 查询累计上/下架
    List<OperatorStat> operatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(
            warehouseId, null, null, null, null, null);
    Map<String, OperatorStat> operatorStatMap =
        operatorStatList.stream().collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat operatorShelve = operatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat operatorUnshelve =
        operatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    // 查询今日上/下架
    List<OperatorStat> todayOperatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(
            warehouseId, null, null, null, DateUtil.beginOfDay(new Date()), null);
    Map<String, OperatorStat> todayOperatorStatMap =
        todayOperatorStatList.stream()
            .collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat todayOperatorShelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat todayOperatorUnshelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    // vo 转换
    ArchiveAccessStatVO vo = new ArchiveAccessStatVO();
    vo.setTotalCount(totalCount);
    vo.setTotalShelveCount(Objects.nonNull(operatorShelve) ? operatorShelve.getCount() : 0);
    vo.setTotalUnshelveCount(Objects.nonNull(operatorUnshelve) ? operatorUnshelve.getCount() : 0);
    vo.setTodayShelveCount(
        Objects.nonNull(todayOperatorShelve) ? todayOperatorShelve.getCount() : 0);
    vo.setTodayUnshelveCount(
        Objects.nonNull(todayOperatorUnshelve) ? todayOperatorUnshelve.getCount() : 0);
    return new SimpleResult<>(vo);
  }

  @Override
  public ListResult<ArchiveStockStatListVO> getArchiveStockStatList(
      ArchiveStockStatListRequest request) {
    Long warehouseId = request.getWarehouseId();
    String summaryType = request.getSummaryType();
    List<Date> createAtRange = request.getCreatedAtRange();
    Date beginCreatedAt = null;
    Date endCreatedAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreatedAt = createAtRange.get(0);
      endCreatedAt = createAtRange.get(1);
    }
    List<String> operatorTypeList =
        ListUtil.toList(
            ArchiveOperatorTypeEnum.STOCK_IN.getValue(),
            ArchiveOperatorTypeEnum.STOCK_OUT.getValue());
    List<ArchiveOperatorRecord> recordList =
        archiveOperatorRecordMapper.selectListForStat(
            warehouseId, operatorTypeList, beginCreatedAt, endCreatedAt);
    // vo 转换
    List<ArchiveStockStatListVO> voList = new ArrayList<>();
    for (ArchiveOperatorRecord record : recordList) {
      String operatorType = record.getOperatorType();
      ArchiveStockStatListVO vo = new ArchiveStockStatListVO();
      vo.setInStockCount(ArchiveOperatorTypeEnum.STOCK_IN.equalsValue(operatorType) ? 1 : 0);
      vo.setOutStockCount(ArchiveOperatorTypeEnum.STOCK_OUT.equalsValue(operatorType) ? 1 : 0);
      vo.setCreatedAt(new DateTime(record.getCreatedAt()));
      voList.add(vo);
    }
    // vo 合并
    voList =
        ColUtil.mergeList(
            voList,
            r -> {
              DateTime createdAt = r.getCreatedAt();
              if (StatsSummaryTypeEnum.MONTH.equalsValue(summaryType)) {
                return createdAt.year() + "-" + createdAt.month();
              } else if (StatsSummaryTypeEnum.DAY.equalsValue(summaryType)) {
                return createdAt.year() + "-" + createdAt.month() + "-" + createdAt.dayOfMonth();
              } else {
                return createdAt.year()
                    + "-"
                    + createdAt.month()
                    + "-"
                    + createdAt.dayOfMonth()
                    + "_"
                    + createdAt.hour(true);
              }
            },
            vos -> {
              ArchiveStockStatListVO vo = vos.get(0);
              vo.setInStockCount(
                  vos.stream().mapToInt(ArchiveStockStatListVO::getInStockCount).sum());
              vo.setOutStockCount(
                  vos.stream().mapToInt(ArchiveStockStatListVO::getOutStockCount).sum());
              DateTime createdAt = vo.getCreatedAt();
              if (StatsSummaryTypeEnum.MONTH.getValue().equals(summaryType)) {
                createdAt.setField(DateField.DAY_OF_MONTH, 1);
                createdAt.setField(DateField.HOUR_OF_DAY, 0);
                createdAt.setField(DateField.MINUTE, 0);
                createdAt.setField(DateField.SECOND, 0);
              } else if (StatsSummaryTypeEnum.DAY.getValue().equals(summaryType)) {
                createdAt.setField(DateField.HOUR_OF_DAY, 0);
                createdAt.setField(DateField.MINUTE, 0);
                createdAt.setField(DateField.SECOND, 0);
              } else {
                createdAt.setField(DateField.MINUTE, 0);
                createdAt.setField(DateField.SECOND, 0);
              }
              return vo;
            });
    voList.sort(Comparator.comparing(ArchiveStockStatListVO::getCreatedAt));
    return new ListResult<>(voList);
  }
}
