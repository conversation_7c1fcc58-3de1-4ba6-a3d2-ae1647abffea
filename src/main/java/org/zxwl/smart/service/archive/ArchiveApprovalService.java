package org.zxwl.smart.service.archive;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.ApprovedListVO;
import org.zxwl.smart.domain.response.pickup.ApprovedVO;
import org.zxwl.smart.domain.response.pickup.PendingApprovalListVO;
import org.zxwl.smart.domain.response.pickup.PendingApprovalVO;

@Validated
public interface ArchiveApprovalService {

  PageResult<PendingApprovalListVO> getArchivePendingApprovalList(
      @NotNull @Valid PendingApprovalListRequest request);

  SimpleResult<PendingApprovalVO> getArchivePendingApproval(
      @NotNull @Valid PendingApprovalRequest request);

  SimpleResult<BaseCreateListVO> approveArchive(@NotNull @Valid ApprovalRequest request);

  PageResult<ApprovedListVO> getArchiveApprovedList(@NotNull @Valid ApprovedListRequest request);

  SimpleResult<ApprovedVO> getArchiveApproved(@NotNull @Valid ApprovedRequest request);
}
