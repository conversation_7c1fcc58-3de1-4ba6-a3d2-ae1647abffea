package org.zxwl.smart.service.inventory;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.inventory.InventoryRecordCreateRequest;
import org.zxwl.smart.domain.request.inventory.InventoryRecordListRequest;
import org.zxwl.smart.domain.request.inventory.InventoryRecordRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.inventory.InventoryRecordListVO;
import org.zxwl.smart.domain.response.inventory.InventoryRecordVO;

@Validated
public interface InventoryService {

  PageResult<InventoryRecordListVO> getInventoryRecordList(
      @NotNull @Valid InventoryRecordListRequest request);

  SimpleResult<InventoryRecordVO> getInventoryRecord(
      @NotNull @Valid InventoryRecordRequest request);

  SimpleResult<BaseCreateVO> createInventoryRecord(
      @NotNull @Valid InventoryRecordCreateRequest request);
}
