package org.zxwl.smart.service.inventory;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.inventory.RobotRecordListRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.inventory.RobotRecordVO;

@Validated
public interface RobotInventoryRecordService {

  PageResult<RobotRecordVO> getRobotRecordList(@NotNull @Valid RobotRecordListRequest request);
}
