package org.zxwl.smart.service.inventory.impl;

import org.springframework.stereotype.Service;
import org.zxwl.smart.domain.request.inventory.RobotRecordListRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.inventory.RobotRecordVO;
import org.zxwl.smart.service.inventory.RobotInventoryRecordService;

@Service
public class RobotInventoryRecordServiceImpl implements RobotInventoryRecordService {

  @Override
  public PageResult<RobotRecordVO> getRobotRecordList(RobotRecordListRequest request) {
    return null;
  }
}
