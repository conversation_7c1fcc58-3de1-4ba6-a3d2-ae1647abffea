package org.zxwl.smart.service.inventory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.Inventory.InventoryConvert;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.constant.consist.AuthorizeConstant;
import org.zxwl.smart.constant.enums.inventory.InventoryDetailStatus;
import org.zxwl.smart.constant.enums.inventory.InventoryRecordTypeEnum;
import org.zxwl.smart.domain.request.inventory.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.inventory.InventoryRecordListVO;
import org.zxwl.smart.domain.response.inventory.InventoryRecordVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGroup;
import org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup;
import org.zxwl.smart.mybatis.entity.inventory.InventoryDetail;
import org.zxwl.smart.mybatis.entity.inventory.InventoryRecord;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.inventory.InventoryDetailMapper;
import org.zxwl.smart.mybatis.mapper.inventory.InventoryRecordMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.inventory.InventoryService;

@Service
@AllArgsConstructor
public class InventoryServiceImpl implements InventoryService {

  private final InventoryRecordMapper inventoryRecordMapper;
  private final InventoryDetailMapper inventoryDetailMapper;
  private final ArchiveMapper archiveMapper;
  private final RackGroupMapper rackGroupMapper;
  private final HrCabinetGroupMapper hrCabinetGroupMapper;
  private final CabinetGroupMapper cabinetGroupMapper;
  private final WarehouseMapper warehouseMapper;
  private final TransactionTemplate transactionTemplate;

  @Override
  public PageResult<InventoryRecordListVO> getInventoryRecordList(
      InventoryRecordListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Integer rackGroupNo = request.getRackGroupNo();
    Integer hrCabinetGroupNo = request.getHrCabinetGroupNo();
    Integer cabinetGroupNo = request.getCabinetGroupNo();
    List<Date> createAtRange = request.getCreateAtRange();
    Date beginCreateAt = null;
    Date endCreateAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreateAt = createAtRange.get(0);
      endCreateAt = createAtRange.get(1);
    }
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<InventoryRecord> recordList =
        inventoryRecordMapper.selectList(
            organizationId,
            rackGroupNo,
            hrCabinetGroupNo,
            cabinetGroupNo,
            beginCreateAt,
            endCreateAt);
    List<InventoryRecordListVO> voList = getInventoryRecordVOList(recordList);
    return new PageResult<>(voList, recordList);
  }

  @Override
  public SimpleResult<InventoryRecordVO> getInventoryRecord(InventoryRecordRequest request) {
    Long id = request.getId();
    InventoryRecord inventoryRecord = inventoryRecordMapper.selectById(id);
    if (Objects.isNull(inventoryRecord)) {
      return new SimpleResult<>("盘点记录不存在");
    }
    List<InventoryDetail> inventoryDetailList = inventoryDetailMapper.selectList(id);
    List<InventoryRecordVO.DetailVO> detailVOList = new ArrayList<>();
    for (InventoryDetail inventoryDetail : inventoryDetailList) {
      InventoryRecordVO.DetailVO vo = InventoryConvert.getInventoryDetailVO(inventoryDetail);
      vo.setLocation(LocationUtil.location(inventoryDetail));
      detailVOList.add(vo);
    }
    InventoryRecordVO vo = InventoryConvert.getInventoryRecordVO(inventoryRecord);
    vo.setLocation(LocationUtil.location(inventoryRecord));
    vo.setDetailList(detailVOList);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<BaseCreateVO> createInventoryRecord(InventoryRecordCreateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    if (Objects.isNull(organizationId)) {
      organizationId = request.getOrganizationId();
    }
    Long userId = AccountLocal.getUserId();
    if (Objects.isNull(userId)) {
      userId = request.getUserId();
    }
    String terminal = AccountLocal.getTerminal();
    Long inventoryTaskId = request.getInventoryTaskId();
    Integer rackGroupNo = request.getRackGroupNo();
    Integer hrCabinetGroupNo = request.getHrCabinetGroupNo();
    Integer cabinetGroupNo = request.getCabinetGroupNo();
    List<InventoryRecordCreateRequest.DetailRequest> detailRequestList = request.getDetailList();
    List<String> archiveTidList =
        detailRequestList.stream()
            .filter(d -> !InventoryDetailStatus.UNBIND.equalsValue(d.getStatus()))
            .map(InventoryRecordCreateRequest.DetailRequest::getTid)
            .collect(Collectors.toList());
    Map<String, Archive> archiveMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(archiveTidList)) {
      List<Archive> archiveList = archiveMapper.selectListByTidList(archiveTidList);
      archiveMap = archiveList.stream().collect(Collectors.toMap(Archive::getTid, v -> v));
    }
    for (InventoryRecordCreateRequest.DetailRequest detailRequest : detailRequestList) {
      String tid = detailRequest.getTid();
      Archive archive = archiveMap.get(tid);
      if (!InventoryDetailStatus.UNBIND.equalsValue(detailRequest.getStatus())
          && Objects.isNull(archive)) {
        return new SimpleResult<>("档案不存在，tid 为" + tid);
      }
    }
    if (Objects.isNull(organizationId)) {
      return new SimpleResult<>("盘点机构不能为空");
    }
    if (Objects.isNull(userId)) {
      return new SimpleResult<>("盘点人不能为空");
    }
    RackGroup rackGroup = null;
    HrCabinetGroup hrCabinetGroup = null;
    CabinetGroup cabinetGroup = null;
    if (Objects.nonNull(rackGroupNo)) {
      rackGroup = rackGroupMapper.selectByGroupNo(rackGroupNo);
      if (Objects.isNull(rackGroup)) {
        return new SimpleResult<>("密集架组不存在，组号为" + rackGroupNo);
      }
      if (!Objects.equals(organizationId, rackGroup.getOrganizationId())) {
        return new SimpleResult<>("该密集架组不属于当前机构");
      }
    } else if (Objects.nonNull(hrCabinetGroupNo)) {
      hrCabinetGroup = hrCabinetGroupMapper.selectByGroupNo(hrCabinetGroupNo);
      if (Objects.isNull(hrCabinetGroup)) {
        return new SimpleResult<>("人事柜组不存在，组号为" + cabinetGroupNo);
      }
      if (!Objects.equals(organizationId, hrCabinetGroup.getOrganizationId())) {
        return new SimpleResult<>("该人事柜组不属于当前机构");
      }
    } else if (Objects.nonNull(cabinetGroupNo)) {
      cabinetGroup = cabinetGroupMapper.selectByGroupNo(cabinetGroupNo);
      if (Objects.isNull(cabinetGroup)) {
        return new SimpleResult<>("柜组不存在，组号为" + cabinetGroupNo);
      }
      if (!Objects.equals(organizationId, cabinetGroup.getOrganizationId())) {
        return new SimpleResult<>("该柜组不属于当前机构");
      }
    } else {
      return new SimpleResult<>("载具编号不能同时为空");
    }
    String inventoryType = getInventoryType(terminal);
    if (Objects.isNull(inventoryType)) {
      return new SimpleResult<>("暂不支持该终端进行盘点，请联系服务端进行终端添加");
    }
    Long warehouseId;
    if (Objects.nonNull(rackGroup)) {
      warehouseId = rackGroup.getWarehouseId();
    } else if (Objects.nonNull(hrCabinetGroup)) {
      warehouseId = hrCabinetGroup.getWarehouseId();
    } else {
      warehouseId = cabinetGroup.getWarehouseId();
    }
    int onShelfCount = 0;
    int offShelfCount = 0;
    int misplacedCount = 0;
    int borrowCount = 0;
    int unbindCount = 0;
    List<InventoryDetail> detailList = new ArrayList<>();
    for (InventoryRecordCreateRequest.DetailRequest detailRequest : detailRequestList) {
      String tid = detailRequest.getTid();
      String status = detailRequest.getStatus();
      Archive archive = archiveMap.get(tid);
      InventoryDetail inventoryDetail = InventoryConvert.getInventoryDetail(detailRequest);
      if (Objects.nonNull(archive)) {
        inventoryDetail.setArchiveId(archive.getId());
        inventoryDetail.setArchiveNo(archive.getArchiveNo());
        inventoryDetail.setArchiveName(archive.getArchiveName());
      }
      detailList.add(inventoryDetail);
      if (InventoryDetailStatus.ON_SHELF.equalsValue(status)) {
        onShelfCount++;
      }
      if (InventoryDetailStatus.OFF_SHELF.equalsValue(status)) {
        offShelfCount++;
      }
      if (InventoryDetailStatus.MISPLACED.equalsValue(status)) {
        misplacedCount++;
      }
      if (InventoryDetailStatus.BORROW.equalsValue(status)) {
        borrowCount++;
      }
      if (InventoryDetailStatus.UNBIND.equalsValue(status)) {
        unbindCount++;
      }
    }
    InventoryRecord inventoryRecord = InventoryConvert.getInventoryRecord(request);
    inventoryRecord.setOrganizationId(organizationId);
    inventoryRecord.setDeviceId(null);
    inventoryRecord.setWarehouseId(warehouseId);
    inventoryRecord.setInventorType(inventoryType);
    inventoryRecord.setOnShelfCount(onShelfCount);
    inventoryRecord.setOffShelfCount(offShelfCount);
    inventoryRecord.setMisplacedCount(misplacedCount);
    inventoryRecord.setBorrowCount(borrowCount);
    inventoryRecord.setUnbindCount(unbindCount);
    inventoryRecord.setCreatedId(userId);
    transactionTemplate.execute(
        status -> {
          inventoryRecordMapper.insert(inventoryRecord);
          detailList.forEach(detail -> detail.setInventoryRecordId(inventoryRecord.getId()));
          inventoryDetailMapper.insertList(detailList);
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(inventoryRecord.getId()));
  }

  private List<InventoryRecordListVO> getInventoryRecordVOList(List<InventoryRecord> recordList) {
    List<InventoryRecordListVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(recordList)) {
      return voList;
    }
    List<Long> warehouseIdList =
        recordList.stream()
            .map(InventoryRecord::getWarehouseId)
            .distinct()
            .collect(Collectors.toList());
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    for (InventoryRecord record : recordList) {
      Warehouse warehouse = warehouseMap.get(record.getWarehouseId());
      InventoryRecordListVO vo = InventoryConvert.getInventoryRecordListVO(record);
      vo.setWarehouseName(warehouse.getWarehouseName());
      vo.setLocation(LocationUtil.location(record));
      voList.add(vo);
    }
    return voList;
  }

  private String getInventoryType(String terminal) {
    if (StrUtil.equals(AuthorizeConstant.TERMINAL_HANDHELD, terminal)) {
      return InventoryRecordTypeEnum.HANDHELD.getValue();
    }
    if (StrUtil.equals(AuthorizeConstant.TERMINAL_INVENTORY_CAR, terminal)) {
      return InventoryRecordTypeEnum.INVENTORY_CAR.getValue();
    }
    if (StrUtil.equals(AuthorizeConstant.TERMINAL_ARCHIVE_CABINET, terminal)
        || StrUtil.equals(AuthorizeConstant.TERMINAL_HR_CABINET, terminal)) {
      return InventoryRecordTypeEnum.CABINET.getValue();
    }
    if (StrUtil.equals(AuthorizeConstant.TERMINAL_SMART_RACK, terminal)
        || StrUtil.equals(AuthorizeConstant.TERMINAL_LIGHT_RACK, terminal)
        || StrUtil.equals(AuthorizeConstant.TERMINAL_BOX_RACK, terminal)) {
      return InventoryRecordTypeEnum.RACK.getValue();
    }
    return null;
  }
}
