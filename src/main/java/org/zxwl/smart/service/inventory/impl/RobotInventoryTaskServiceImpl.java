package org.zxwl.smart.service.inventory.impl;

import org.springframework.stereotype.Service;
import org.zxwl.smart.domain.request.inventory.RobotTaskCreateRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskDeleteRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskListRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.inventory.RobotTaskVO;
import org.zxwl.smart.service.inventory.RobotInventoryTaskService;

@Service
public class RobotInventoryTaskServiceImpl implements RobotInventoryTaskService {

  @Override
  public PageResult<RobotTaskVO> getRobotTaskList(RobotTaskListRequest request) {
    return new PageResult<>(null, null);
  }

  @Override
  public SimpleResult<BaseCreateVO> createRobotTask(RobotTaskCreateRequest request) {
    return new SimpleResult<>(new BaseCreateVO());
  }

  @Override
  public BaseResult deleteRobotTask(RobotTaskDeleteRequest request) {
    return BaseResult.success();
  }
}
