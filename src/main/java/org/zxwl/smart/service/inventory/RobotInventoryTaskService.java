package org.zxwl.smart.service.inventory;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.inventory.RobotTaskCreateRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskDeleteRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskListRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.inventory.RobotTaskVO;

@Validated
public interface RobotInventoryTaskService {

  PageResult<RobotTaskVO> getRobotTaskList(@NotNull @Valid RobotTaskListRequest request);

  SimpleResult<BaseCreateVO> createRobotTask(@NotNull @Valid RobotTaskCreateRequest request);

  BaseResult deleteRobotTask(@NotNull @Valid RobotTaskDeleteRequest request);
}
