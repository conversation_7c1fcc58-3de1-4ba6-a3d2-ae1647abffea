package org.zxwl.smart.service.permission;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.permission.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.permission.PermissionVO;

@Validated
public interface PermissionService {

  /** 获取权限列表 */
  ListResult<PermissionVO> getPermissionList(@NotNull @Valid PermissionListRequest request);

  /** 获取用户权限信息 */
  ListResult<PermissionVO> getUserPermission(@NotNull @Valid UserPermissionRequest request);

  /** 创建权限 */
  BaseResult createPermission(@NotNull @Valid PermissionCreateRequest request);
}
