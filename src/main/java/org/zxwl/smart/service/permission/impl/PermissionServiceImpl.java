package org.zxwl.smart.service.permission.impl;

import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.permission.PermissionConvert;
import org.zxwl.smart.domain.request.permission.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.permission.PermissionVO;
import org.zxwl.smart.mybatis.entity.sys.Permission;
import org.zxwl.smart.mybatis.mapper.sys.PermissionMapper;
import org.zxwl.smart.service.permission.PermissionService;

@Service
@AllArgsConstructor
public class PermissionServiceImpl implements PermissionService {

  private final PermissionMapper permissionMapper;

  @Override
  public ListResult<PermissionVO> getPermissionList(PermissionListRequest request) {
    List<Permission> permissionList = permissionMapper.selectList();
    if (Objects.isNull(permissionList)) {
      return new ListResult<>("权限列表为空");
    }
    List<PermissionVO> permissionVOList =
        permissionList.stream()
            .map(PermissionConvert::getPermissionVO)
            .collect(Collectors.toList());
    List<PermissionVO> treeList = buildPermissionTree(permissionVOList);
    return new ListResult<>(treeList);
  }

  @Override
  public ListResult<PermissionVO> getUserPermission(UserPermissionRequest request) {
    Long userId = request.getUserId();
    Long organizationId = request.getOrganizationId();
    List<Permission> permissionList = permissionMapper.selectPermissionList(userId, organizationId);
    if (Objects.isNull(permissionList)) {
      return new ListResult<>("用户权限为空");
    }
    List<PermissionVO> permissionVOList =
        permissionList.stream()
            .map(PermissionConvert::getPermissionVO)
            .collect(Collectors.toList());
    List<PermissionVO> treeList = buildPermissionTree(permissionVOList);
    return new ListResult<>(treeList);
  }

  @Override
  public BaseResult createPermission(PermissionCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    String permissionCode = request.getPermissionCode();
    if (permissionMapper.selectByPermissionCode(permissionCode) > 0) {
      return BaseResult.failed("权限编码已存在");
    }
    Permission permission = PermissionConvert.getPermission(request);
    permission.setCreatedId(userId);
    permission.setUpdatedId(userId);
    permissionMapper.insert(permission);
    return BaseResult.success();
  }

  /** 构建权限树 */
  private List<PermissionVO> buildPermissionTree(List<PermissionVO> permissionVOList) {
    if (Objects.isNull(permissionVOList)) {
      return new ArrayList<>();
    }
    // 按照序号排序
    permissionVOList.sort(
        Comparator.comparing(PermissionVO::getOrdinal, Comparator.nullsLast(Integer::compareTo)));
    // 构建权限编码到权限的Map，用于查找父权限
    Map<String, PermissionVO> permissionMap =
        permissionVOList.stream()
            .collect(Collectors.toMap(PermissionVO::getPermissionCode, permission -> permission));
    List<PermissionVO> rootPermissionList = new ArrayList<>();
    for (PermissionVO permissionVO : permissionVOList) {
      String parentName = permissionVO.getParentName();
      if (Objects.isNull(parentName) || parentName.trim().isEmpty()) {
        // 根权限
        rootPermissionList.add(permissionVO);
      } else {
        // 子权限，查找父权限
        PermissionVO parentPermission = findParentByName(permissionMap, parentName);
        if (Objects.nonNull(parentPermission)) {
          if (Objects.isNull(parentPermission.getChildren())) {
            parentPermission.setChildren(new ArrayList<>());
          }
          parentPermission.getChildren().add(permissionVO);
        } else {
          rootPermissionList.add(permissionVO);
        }
      }
    }
    sortChildren(rootPermissionList);
    return rootPermissionList;
  }

  /** 根据父权限名称查找父权限 */
  private PermissionVO findParentByName(
      Map<String, PermissionVO> permissionMap, String parentName) {
    return permissionMap.values().stream()
        .filter(permission -> Objects.equals(permission.getPermissionName(), parentName))
        .findFirst()
        .orElse(null);
  }

  /** 递归排序子节点 */
  private void sortChildren(List<PermissionVO> rootPermissionList) {
    if (Objects.isNull(rootPermissionList)) {
      return;
    }
    for (PermissionVO permissionVO : rootPermissionList) {
      if (Objects.nonNull(permissionVO.getChildren()) && !permissionVO.getChildren().isEmpty()) {
        permissionVO
            .getChildren()
            .sort(
                Comparator.comparing(
                    PermissionVO::getOrdinal, Comparator.nullsLast(Integer::compareTo)));
        sortChildren(permissionVO.getChildren());
      }
    }
  }
}
