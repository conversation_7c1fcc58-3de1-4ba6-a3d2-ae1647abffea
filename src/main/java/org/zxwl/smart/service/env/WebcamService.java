package org.zxwl.smart.service.env;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.WebcamCreateRequest;
import org.zxwl.smart.domain.request.env.WebcamDeleteRequest;
import org.zxwl.smart.domain.request.env.WebcamListRequest;
import org.zxwl.smart.domain.request.env.WebcamUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.env.WebcamVO;

@Validated
public interface WebcamService {

  int syncWebcamToMediaMTX();

  PageResult<WebcamVO> getWebcamList(@NotNull @Valid WebcamListRequest request);

  BaseResult createWebcam(@NotNull @Valid WebcamCreateRequest request);

  BaseResult updateWebcam(@NotNull @Valid WebcamUpdateRequest request);

  BaseResult deleteWebcam(@NotNull @Valid WebcamDeleteRequest request);
}
