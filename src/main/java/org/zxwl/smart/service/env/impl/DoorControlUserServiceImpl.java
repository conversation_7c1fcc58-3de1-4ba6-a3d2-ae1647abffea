package org.zxwl.smart.service.env.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.common.convert.env.EnvDeviceConvert;
import org.zxwl.smart.constant.enums.env.DoorControlUserStatusEnum;
import org.zxwl.smart.domain.excel.DoorControlUserImportTemplate;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.ResultCodeEnum;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.mybatis.ext.env.DoorControlPassTimeExt;
import org.zxwl.smart.domain.response.env.DoorControlUserCopyResultVO;
import org.zxwl.smart.domain.response.env.DoorControlUserImportResultVO;
import org.zxwl.smart.domain.response.env.DoorControlUserVO;
import org.zxwl.smart.listener.DoorControlUserImportListener;
import org.zxwl.smart.mybatis.entity.device.DoorControlUser;
import org.zxwl.smart.mybatis.mapper.device.DoorControlRecordMapper;
import org.zxwl.smart.mybatis.mapper.device.DoorControlUserMapper;
import org.zxwl.smart.service.env.DoorControlUserService;

@Slf4j
@Service
@AllArgsConstructor
public class DoorControlUserServiceImpl implements DoorControlUserService {

  private final DoorControlUserMapper doorControlUserMapper;
  private final EnvDataCache envDataCache;
  private final DoorControlRecordMapper doorControlRecordMapper;

  @Override
  public PageResult<DoorControlUserVO> getDoorControlUserList(DoorControlUserListRequest request) {
    String deviceNo = request.getDeviceNo();
    String userName = request.getUserName();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<DoorControlUser> userList = doorControlUserMapper.selectList(deviceNo, userName);
    if (userList == null || userList.isEmpty()) {
      return new PageResult<>("用户列表为空");
    }
    List<String> idCardList =
        userList.stream()
            .map(DoorControlUser::getIdCard)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
    // 批量查询最后通行时间
    Map<String, LocalDateTime> lastPassTimeMap = new HashMap<>();
    if (!idCardList.isEmpty()) {
      List<DoorControlPassTimeExt> voList =
          doorControlRecordMapper.selectBatchLastTimeByUsers(deviceNo, idCardList);
      for (DoorControlPassTimeExt vo : voList) {
        String idCard = vo.getIdCard();
        LocalDateTime lastPassTime = vo.getLastPassTime();
        if (idCard != null && lastPassTime != null) {
          // 如果已存在相同idCard的记录，保留时间最新的
          LocalDateTime existingTime = lastPassTimeMap.get(idCard);
          if (existingTime == null || lastPassTime.isAfter(existingTime)) {
            lastPassTimeMap.put(idCard, lastPassTime);
          }
        }
      }
    }
    List<DoorControlUserVO> voList =
        userList.stream()
            .map(
                user -> {
                  DoorControlUserVO vo = EnvDeviceConvert.getDoorControlUserVO(user);
                  vo.setLastPassTime(lastPassTimeMap.get(user.getIdCard()));
                  return vo;
                })
            .toList();
    return new PageResult<>(voList, userList);
  }

  @Override
  public BaseResult createDoorControlUser(DoorControlUserCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    String idCard = request.getIdCard();
    String deviceNo = request.getDeviceNo();
    String userPwd = request.getUserPwd();
    DoorControlUser user = EnvDeviceConvert.getDoorControlUserVO(request);
    if (StringUtils.isBlank(userPwd)) {
      return new SimpleResult<>("用户密码不能为空");
    }
    if (doorControlUserMapper.existByIdCard(idCard) > 0) {
      return new SimpleResult<>("用户编号不可重复");
    }
    user.setCreatedId(userId);
    user.setUpdatedId(userId);
    doorControlUserMapper.insert(user);
    envDataCache.addCommand(deviceNo, buildUserInfoCommand(idCard, user));
    return BaseResult.success();
  }

  @Override
  public BaseResult updateDoorControlUser(DoorControlUserUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    String idCard = request.getIdCard();
    DoorControlUser user = EnvDeviceConvert.getDoorControlUserVO(request);
    String deviceNo = user.getDeviceNo();
    if (doorControlUserMapper.existByIdCard(idCard) == 0) {
      return new SimpleResult<>("用户不存在");
    }
    user.setUpdatedId(userId);
    doorControlUserMapper.updateUser(user);
    envDataCache.addCommand(deviceNo, buildUserInfoCommand(idCard, user));
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteDoorControlUser(DoorControlUserDeleteRequest request) {
    List<Long> idList = request.getIdList();
    if (CollUtil.isEmpty(idList)) {
      return new SimpleResult<>("用户id列表不能为空");
    }
    List<DoorControlUser> userList = doorControlUserMapper.selectByIdList(idList);
    if (CollUtil.isEmpty(userList)) {
      return new SimpleResult<>("未找到要删除的用户");
    }
    doorControlUserMapper.deleteByIdList(
        idList, DoorControlUserStatusEnum.DISABLE.getValue(), System.currentTimeMillis());
    for (DoorControlUser user : userList) {
      envDataCache.addCommand(
          user.getDeviceNo(),
          "C:" + System.currentTimeMillis() + ":DATA DELETE USERINFO PIN=" + user.getIdCard());
    }
    //    envDataCache.addCommand(user.getDeviceNo(), "C:" + System.currentTimeMillis() +
    // ":REBOOT");
    return BaseResult.success();
  }

  @Override
  public SimpleResult<DoorControlUserImportResultVO> importDoorControlUser(
      MultipartFile multipartFile, String deviceNo) {
    if (multipartFile == null || multipartFile.isEmpty()) {
      return new SimpleResult<>("导入文件不能为空");
    }

    String originalFilename = multipartFile.getOriginalFilename();
    if (originalFilename == null
        || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
      return new SimpleResult<>("导入文件格式不正确,请上传 Excel 文件（.xlsx 或 .xls）");
    }
    try {
      // 创建 Excel 监听器
      DoorControlUserImportListener doorControlUserImportListener =
          new DoorControlUserImportListener(deviceNo);
      // 解析 Excel 文件
      EasyExcel.read(
              multipartFile.getInputStream(),
              DoorControlUserImportTemplate.class,
              doorControlUserImportListener)
          .sheet()
          .doRead();
      // 获取解析结果
      List<DoorControlUserCreateRequest> successList =
          doorControlUserImportListener.getSuccessList();
      List<DoorControlUserImportResultVO.FailRecord> failList =
          doorControlUserImportListener.getFailList();
      // 批量保存成功记录
      int successCount = 0;
      if (!successList.isEmpty()) {
        for (DoorControlUserCreateRequest request : successList) {
          try {
            BaseResult result = createDoorControlUser(request);
            if (result.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
              successCount++;
            } else {
              // 如果创建失败，添加到失败列表
              DoorControlUserImportResultVO.FailRecord failRecord =
                  new DoorControlUserImportResultVO.FailRecord();
              failRecord.setRowIndex(doorControlUserImportListener.getRowIndex(request));
              failRecord.setReason("创建失败：" + result.getMsg());
              failRecord.setUserName(request.getUserName());
              failRecord.setIdCard(request.getIdCard());
              failList.add(failRecord);
            }
          } catch (Exception e) {
            log.error("创建门禁用户失败", e);
            DoorControlUserImportResultVO.FailRecord failRecord =
                new DoorControlUserImportResultVO.FailRecord();
            failRecord.setRowIndex(doorControlUserImportListener.getRowIndex(request));
            failRecord.setReason("创建失败：" + e.getMessage());
            failRecord.setUserName(request.getUserName());
            failRecord.setIdCard(request.getIdCard());
            failList.add(failRecord);
          }
        }
      }
      DoorControlUserImportResultVO vo =
          DoorControlUserImportResultVO.builder()
              .total(successList.size() + failList.size())
              .successCount(successCount)
              .failCount(failList.size())
              .failRecordList(failList)
              .build();
      return new SimpleResult<>(vo);
    } catch (Exception e) {
      return new SimpleResult<>("导入失败：" + e.getMessage());
    }
  }

  @Override
  public SimpleResult<DoorControlUserCopyResultVO> copyDoorControlUser(
      @NotNull @Valid DoorControlUserCopyRequest request) {
    Long userId = AccountLocal.getUserId();
    String sourceDeviceNo = request.getSourceDeviceNo();
    String targetDeviceNo = request.getTargetDeviceNo();
    List<Long> userIdList = request.getUserIdList();
    if (sourceDeviceNo.equals(targetDeviceNo)) {
      return new SimpleResult<>("源设备和目标设备不能相同");
    }
    // 根据业务要求，复制所有用户
    List<DoorControlUser> sourceUserList = doorControlUserMapper.selectList(sourceDeviceNo, null);
    if (sourceUserList == null || sourceUserList.isEmpty()) {
      return new SimpleResult<>("源设备上没有可导入的用户");
    }
    int successCount = 0;
    List<DoorControlUserCopyResultVO.FailRecord> failRecordList = new ArrayList<>();

    // 复制用户
    for (DoorControlUser sourceUser : sourceUserList) {
      try {
        // 创建新用户对象
        DoorControlUser targetUser = new DoorControlUser();
        BeanUtils.copyProperties(sourceUser, targetUser);
        targetUser.setDeviceNo(targetDeviceNo);
        targetUser.setCreatedId(userId);
        targetUser.setUpdatedId(userId);
        doorControlUserMapper.insert(targetUser);
        envDataCache.addCommand(
            targetDeviceNo, buildUserInfoCommand(targetUser.getIdCard(), targetUser));
        successCount++;
      } catch (Exception e) {
        // 记录失败信息
        DoorControlUserCopyResultVO.FailRecord failRecord =
            new DoorControlUserCopyResultVO.FailRecord();
        failRecord.setUserName(sourceUser.getUserName());
        failRecord.setIdCard(sourceUser.getIdCard());
        failRecord.setReason("复制失败：" + e.getMessage());
        failRecordList.add(failRecord);
      }
    }
    DoorControlUserCopyResultVO vo =
        DoorControlUserCopyResultVO.builder()
            .total(sourceUserList.size())
            .successCount(successCount)
            .failCount(failRecordList.size())
            .failRecordList(failRecordList)
            .build();
    return new SimpleResult<>(vo);
  }

  private String buildUserInfoCommand(String idCard, DoorControlUser user) {
    return "C:"
        + System.currentTimeMillis()
        + ":DATA UPDATE USERINFO PIN="
        + idCard
        + "\tName="
        + user.getUserName()
        + "\tPri="
        + user.getPri()
        + "\tPasswd="
        + user.getUserPwd()
        + "\tCard="
        + user.getCard()
        + "\tGrp="
        + user.getUserGroup()
        + "\tTZ="
        + user.getTz()
        + "\tVerify="
        + user.getVerifyType()
        + "\tViceCard="
        + user.getViceCard()
        + "\tStartDateTime="
        + user.getStartDateTime()
        + "\tEndDateTime="
        + user.getEndDateTime();
  }
}
