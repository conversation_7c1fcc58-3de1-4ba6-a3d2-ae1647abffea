package org.zxwl.smart.service.env.impl;

import org.springframework.stereotype.Service;
import org.zxwl.smart.domain.request.env.SmartDeviceCreateRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceDeleteRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceListRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.env.SmartDeviceVO;
import org.zxwl.smart.service.env.SmartDeviceService;

@Service
public class SmartDeviceServiceImpl implements SmartDeviceService {

  @Override
  public PageResult<SmartDeviceVO> getSmartDeviceList(SmartDeviceListRequest request) {
    return new PageResult<>(null, null);
  }

  @Override
  public BaseResult createSmartDevice(SmartDeviceCreateRequest request) {
    return BaseResult.success();
  }

  @Override
  public BaseResult updateSmartDevice(SmartDeviceUpdateRequest request) {
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteSmartDevice(SmartDeviceDeleteRequest request) {
    return BaseResult.success();
  }
}
