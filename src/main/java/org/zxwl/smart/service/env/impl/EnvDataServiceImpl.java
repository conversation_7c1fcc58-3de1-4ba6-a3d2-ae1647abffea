package org.zxwl.smart.service.env.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import com.github.pagehelper.PageHelper;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.cache.model.BuildingCacheModel;
import org.zxwl.smart.cache.model.WarehouseCacheModel;
import org.zxwl.smart.common.convert.env.EnvDataConvert;
import org.zxwl.smart.common.convert.env.EnvDeviceConvert;
import org.zxwl.smart.common.utils.ColUtil;
import org.zxwl.smart.common.utils.EnvDataUtil;
import org.zxwl.smart.common.utils.ResponseUtil;
import org.zxwl.smart.constant.enums.stats.StatsSummaryTypeEnum;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.*;
import org.zxwl.smart.mybatis.entity.device.*;
import org.zxwl.smart.mybatis.entity.device.EnvAirQualityRecord;
import org.zxwl.smart.mybatis.entity.device.EnvEnergyRecord;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.warehouse.AreaDeviceMapper;
import org.zxwl.smart.mybatis.mapper.device.*;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.env.EnvDataService;

@Service
@AllArgsConstructor
public class EnvDataServiceImpl implements EnvDataService {

  private final WarehouseMapper warehouseMapper;
  private final AreaDeviceMapper areaDeviceMapper;
  private EnvDataCache envDataCache;
  private EnvAirQualityRecordMapper envAirQualityRecordMapper;
  private EnvEnergyRecordMapper envEnergyRecordMapper;
  private EnvDeviceLogMapper envDeviceLogMapper;
  private EnvDeviceMapper envDeviceMapper;
  private UserMapper userMapper;

  @Override
  public SimpleResult<EnvDataVO> getEnvData(EnvDataRequest request) {
    Long warehouseId = request.getWarehouseId();
    EnvDataVO vo;
    if (Objects.isNull(warehouseId)) {
      // 查询全局数据
      BuildingCacheModel buildingCacheModel = envDataCache.getBuilding();
      vo = EnvDeviceConvert.getEnvDataVO(buildingCacheModel);
    } else {
      // 查询库房数据
      WarehouseCacheModel warehouseCacheModel = envDataCache.getWarehouse(warehouseId);
      if (Objects.nonNull(warehouseCacheModel)) {
        vo = EnvDeviceConvert.getEnvDataVO(warehouseCacheModel);
      } else {
        vo = new EnvDataVO();
      }
    }
    return new SimpleResult<>(vo);
  }

  @Override
  public ListResult<EnvAirQualitySummaryRecordVO> getEnvAirQualitySummaryRecordList(
      EnvAirQualitySummaryRecordRequest request) {
    String summaryType = request.getSummaryType();
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    List<Date> createAtRange = request.getCreatedAtRange();
    Date beginCreatedAt = null;
    Date endCreatedAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreatedAt = createAtRange.get(0);
      endCreatedAt = createAtRange.get(1);
    }
    List<EnvAirQualityRecord> recordList;
    if (Objects.nonNull(areaId)) {
      recordList =
          envAirQualityRecordMapper.selectListByAreaId(areaId, beginCreatedAt, endCreatedAt);
    } else if (Objects.nonNull(deviceId)) {
      recordList =
          envAirQualityRecordMapper.selectListByDeviceId(deviceId, beginCreatedAt, endCreatedAt);
    } else {
      recordList =
          envAirQualityRecordMapper.selectListByWarehouseId(
              warehouseId, beginCreatedAt, endCreatedAt);
    }
    List<EnvAirQualitySummaryRecordVO> voList =
        EnvDataConvert.getEnvAirQualityByHourRecordVOList(recordList);
    voList =
        ColUtil.mergeList(
            voList,
            vo -> {
              DateTime createdAt = vo.getCreatedAt();
              if (StatsSummaryTypeEnum.MONTH.equalsValue(summaryType)) {
                return createdAt.year() + "-" + createdAt.month();
              } else if (StatsSummaryTypeEnum.DAY.equalsValue(summaryType)) {
                return createdAt.year() + "-" + createdAt.month() + "-" + createdAt.dayOfMonth();
              } else {
                return createdAt.year()
                    + "-"
                    + createdAt.month()
                    + "-"
                    + createdAt.dayOfMonth()
                    + "_"
                    + createdAt.hour(true);
              }
            },
            vos -> {
              EnvAirQualitySummaryRecordVO vo = vos.get(0);
              vo.setTemperature(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getTemperature).toList()));
              vo.setHumidity(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getHumidity).toList()));
              vo.setPm25(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getPm25).toList()));
              vo.setPm10(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getPm10).toList()));
              vo.setTvoc(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getTvoc).toList()));
              vo.setCo2(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getCo2).toList()));
              vo.setHcho(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getHcho).toList()));
              vo.setO3(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getO3).toList()));
              vo.setLight(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getLight).toList()));
              vo.setNo2(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getNo2).toList()));
              vo.setSo2(
                  EnvDataUtil.calculateMean(
                      vos.stream().map(EnvAirQualitySummaryRecordVO::getSo2).toList()));
              DateTime createdAt = vo.getCreatedAt();
              if (StatsSummaryTypeEnum.MONTH.getValue().equals(summaryType)) {
                createdAt.setField(DateField.DAY_OF_MONTH, 1);
                createdAt.setField(DateField.HOUR_OF_DAY, 0);
                createdAt.setField(DateField.MINUTE, 0);
                createdAt.setField(DateField.SECOND, 0);
              } else if (StatsSummaryTypeEnum.DAY.getValue().equals(summaryType)) {
                createdAt.setField(DateField.HOUR_OF_DAY, 0);
                createdAt.setField(DateField.MINUTE, 0);
                createdAt.setField(DateField.SECOND, 0);
              } else {
                createdAt.setField(DateField.MINUTE, 0);
                createdAt.setField(DateField.SECOND, 0);
              }
              return vo;
            });
    voList.sort(Comparator.comparing(EnvAirQualitySummaryRecordVO::getCreatedAt));
    return new ListResult<>(voList);
  }

  @Override
  public PageResult<EnvAirQualityRecordVO> getEnvAirQualityRecordList(
      EnvAirQualityRecordRequest request) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    List<Date> createAtRange = request.getCreatedAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    Date beginCreatedAt = null;
    Date endCreatedAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreatedAt = createAtRange.get(0);
      endCreatedAt = createAtRange.get(1);
    }
    PageHelper.startPage(pageNum, pageSize);
    List<EnvAirQualityRecord> recordList;
    if (Objects.nonNull(areaId)) {
      recordList =
          envAirQualityRecordMapper.selectListByAreaId(areaId, beginCreatedAt, endCreatedAt);
    } else if (Objects.nonNull(deviceId)) {
      recordList =
          envAirQualityRecordMapper.selectListByDeviceId(deviceId, beginCreatedAt, endCreatedAt);
    } else {
      recordList =
          envAirQualityRecordMapper.selectListByWarehouseId(
              warehouseId, beginCreatedAt, endCreatedAt);
    }
    List<EnvAirQualityRecordVO> voList = EnvDataConvert.EnvAirQualityRecordVO(recordList);
    return new PageResult<>(voList, recordList);
  }

  @Override
  public void getEnvAirQualityRecordExcel(
      EnvAirQualityRecordRequest request, HttpServletResponse response) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    List<Date> createAtRange = request.getCreatedAtRange();
    Date beginCreatedAt = null;
    Date endCreatedAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreatedAt = createAtRange.get(0);
      endCreatedAt = createAtRange.get(1);
    }
    List<EnvAirQualityRecord> recordList;
    if (Objects.nonNull(areaId)) {
      recordList =
          envAirQualityRecordMapper.selectListByAreaId(areaId, beginCreatedAt, endCreatedAt);
    } else if (Objects.nonNull(deviceId)) {
      recordList =
          envAirQualityRecordMapper.selectListByDeviceId(deviceId, beginCreatedAt, endCreatedAt);
    } else {
      recordList =
          envAirQualityRecordMapper.selectListByWarehouseId(
              warehouseId, beginCreatedAt, endCreatedAt);
    }
    // todo：后面要做大数据处理，分多条查询，多批次往excel写入
    List<EnvAirQualityRecordExcelVO> voList =
        EnvDataConvert.getEnvAirQualityRecordExcelVOList(recordList);
    ResponseUtil.exportExcel(response, voList, EnvAirQualityRecordExcelVO.class, "空气质量记录");
  }

  @Override
  public PageResult<EnvEnergyRecordVO> getEnvEnergyRecordList(EnvEnvEnergyRecordRequest request) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    List<Date> createAtRange = request.getCreatedAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    Date beginCreatedAt = null;
    Date endCreatedAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreatedAt = createAtRange.get(0);
      endCreatedAt = createAtRange.get(1);
    }
    PageHelper.startPage(pageNum, pageSize);
    List<EnvEnergyRecord> recordList;
    if (Objects.nonNull(areaId)) {
      recordList = envEnergyRecordMapper.selectListByAreaId(areaId, beginCreatedAt, endCreatedAt);
    } else if (Objects.nonNull(deviceId)) {
      recordList =
          envEnergyRecordMapper.selectListByDeviceId(deviceId, beginCreatedAt, endCreatedAt);
    } else {
      recordList =
          envEnergyRecordMapper.selectListByWarehouseId(warehouseId, beginCreatedAt, endCreatedAt);
    }
    List<EnvEnergyRecordVO> voList = EnvDataConvert.getEnvEnergyRecordVO(recordList);
    return new PageResult<>(voList, recordList);
  }

  @Override
  public void getEnvEnergyRecordExcel(
      EnvEnvEnergyRecordRequest request, HttpServletResponse response) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    List<Date> createAtRange = request.getCreatedAtRange();
    Date beginCreatedAt = null;
    Date endCreatedAt = null;
    if (Objects.nonNull(createAtRange)) {
      beginCreatedAt = createAtRange.get(0);
      endCreatedAt = createAtRange.get(1);
    }
    List<EnvEnergyRecord> recordList;
    if (Objects.nonNull(areaId)) {
      recordList = envEnergyRecordMapper.selectListByAreaId(areaId, beginCreatedAt, endCreatedAt);
    } else if (Objects.nonNull(deviceId)) {
      recordList =
          envEnergyRecordMapper.selectListByDeviceId(deviceId, beginCreatedAt, endCreatedAt);
    } else {
      recordList =
          envEnergyRecordMapper.selectListByWarehouseId(warehouseId, beginCreatedAt, endCreatedAt);
    }
    List<EnvEnergyRecordExcelVO> voList = EnvDataConvert.getEnvEnergyRecordExcelVO(recordList);
    ResponseUtil.exportExcel(response, voList, EnvEnergyRecordExcelVO.class, "能耗记录");
  }

  @Override
  public PageResult<EnvDeviceLogVO> getEnvDeviceLogList(EnvDeviceLogRequest request) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    Long createdId = request.getCreatedId();
    List<Date> createdAtRange = request.getCreatedAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    Date createdAtBegin = null;
    Date createdAtEnd = null;
    if (Objects.nonNull(createdAtRange)) {
      createdAtBegin = createdAtRange.get(0);
      createdAtEnd = createdAtRange.get(1);
    }
    List<Long> deviceIdList = null;
    if (Objects.nonNull(areaId)) {
      deviceIdList = areaDeviceMapper.selectEnvDeviceIdListByAreaId(areaId);
    }
    if (Objects.nonNull(deviceIdList) && deviceIdList.isEmpty()) {
      return new PageResult<>(new ArrayList<>(), new ArrayList<>());
    }
    PageHelper.startPage(pageNum, pageSize);
    List<EnvDeviceLog> logList =
        envDeviceLogMapper.selectList(
            warehouseId, deviceIdList, deviceId, createdId, createdAtBegin, createdAtEnd);
    List<EnvDeviceLogVO> volist = getEnvDeviceLogVOList(logList);
    return new PageResult<>(volist, logList);
  }

  @Override
  public void getEnvDeviceLogExcel(EnvDeviceLogRequest request, HttpServletResponse response) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Long deviceId = request.getDeviceId();
    Long createdId = request.getCreatedId();
    List<Date> createdAtRange = request.getCreatedAtRange();
    Date createdAtBegin = null;
    Date createdAtEnd = null;
    if (Objects.nonNull(createdAtRange)) {
      createdAtBegin = createdAtRange.get(0);
      createdAtEnd = createdAtRange.get(1);
    }
    List<Long> deviceIdList = null;
    if (Objects.nonNull(areaId)) {
      deviceIdList = areaDeviceMapper.selectEnvDeviceIdListByAreaId(areaId);
    }
    List<EnvDeviceLogExcelVO> voList;
    if (Objects.nonNull(deviceIdList) && deviceIdList.isEmpty()) {
      voList = new ArrayList<>();
    } else {
      List<EnvDeviceLog> logList =
          envDeviceLogMapper.selectList(
              warehouseId, deviceIdList, deviceId, createdId, createdAtBegin, createdAtEnd);
      voList = getEnvDeviceLogExcelVOList(logList);
    }
    ResponseUtil.exportExcel(response, voList, EnvDeviceLogExcelVO.class, "设备调控日志记录表");
  }

  @Override
  public BaseResult deleteEnvDeviceLog(EnvDeviceLogDeleteRequest request) {
    List<Long> idList = request.getIdList();
    envDeviceLogMapper.deleteByIdList(idList);
    return BaseResult.success();
  }

  private List<EnvDeviceLogVO> getEnvDeviceLogVOList(List<EnvDeviceLog> logList) {
    if (CollUtil.isEmpty(logList)) {
      return new ArrayList<>();
    }
    List<EnvDeviceLogVO> voList = EnvDataConvert.getEnvDeviceLogVOList(logList);
    // 库房信息完善
    List<Long> warehouseIdList =
        logList.stream().map(EnvDeviceLog::getWarehouseId).distinct().toList();
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    for (EnvDeviceLogVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    // 操作人名信息完善
    List<Long> createdIdList = logList.stream().map(EnvDeviceLog::getCreatedId).distinct().toList();
    List<User> userList = userMapper.selectNameListByIdList(createdIdList, true);
    Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, v -> v));
    for (EnvDeviceLogVO vo : voList) {
      User user = userMap.get(vo.getCreatedId());
      if (Objects.nonNull(user)) {
        vo.setCreatedName(user.getUserName());
      }
    }
    return voList;
  }

  private List<EnvDeviceLogExcelVO> getEnvDeviceLogExcelVOList(List<EnvDeviceLog> logList) {
    if (CollUtil.isEmpty(logList)) {
      return new ArrayList<>();
    }
    List<EnvDeviceLogExcelVO> voList = EnvDataConvert.getEnvDeviceLogExcelVOList(logList);
    // 完善库房信息
    List<Long> warehouseIdList =
        logList.stream().map(EnvDeviceLog::getWarehouseId).distinct().toList();
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    for (EnvDeviceLogExcelVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    // 操作人名信息完善
    List<Long> createdIdList = logList.stream().map(EnvDeviceLog::getCreatedId).distinct().toList();
    List<User> userList = userMapper.selectNameListByIdList(createdIdList, true);
    Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, v -> v));
    for (EnvDeviceLogExcelVO vo : voList) {
      User user = userMap.get(vo.getCreatedId());
      if (Objects.nonNull(user)) {
        vo.setCreatedName(user.getUserName());
      }
    }
    return voList;
  }
}
