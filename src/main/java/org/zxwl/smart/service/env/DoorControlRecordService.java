package org.zxwl.smart.service.env;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.DoorControlRecordCountRequest;
import org.zxwl.smart.domain.request.env.DoorControlRecordDeleteRequest;
import org.zxwl.smart.domain.request.env.DoorControlRecordListRequest;
import org.zxwl.smart.domain.request.env.DoorControlSingleRecordCountRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.DoorControlRecordCountVO;
import org.zxwl.smart.domain.response.env.DoorControlRecordVO;

@Validated
public interface DoorControlRecordService {

  PageResult<DoorControlRecordVO> getDoorControlRecordList(
      @NotNull @Valid DoorControlRecordListRequest request);

  void getDoorControlRecordExcel(
      @NotNull @Valid DoorControlRecordListRequest request, HttpServletResponse response);

  SimpleResult<DoorControlRecordCountVO> getDoorControlRecordCount(
      @NotNull @Valid DoorControlRecordCountRequest request);

  SimpleResult<DoorControlRecordCountVO> getSingleDoorControlRecordCount(
      @NotNull @Valid DoorControlSingleRecordCountRequest request);

  BaseResult deleteDoorControlRecord(DoorControlRecordDeleteRequest request);
}
