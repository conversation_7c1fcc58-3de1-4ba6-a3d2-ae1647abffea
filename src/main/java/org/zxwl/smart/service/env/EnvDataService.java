package org.zxwl.smart.service.env;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.request.env.EnvAirQualityRecordRequest;
import org.zxwl.smart.domain.request.env.EnvAirQualitySummaryRecordRequest;
import org.zxwl.smart.domain.request.env.EnvDataRequest;
import org.zxwl.smart.domain.request.env.EnvEnvEnergyRecordRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.*;
import org.zxwl.smart.domain.response.env.EnvAirQualityRecordVO;
import org.zxwl.smart.domain.response.env.EnvAirQualitySummaryRecordVO;
import org.zxwl.smart.domain.response.env.EnvDataVO;
import org.zxwl.smart.domain.response.env.EnvEnergyRecordVO;

@Validated
public interface EnvDataService {

  SimpleResult<EnvDataVO> getEnvData(@NotNull @Valid EnvDataRequest request);

  ListResult<EnvAirQualitySummaryRecordVO> getEnvAirQualitySummaryRecordList(
      @NotNull @Valid EnvAirQualitySummaryRecordRequest request);

  PageResult<EnvAirQualityRecordVO> getEnvAirQualityRecordList(
      @NotNull @Valid EnvAirQualityRecordRequest request);

  void getEnvAirQualityRecordExcel(
      @NotNull @Valid EnvAirQualityRecordRequest request, HttpServletResponse response);

  PageResult<EnvEnergyRecordVO> getEnvEnergyRecordList(
      @NotNull @Valid EnvEnvEnergyRecordRequest request);

  void getEnvEnergyRecordExcel(
      @NotNull @Valid EnvEnvEnergyRecordRequest request, HttpServletResponse response);

  PageResult<EnvDeviceLogVO> getEnvDeviceLogList(@NotNull @Valid EnvDeviceLogRequest request);

  void getEnvDeviceLogExcel(
      @NotNull @Valid EnvDeviceLogRequest request, HttpServletResponse response);

  BaseResult deleteEnvDeviceLog(EnvDeviceLogDeleteRequest request);
}
