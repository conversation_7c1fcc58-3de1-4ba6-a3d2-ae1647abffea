package org.zxwl.smart.service.env.impl;

import static cn.hutool.core.util.CharsetUtil.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.cache.model.DoorControlDeviceCacheModel;
import org.zxwl.smart.common.utils.RequestUtil;
import org.zxwl.smart.constant.consist.DoorControlDeviceConstant;
import org.zxwl.smart.constant.enums.env.DoorControlMethodEnum;
import org.zxwl.smart.constant.enums.env.DoorControlUserStatusEnum;
import org.zxwl.smart.domain.request.env.DoorControlDeviceHttpUploadDataParams;
import org.zxwl.smart.domain.request.env.DoorControlDeviceInitParams;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.mybatis.entity.device.DoorControlUser;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;
import org.zxwl.smart.mybatis.mapper.device.DoorControlRecordMapper;
import org.zxwl.smart.mybatis.mapper.device.DoorControlUserMapper;
import org.zxwl.smart.mybatis.mapper.device.EnvDeviceMapper;
import org.zxwl.smart.service.env.DoorControlDeviceHttpService;

@Slf4j
@Service
@AllArgsConstructor
public class DoorControlDeviceHttpServiceImpl implements DoorControlDeviceHttpService {

  private final DoorControlRecordMapper doorControlRecordMapper;
  private final DoorControlUserMapper doorControlUserMapper;
  private final EnvDataCache envDataCache;
  private final ObjectMapper objectMapper;
  private final EnvDeviceMapper envDeviceMapper;

  // 初始化设备配置信息
  @Override
  public String initOptions(DoorControlDeviceInitParams params) {
    String deviceNo = params.getSN();
    return String.format(
        """
    GET OPTION FROM: %s
    ATTLOGStamp=None
    OPERLOGStamp=9999
    ATTPHOTOStamp=None
    ErrorDelay=30
    Delay=10
    TransTimes=00:00;10:20
    TransInterval=1
    TransFlag=TransData AttLog\tOpLog\tEnrollUser\tChgUser\tEnrollFP\tChgFP\tFACE\tWORKCODE
    TimeZone=8
    Realtime=1
    Encrypt=None
    """,
        deviceNo);
  }

  @Override
  public String uploadData(
      DoorControlDeviceHttpUploadDataParams params, HttpServletRequest request) {
    // 解析请求体
    String dataRecord = RequestUtil.getBody(request, GBK);
    String deviceNo = params.getSN();
    String table = params.getTable();
    if (deviceNo == null) {
      try {
        return new String("设备编号为空".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备编号为空";
      }
    }
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      try {
        return new String("设备不存在".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备不存在";
      }
    }
    // 更新缓存中设备状态
    updateDoorControlDeviceStatus(deviceNo);
    String[] codes = dataRecord.split("\n");
    String result =
        switch (table) {
          // 上传考勤记录
          case "ATTLOG" -> {
            uploadAttendanceLog(deviceNo, codes);
            yield DoorControlDeviceConstant.OK + ":" + codes.length;
          }
          // 上传操作日志
          case "OPERLOG" -> {
            uploadOperationLog(deviceNo, codes);
            yield DoorControlDeviceConstant.OK + ":" + codes.length;
          }
          // 上传一体化模板
          case "BIODATA" -> {
            uploadBioData(deviceNo, codes);
            yield DoorControlDeviceConstant.OK + ":" + codes.length;
          }
          case "options" -> DoorControlDeviceConstant.OK;
          default -> {
            try {
              yield new String("处理失败".getBytes(DoorControlDeviceConstant.GB2312));
            } catch (UnsupportedEncodingException e) {
              yield "处理失败";
            }
          }
        };
    return result;
  }

  @Override
  public String getRequest(String deviceNo) {
    if (deviceNo == null) {
      try {
        return new String("设备编号为空".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备编号为空";
      }
    }
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      try {
        return new String("设备不存在".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备不存在";
      }
    }
    // 更新缓存中设备状态
    updateDoorControlDeviceStatus(deviceNo);
    List<String> commands = envDataCache.getCommandQueue(deviceNo);
    StringBuilder msg = new StringBuilder();
    if (commands.isEmpty()) {
      msg.append(DoorControlDeviceConstant.OK);
    } else {
      for (String command : commands) {
        msg.append(command).append("\n");
      }
      envDataCache.clearCommandQueue(deviceNo);
      try {
        return new String(msg.toString().getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return DoorControlDeviceConstant.OK;
      }
    }
    return msg.toString();
  }

  @Override
  public String ping(String deviceNo) {
    if (deviceNo == null) {
      try {
        return new String("设备编号为空".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备编号为空";
      }
    }
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      try {
        return new String("设备不存在".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备不存在";
      }
    }
    // 更新缓存中设备状态
    updateDoorControlDeviceStatus(deviceNo);
    try {
      return new String(DoorControlDeviceConstant.OK.getBytes(DoorControlDeviceConstant.GB2312));
    } catch (UnsupportedEncodingException e) {
      return DoorControlDeviceConstant.OK;
    }
  }

  @Override
  public String deviceCommand(String deviceNo, HttpServletRequest request) {
    String cmdRecord = RequestUtil.getBody(request);
    System.out.println(cmdRecord);
    if (deviceNo == null) {
      try {
        return new String("设备编号为空".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "设备编号为空";
      }
    }
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      try {
        return new String("缓存中不存在该设备".getBytes(DoorControlDeviceConstant.GB2312));
      } catch (UnsupportedEncodingException e) {
        return "缓存中不存在该设备";
      }
    }
    // 更新缓存中设备状态
    updateDoorControlDeviceStatus(deviceNo);
    try {
      return new String(DoorControlDeviceConstant.OK.getBytes(DoorControlDeviceConstant.GB2312));
    } catch (UnsupportedEncodingException e) {
      return DoorControlDeviceConstant.OK;
    }
  }

  // 处理考勤记录上传
  private void uploadAttendanceLog(String deviceNo, String[] codes) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    for (String code : codes) {
      String[] temp = code.split("\t");
      if (temp.length < 4) {
        continue;
      }
      // 获取用户工号和验证类型
      String idCard = temp[0];
      String verifyType = temp[3];
      // 根据验证类型获取开门方式
      String controlMethod;
      try {
        int verify = Integer.parseInt(verifyType);
        controlMethod = DoorControlMethodEnum.getVerifyTypeByValue(verify);
      } catch (NumberFormatException e) {
        controlMethod = "other";
      }
      DoorControlUser user =
          doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
      if (user == null) {
        continue;
      }
      EnvDevice envDevice = envDeviceMapper.selectByDeviceNo(cacheModel.getDeviceNo());
      Long userId = user.getId();
      String userName = user.getUserName();
      if (userName.isEmpty()) {
        // 创建命令加入命令队列缓存
        envDataCache.addCommand(
            deviceNo, "C:" + System.currentTimeMillis() + ":DATA QUERY USERINFO PIN=" + idCard);
        doorControlRecordMapper.insert(
            cacheModel.getDeviceNo(),
            cacheModel.getWarehouseId(),
            envDevice.getDeviceName(),
            controlMethod,
            userId,
            userName,
            idCard);
      }
    }
  }

  // 上传操作日志
  private void uploadOperationLog(String deviceNo, String[] codes) {
    for (String code : codes) {
      String[] temp = code.split("\t");
      //      log.info("处理生物识别数据: deviceNo={}, 数据={}, 数组长度={}", deviceNo, code, temp.length);
      // 上传用户信息
      if (temp[0].startsWith("USER PIN")) {
        uploadUserInfo(deviceNo, temp);
      }
      // 上传指纹模板
      if (temp[0].startsWith("FP PIN")) {
        uploadFingerTemplate(deviceNo, temp);
      }
      // 上传面部模板
      if (temp[0].startsWith("FACE PIN")) {
        uploadFaceTemplate(deviceNo, temp);
      }
      // 删除用户
      if (temp[0].startsWith("OPLOG 103")) {
        deleteUser(deviceNo, temp);
      }
      // 出门开关
      if (temp[0].startsWith("OPLOG 53")) {
        //        doorSwitch(deviceNo, temp);
        // TODO: 逻辑待补充
      }
    }
  }

  // 上传一体化模板
  private void uploadBioData(String deviceNo, String[] codes) {
    for (String code : codes) {
      String[] temp = code.split("\t");
      // 上传面部模板
      if (temp.length > 5 && "9".equals(temp[5].substring(5))) {
        uploadFaceData(deviceNo, temp);
      }
      // 上传指纹模板
      if (temp.length > 5 && "1".equals(temp[5].substring(5))) {
        uploadFingerData(deviceNo, temp);
      }
      // 上传用户信息
      if (temp.length > 5 && "0".equals(temp[5].substring(5))) {
        uploadUserInfo(deviceNo, temp);
      }
    }
  }

  // 上传用户信息
  private void uploadUserInfo(String deviceNo, String[] temp) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    String idCard = temp[0].substring(9);
    String name = temp[1].substring(5);
    String pri = temp[2].substring(4);
    String passwd = temp[3].substring(7);
    String card = temp[4].substring(5);
    String grp = temp[5].substring(4);
    String tz = temp[6].substring(3);
    String verify = temp[7].substring(7);
    String viceCard = temp[8].substring(9);
    String startDatetime = temp[9].substring(14);
    String endDatetime = temp[10].substring(12);
    DoorControlUser user =
        doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
    if (user == null) {
      // 创建新用户
      user = new DoorControlUser();
      user.setIdCard(idCard);
      user.setDeviceNo(cacheModel.getDeviceNo());
      user.setUserName(name);
      user.setPri(Integer.parseInt(pri));
      user.setUserPwd(passwd);
      user.setCard(card);
      user.setUserGroup(Integer.valueOf(grp));
      user.setTz(tz);
      user.setVerifyType(Integer.valueOf(verify));
      user.setViceCard(viceCard);
      user.setStartDateTime(startDatetime);
      user.setEndDateTime(endDatetime);
      user.setUserStatus(DoorControlUserStatusEnum.NORMAL.getValue());
      doorControlUserMapper.insert(user);
    } else {
      // 更新用户信息
      doorControlUserMapper.updateUserInfo(
          name,
          pri,
          passwd,
          card,
          grp,
          tz,
          verify,
          viceCard,
          startDatetime,
          endDatetime,
          user.getId());
    }
  }

  // 上传指纹模板
  private void uploadFingerTemplate(String deviceNo, String[] temp) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    String idCard = temp[0].substring(7);
    int fingerId = Integer.parseInt(temp[1].substring(4));
    String tmp = temp[4].substring(4);

    DoorControlUser user =
        doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
    if (user == null) {
      envDataCache.addCommand(
          deviceNo, "C:" + System.currentTimeMillis() + ":DATA QUERY USERINFO PIN=" + idCard);
      return;
    }
    try {
      // 存储多个指纹模板
      Map<String, String> fingerTemplates;
      // 解析现有的指纹数据
      if (StringUtils.isNotBlank(user.getFingerId())) {
        fingerTemplates =
            objectMapper.readValue(user.getFingerId(), new TypeReference<Map<String, String>>() {});
      } else {
        fingerTemplates = new HashMap<>();
      }
      fingerTemplates.put(String.valueOf(fingerId), tmp);
      // 将指纹模板集合转化为 JSON 字符串
      user.setFingerId(objectMapper.writeValueAsString(fingerTemplates));
      doorControlUserMapper.updateUserFingerIdById(user);
    } catch (Exception e) {
      new SimpleResult<>("处理指纹模板失败");
    }
  }

  // 上传面部模板
  private void uploadFaceTemplate(String deviceNo, String[] temp) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    String idCard = temp[0].substring(9);
    String tmp = temp[4].substring(4);
    DoorControlUser user =
        doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
    if (user == null) {
      envDataCache.addCommand(
          deviceNo, "C:" + System.currentTimeMillis() + ":DATA QUERY USERINFO PIN=" + idCard);
      return;
    }
    user.setFaceId(tmp);
    doorControlUserMapper.updateUserFaceIdById(user);
  }

  // 删除用户
  private void deleteUser(String deviceNo, String[] temp) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    String idCard = temp[3];
    DoorControlUser user =
        doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
    if (user == null) {
      return;
    }
    user.setUserStatus(DoorControlUserStatusEnum.DISABLE.getValue());
    user.setDeleteAt(System.currentTimeMillis());
    doorControlUserMapper.deleteById(user);
  }

  // 上传面部模板
  private void uploadFaceData(String deviceNo, String[] temp) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    String idCard = temp[0].substring(12);
    String tmp = temp[9].substring(4);
    DoorControlUser user =
        doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
    if (user == null) {
      envDataCache.addCommand(
          deviceNo, "C:" + System.currentTimeMillis() + ":DATA QUERY USERINFO PIN=" + idCard);
    } else {
      user.setFaceId(tmp);
      doorControlUserMapper.updateUserFaceIdById(user);
    }
  }

  // 上传指纹模板
  private void uploadFingerData(String deviceNo, String[] temp) {
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel == null) {
      return;
    }
    String idCard = temp[0].substring(12);
    int fingerId = Integer.parseInt(temp[1].substring(3));
    String tmp = temp[9].substring(4);

    DoorControlUser user =
        doorControlUserMapper.selectByDeviceNoAndIdCard(cacheModel.getDeviceNo(), idCard);
    if (user == null) {
      envDataCache.addCommand(
          deviceNo, "C:" + System.currentTimeMillis() + ":DATA QUERY USERINFO PIN=" + idCard);
    } else {
      try {
        // 存储多个指纹模板
        Map<String, String> fingerTemplates;
        // 解析现有的指纹数据
        if (StringUtils.isNotBlank(user.getFingerId())) {
          fingerTemplates =
              objectMapper.readValue(
                  user.getFingerId(), new TypeReference<Map<String, String>>() {});
        } else {
          fingerTemplates = new HashMap<>();
        }
        fingerTemplates.put(String.valueOf(fingerId), tmp);
        // 将指纹模板集合转化为 JSON 字符串
        user.setFingerId(objectMapper.writeValueAsString(fingerTemplates));
        doorControlUserMapper.updateUserFingerIdById(user);
      } catch (Exception e) {
        new SimpleResult<>("处理指纹模板失败");
      }
    }
  }

  public void updateDoorControlDeviceStatus(String deviceNo) {
    if (deviceNo == null) {
      return;
    }
    DoorControlDeviceCacheModel cacheModel = envDataCache.getDevice(deviceNo);
    if (cacheModel != null) {
      boolean wasOffline = !cacheModel.isOnline();
      cacheModel.setOnline(true);
      cacheModel.setLastReceiveDataTime(System.currentTimeMillis());
      if (wasOffline) {
        log.info("门禁设备已上线, deviceNo={}", deviceNo);
      }
    }
  }
}
