package org.zxwl.smart.service.env;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.SmartDeviceCreateRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceDeleteRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceListRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.env.SmartDeviceVO;

@Validated
public interface SmartDeviceService {

  PageResult<SmartDeviceVO> getSmartDeviceList(@NotNull @Valid SmartDeviceListRequest request);

  BaseResult createSmartDevice(@NotNull @Valid SmartDeviceCreateRequest request);

  BaseResult updateSmartDevice(@NotNull @Valid SmartDeviceUpdateRequest request);

  BaseResult deleteSmartDevice(@NotNull @Valid SmartDeviceDeleteRequest request);
}
