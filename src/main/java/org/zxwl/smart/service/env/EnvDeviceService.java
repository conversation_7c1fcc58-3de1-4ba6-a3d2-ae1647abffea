package org.zxwl.smart.service.env;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.EnvDeviceTypeVO;
import org.zxwl.smart.domain.response.env.EnvDeviceVO;

@Validated
public interface EnvDeviceService {

  ListResult<EnvDeviceTypeVO> getDeviceTypeList();

  SimpleResult<EnvDeviceVO> getEnvDevice(@NotNull @Valid EnvDeviceRequest request);

  ListResult<EnvDeviceVO> getEnvDeviceList(@NotNull @Valid EnvDeviceListRequest request);

  BaseResult createEnvDevice(@NotNull @Valid EnvDeviceCreateRequest request);

  BaseResult updateEnvDevice(@NotNull @Valid EnvDeviceUpdateRequest request);

  BaseResult deleteEnvDevice(@NotNull @Valid EnvDeviceDeleteRequest request);

  BaseResult controlEnvDevice(@NotNull @Valid EnvDeviceControlRequest request);

  BaseResult controlEnvDeviceList(@NotNull @Valid EnvDeviceBatchControlRequest request);
}
