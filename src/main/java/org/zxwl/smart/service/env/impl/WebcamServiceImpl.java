package org.zxwl.smart.service.env.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.client.mediamtx.MediamtxApiClient;
import org.zxwl.smart.common.convert.env.WebCamConvert;
import org.zxwl.smart.common.properties.ProjectProperties;
import org.zxwl.smart.domain.request.env.WebcamCreateRequest;
import org.zxwl.smart.domain.request.env.WebcamDeleteRequest;
import org.zxwl.smart.domain.request.env.WebcamListRequest;
import org.zxwl.smart.domain.request.env.WebcamUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.env.WebcamVO;
import org.zxwl.smart.mybatis.entity.env.Webcam;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.env.WebcamMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.env.WebcamService;

@Service
@AllArgsConstructor
public class WebcamServiceImpl implements WebcamService {

  private final WebcamMapper webcamMapper;
  private final WarehouseMapper warehouseMapper;
  private final ProjectProperties projectProperties;

  @Override
  public int syncWebcamToMediaMTX() {
    List<Webcam> webcamList = webcamMapper.selectList(null, null, null);
    int count = 0;
    for (Webcam webcam : webcamList) {
      boolean success =
          MediamtxApiClient.addStreamPath(
              projectProperties.getMediamtxApiUser(),
              projectProperties.getMediamtxApiPass(),
              projectProperties.getMediamtxIp(),
              projectProperties.getMediamtxApiPort(),
              webcam.getId().toString(),
              webcam.getStreamUrl());
      if (success) count++;
    }
    return count;
  }

  @Override
  public PageResult<WebcamVO> getWebcamList(WebcamListRequest request) {
    Long warehouseId = request.getWarehouseId();
    Boolean includePublic = request.getIncludePublic();
    String deviceNameLike = request.getDeviceNameLike();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<Webcam> list = webcamMapper.selectList(warehouseId, includePublic, deviceNameLike);
    List<WebcamVO> voList = WebCamConvert.getWebcamVOList(list);
    // 拼接webRTC地址
    for (WebcamVO vo : voList) {
      vo.setWebRTCUrl(
          MediamtxApiClient.getWebRTCUrl(
              projectProperties.getMediamtxIp(),
              projectProperties.getMediamtxWebRtcPort(),
              vo.getId().toString()));
    }
    // 库房名字完善
    List<Long> warehouseIdList =
        list.stream().map(Webcam::getWarehouseId).filter(Objects::nonNull).distinct().toList();
    Map<Long, Warehouse> warehouseMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(warehouseIdList)) {
      List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
      warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    }
    for (WebcamVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    return new PageResult<>(voList, list);
  }

  @Override
  public BaseResult createWebcam(WebcamCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long warehouseId = request.getWarehouseId();
    if (warehouseMapper.existById(warehouseId) == 0) {
      return BaseResult.failed("库房不存在");
    }
    Webcam webcam = WebCamConvert.getWebcam(request);
    webcam.setCreatedId(userId);
    webcam.setUpdatedId(userId);
    webcamMapper.insert(webcam);
    // 将拉流地址放入MediaMTX Path中
    MediamtxApiClient.addStreamPath(
        projectProperties.getMediamtxApiUser(),
        projectProperties.getMediamtxApiPass(),
        projectProperties.getMediamtxIp(),
        projectProperties.getMediamtxApiPort(),
        webcam.getId().toString(),
        webcam.getStreamUrl());
    return BaseResult.success();
  }

  @Override
  public BaseResult updateWebcam(WebcamUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    String streamUrl = request.getStreamUrl();
    Webcam orgWebcam = webcamMapper.selectById(id);
    if (Objects.isNull(orgWebcam)) {
      return BaseResult.failed("摄像头不存在");
    }
    Webcam webcam = WebCamConvert.getWebcam(request);
    webcam.setUpdatedId(userId);
    webcamMapper.updateById(webcam);
    // 更新MediaMTX Path地址
    MediamtxApiClient.updateStreamPath(
        projectProperties.getMediamtxApiUser(),
        projectProperties.getMediamtxApiPass(),
        projectProperties.getMediamtxIp(),
        projectProperties.getMediamtxApiPort(),
        webcam.getId().toString(),
        streamUrl);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteWebcam(WebcamDeleteRequest request) {
    Long id = request.getId();
    Webcam orgWebcam = webcamMapper.selectById(id);
    if (Objects.isNull(orgWebcam)) {
      return BaseResult.failed("摄像头不存在");
    }
    webcamMapper.deleteById(id);
    // 删除MediaMTX Path地址
    MediamtxApiClient.deleteStreamPath(
        projectProperties.getMediamtxApiUser(),
        projectProperties.getMediamtxApiPass(),
        projectProperties.getMediamtxIp(),
        projectProperties.getMediamtxApiPort(),
        orgWebcam.getId().toString());
    return BaseResult.success();
  }
}
