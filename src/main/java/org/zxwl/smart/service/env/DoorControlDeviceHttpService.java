package org.zxwl.smart.service.env;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.DoorControlDeviceHttpUploadDataParams;
import org.zxwl.smart.domain.request.env.DoorControlDeviceInitParams;

@Validated
public interface DoorControlDeviceHttpService {

  String initOptions(DoorControlDeviceInitParams params);

  String uploadData(DoorControlDeviceHttpUploadDataParams params, HttpServletRequest request);

  String getRequest(String deviceNo);

  String ping(String deviceNo);

  String deviceCommand(String deviceNo, HttpServletRequest request);
}
