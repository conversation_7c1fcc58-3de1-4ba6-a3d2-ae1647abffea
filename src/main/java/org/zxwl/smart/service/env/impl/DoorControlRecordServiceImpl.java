package org.zxwl.smart.service.env.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.common.convert.env.EnvDataConvert;
import org.zxwl.smart.common.utils.ResponseUtil;
import org.zxwl.smart.domain.request.env.DoorControlRecordCountRequest;
import org.zxwl.smart.domain.request.env.DoorControlRecordDeleteRequest;
import org.zxwl.smart.domain.request.env.DoorControlRecordListRequest;
import org.zxwl.smart.domain.request.env.DoorControlSingleRecordCountRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.DoorControlRecordCountVO;
import org.zxwl.smart.domain.response.env.DoorControlRecordExcelVO;
import org.zxwl.smart.domain.response.env.DoorControlRecordVO;
import org.zxwl.smart.mybatis.entity.device.DoorControlRecord;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.device.DoorControlRecordMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.env.DoorControlRecordService;

@Service
@AllArgsConstructor
public class DoorControlRecordServiceImpl implements DoorControlRecordService {

  private final DoorControlRecordMapper doorControlRecordMapper;
  private final WarehouseMapper warehouseMapper;

  @Override
  public PageResult<DoorControlRecordVO> getDoorControlRecordList(
      DoorControlRecordListRequest request) {
    Long warehouseId = request.getWarehouseId();
    List<String> deviceNoList = request.getDeviceNoList();
    List<Date> createdAtRange = request.getCreatedAtRange();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    Date createdAtBegin = null;
    Date createdAtEnd = null;

    if (Objects.nonNull(createdAtRange)) {
      createdAtBegin = createdAtRange.get(0);
      createdAtEnd = createdAtRange.get(1);
    }

    PageHelper.startPage(pageNum, pageSize);
    List<DoorControlRecord> recordList =
        doorControlRecordMapper.selectList(warehouseId, deviceNoList, createdAtBegin, createdAtEnd);
    List<DoorControlRecordVO> voList = getDoorControlRecordVOList(recordList);
    return new PageResult<>(voList, recordList);
  }

  @Override
  public void getDoorControlRecordExcel(
      DoorControlRecordListRequest request, HttpServletResponse response) {
    Long warehouseId = request.getWarehouseId();
    List<String> deviceNoList = request.getDeviceNoList();
    List<Date> createdAtRange = request.getCreatedAtRange();
    Date createdAtBegin = null;
    Date createdAtEnd = null;

    if (Objects.nonNull(createdAtRange)) {
      createdAtBegin = createdAtRange.get(0);
      createdAtEnd = createdAtRange.get(1);
    }

    if (Objects.nonNull(deviceNoList) && deviceNoList.isEmpty()) {
      return;
    }
    List<DoorControlRecord> recordList =
        doorControlRecordMapper.selectList(warehouseId, deviceNoList, createdAtBegin, createdAtEnd);
    List<DoorControlRecordExcelVO> voList = getDoorControlRecordExcelVOList(recordList);
    ResponseUtil.exportExcel(response, voList, DoorControlRecordExcelVO.class, "门禁日志记录表");
  }

  @Override
  public SimpleResult<DoorControlRecordCountVO> getDoorControlRecordCount(
      DoorControlRecordCountRequest request) {
    Long warehouseId = request.getWarehouseId();
    if (Objects.isNull(warehouseId)) {
      return new SimpleResult<>("库房id不能为空");
    }
    // 获取今日开始时间和结束时间
    Date todayBegin = DateUtil.beginOfDay(new Date());
    Date todayEnd = DateUtil.endOfDay(new Date());
    // 获取总记录数
    Long totalCount = doorControlRecordMapper.selectCount(warehouseId, null, null);
    // 获取今日记录数
    Long todayCount = doorControlRecordMapper.selectCount(warehouseId, todayBegin, todayEnd);
    DoorControlRecordCountVO vo = new DoorControlRecordCountVO();
    vo.setTotalCount(totalCount);
    vo.setTodayCount(todayCount);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<DoorControlRecordCountVO> getSingleDoorControlRecordCount(
      DoorControlSingleRecordCountRequest request) {
    String deviceNo = request.getDeviceNo();
    if (Objects.isNull(deviceNo)) {
      return new SimpleResult<>("设备编号不能为空");
    }
    // 获取今日开始时间和结束时间
    Date todayBegin = DateUtil.beginOfDay(new Date());
    Date todayEnd = DateUtil.endOfDay(new Date());
    // 获取总记录数
    Long totalCount = doorControlRecordMapper.selectCountByDeviceNo(deviceNo, null, null);
    // 获取今日记录数
    Long todayCount = doorControlRecordMapper.selectCountByDeviceNo(deviceNo, todayBegin, todayEnd);
    DoorControlRecordCountVO vo = new DoorControlRecordCountVO();
    vo.setTotalCount(totalCount);
    vo.setTodayCount(todayCount);
    return new SimpleResult<>(vo);
  }

  private List<DoorControlRecordExcelVO> getDoorControlRecordExcelVOList(
      List<DoorControlRecord> recordList) {
    if (Objects.isNull(recordList)) {
      return new ArrayList<>();
    }
    List<DoorControlRecordExcelVO> voList =
        EnvDataConvert.getDoorControlRecordExcelVOList(recordList);
    List<Long> warehouseIdList =
        recordList.stream().map(DoorControlRecord::getWarehouseId).distinct().toList();
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    for (DoorControlRecordExcelVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    return voList;
  }

  private List<DoorControlRecordVO> getDoorControlRecordVOList(List<DoorControlRecord> recordList) {
    if (CollUtil.isEmpty(recordList)) {
      return new ArrayList<>();
    }
    List<DoorControlRecordVO> voList = EnvDataConvert.getDoorControlRecordVOList(recordList);
    // 完善库房信息
    List<Long> warehouseIdList =
        recordList.stream().map(DoorControlRecord::getWarehouseId).distinct().toList();
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    for (DoorControlRecordVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    return voList;
  }

  @Override
  public BaseResult deleteDoorControlRecord(DoorControlRecordDeleteRequest request) {
    List<Long> idList = request.getIdList();
    if (CollUtil.isEmpty(idList)) {
      return BaseResult.failed("记录ID列表不能为空");
    }
    doorControlRecordMapper.deleteByIdList(idList);
    return BaseResult.success();
  }
}
