package org.zxwl.smart.service.env;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.EASArchiveAlarmVO;
import org.zxwl.smart.domain.response.env.EASGetAlarmRecordVO;
import org.zxwl.smart.domain.response.env.EASPersonNumVO;

@Validated
public interface EASDeviceService {

  SimpleResult<EASArchiveAlarmVO> getArchiveAlarm(@NotNull @Valid EASArchiveAlarmRequest request);

  BaseResult addPeopleNum(@NotNull @Valid EASAddPeopleNumRequest request);

  SimpleResult<EASPersonNumVO> getPeopleNum(@NotNull @Valid EASGetPeopleNumRequest request);

  PageResult<EASGetAlarmRecordVO> getAlarmRecordList(@NotNull @Valid EASGetAlarmRecordRequest request);

  BaseResult deleteAlarmRecord(@NotNull @Valid EASDeleteAlarmRecordRequest request);
}
