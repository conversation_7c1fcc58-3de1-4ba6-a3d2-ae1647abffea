package org.zxwl.smart.service.env;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.DoorControlUserCopyResultVO;
import org.zxwl.smart.domain.response.env.DoorControlUserImportResultVO;
import org.zxwl.smart.domain.response.env.DoorControlUserVO;

@Validated
public interface DoorControlUserService {

  PageResult<DoorControlUserVO> getDoorControlUserList(
      @NotNull @Valid DoorControlUserListRequest request);

  SimpleResult<DoorControlUserImportResultVO> importDoorControlUser(
      MultipartFile multipartFile, String deviceNo);

  SimpleResult<DoorControlUserCopyResultVO> copyDoorControlUser(
      @NotNull @Valid DoorControlUserCopyRequest request);

  BaseResult createDoorControlUser(@NotNull @Valid DoorControlUserCreateRequest request);

  BaseResult updateDoorControlUser(@NotNull @Valid DoorControlUserUpdateRequest request);

  BaseResult deleteDoorControlUser(@NotNull @Valid DoorControlUserDeleteRequest request);
}
