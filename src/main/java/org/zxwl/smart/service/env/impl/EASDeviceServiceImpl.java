package org.zxwl.smart.service.env.impl;

import java.util.ArrayList;
import java.util.List;

import com.github.pagehelper.PageHelper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.common.convert.env.EASDeviceConvert;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.EASArchiveAlarmVO;
import org.zxwl.smart.domain.response.env.EASGetAlarmRecordVO;
import org.zxwl.smart.domain.response.env.EASPersonNumVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.device.EASDevice;
import org.zxwl.smart.mybatis.entity.device.EasDeviceAlarmRecord;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.device.EASDeviceMapper;
import org.zxwl.smart.mybatis.mapper.device.EasDeviceAlarmRecordMapper;
import org.zxwl.smart.service.env.EASDeviceService;

@Service
@AllArgsConstructor
public class EASDeviceServiceImpl implements EASDeviceService {

  private final EasDeviceAlarmRecordMapper easDeviceAlarmRecordMapper;
  private final ArchiveMapper archiveMapper;
  private final EASDeviceMapper easDeviceMapper;

  @Override
  public SimpleResult<EASArchiveAlarmVO> getArchiveAlarm(EASArchiveAlarmRequest request) {
    String tid = request.getTid();
    String deviceIp = request.getDeviceIp();
    Archive archive = archiveMapper.selectByIdOrTid(null, tid, false);
    EASArchiveAlarmVO vo = new EASArchiveAlarmVO();
    if (archive != null) {
      vo.setArchiveId(archive.getId());
      vo.setArchiveName(archive.getArchiveName());
      vo.setArchiveNo(archive.getArchiveNo());
      vo.setAlarm(true);
      vo.setAlarmMessage("防盗门报警");
      EasDeviceAlarmRecord easDeviceAlarmRecord =
          EASDeviceConvert.getEASDeviceAlarmRecord(archive, deviceIp);
      EASDevice easDevice = easDeviceMapper.selectByDeviceIp(deviceIp);
      easDeviceAlarmRecord.setWarehouseId(archive.getWarehouseId());
      easDeviceAlarmRecord.setDeviceId(easDevice.getId());
      easDeviceAlarmRecord.setDeviceName(easDevice.getDeviceName());
      easDeviceAlarmRecord.setDeviceNo(easDevice.getDeviceNo());
      easDeviceAlarmRecordMapper.insert(easDeviceAlarmRecord);
      return new SimpleResult<>(vo);
    }
    vo.setAlarm(false);
    return new SimpleResult<>(vo);
  }

  @Override
  public BaseResult addPeopleNum(EASAddPeopleNumRequest request) {
    return BaseResult.success();
  }

  @Override
  public SimpleResult<EASPersonNumVO> getPeopleNum(EASGetPeopleNumRequest request) {
    return new SimpleResult<>(new EASPersonNumVO());
  }

  @Override
  public PageResult<EASGetAlarmRecordVO> getAlarmRecordList(EASGetAlarmRecordRequest request) {
    String archiveNameLike = request.getArchiveNameLike();
    String archiveNoLike = request.getArchiveNoLike();
    String deviceNameLike = request.getDeviceNameLike();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<EasDeviceAlarmRecord> recordList =
        easDeviceAlarmRecordMapper.selectList(archiveNameLike, archiveNoLike, deviceNameLike);
    List<EASGetAlarmRecordVO> voList = new ArrayList<>();
    if (recordList == null || recordList.isEmpty()) {
      return new PageResult<>("报警记录为空");
    }
    for (EasDeviceAlarmRecord record : recordList) {
      EASGetAlarmRecordVO vo = EASDeviceConvert.getEASGetAlarmRecordVO(record);
      voList.add(vo);
    }
    return new PageResult<>(voList, recordList);
  }

  @Override
  public BaseResult deleteAlarmRecord(EASDeleteAlarmRecordRequest request) {
    Long id = request.getId();
    int result = easDeviceAlarmRecordMapper.deleteById(id);
    if (result > 0) {
      return BaseResult.success();
    }
    return BaseResult.failed("删除失败");
  }
}
