package org.zxwl.smart.service.env.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.cache.model.DoorControlDeviceCacheModel;
import org.zxwl.smart.cache.model.EnvDeviceCacheModel;
import org.zxwl.smart.client.hk.ConnectionManager;
import org.zxwl.smart.client.hk.enums.*;
import org.zxwl.smart.client.hk.model.HKResponse;
import org.zxwl.smart.common.convert.env.EnvDeviceConvert;
import org.zxwl.smart.constant.consist.DeviceConstant;
import org.zxwl.smart.constant.enums.env.EnvDeviceGroupEnum;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.EnvDeviceTypeVO;
import org.zxwl.smart.domain.response.env.EnvDeviceVO;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;
import org.zxwl.smart.mybatis.entity.device.EnvDeviceLog;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.device.EnvDeviceLogMapper;
import org.zxwl.smart.mybatis.mapper.device.EnvDeviceMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.AreaDeviceMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.AreaMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.env.EnvDeviceService;

@Slf4j
@Service
@AllArgsConstructor
public class EnvDeviceServiceImpl implements EnvDeviceService {

  private final EnvDeviceMapper envDeviceMapper;
  private final WarehouseMapper warehouseMapper;
  private final EnvDataCache envDataCache;
  private final ConnectionManager connectionManager;
  private final EnvDeviceLogMapper envDeviceLogMapper;
  private final AreaMapper areaMapper;
  private final AreaDeviceMapper areaDeviceMapper;

  @Override
  public ListResult<EnvDeviceTypeVO> getDeviceTypeList() {
    List<EnvDeviceTypeVO> voList =
        Arrays.stream(EnvDeviceTypeEnum.values())
            .filter(e -> !e.isHidden())
            .map(
                e ->
                    new EnvDeviceTypeVO(
                        e.getValue(), EnvDeviceGroupEnum.getValuesByCode(e.getDeviceGroup())))
            .collect(Collectors.toList());
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<EnvDeviceVO> getEnvDevice(EnvDeviceRequest request) {
    Long id = request.getId();
    EnvDevice device = envDeviceMapper.selectById(id);
    if (Objects.isNull(device)) {
      return new SimpleResult<>("设备不存在");
    }
    // 库房名称字段补充
    Long warehouseId = device.getWarehouseId();
    EnvDeviceVO vo = EnvDeviceConvert.getEnvDeviceVO(device);
    EnvDeviceTypeEnum deviceTypeEnum = EnvDeviceTypeEnum.getByValue(device.getDeviceType());
    if (Objects.nonNull(warehouseId)) {
      Warehouse warehouse = warehouseMapper.selectById(warehouseId);
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    if (Objects.nonNull(deviceTypeEnum)) {
      vo.setDeviceGroupList(EnvDeviceGroupEnum.getValuesByCode(deviceTypeEnum.getDeviceGroup()));
      if (EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(device.getDeviceType())) {
        if (processDoorControlDevice(device, vo)) {
          return new SimpleResult<>(vo);
        }
      }
      processEnvDevice(device, deviceTypeEnum, vo);
    }
    return new SimpleResult<>(vo);
  }

  @Override
  public ListResult<EnvDeviceVO> getEnvDeviceList(EnvDeviceListRequest request) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    String deviceType = request.getDeviceType();
    String deviceNo = request.getDeviceNo();
    String deviceName = request.getDeviceName();
    List<Long> idList = null;
    if (Objects.nonNull(areaId)) {
      idList = areaDeviceMapper.selectEnvDeviceIdListByAreaId(areaId);
    }
    List<EnvDevice> list =
        envDeviceMapper.selectList(warehouseId, idList, deviceType, deviceNo, deviceName);
    List<EnvDeviceVO> voList = getEnvDeviceVOList(list);
    return new ListResult<>(voList);
  }

  @Override
  public BaseResult createEnvDevice(EnvDeviceCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long warehouseId = request.getWarehouseId();
    String deviceType = request.getDeviceType();
    String deviceNo = request.getDeviceNo();
    String deviceIp = request.getDeviceIp();
    Byte deviceSerialPort = request.getDeviceSerialPort();
    boolean linkedDataHub = false;
    Byte deviceSerialAddr = null;
    if (warehouseMapper.existById(warehouseId) == 0) {
      return new SimpleResult<>("库房不存在");
    }
    if (Objects.nonNull(deviceNo) && envDeviceMapper.existByDeviceNo(deviceNo) > 0) {
      return new SimpleResult<>("设备编号已存在");
    }
    if (!EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(deviceType)) {
      if (Objects.isNull(deviceSerialPort)) {
        return new SimpleResult<>("设备串口不能为空");
      }
      // 默认环控设备都走数据汇集箱
      linkedDataHub = true;
      // 数据汇集箱R485端口上的设备串口地址默认都为1
      deviceSerialAddr = deviceSerialPort < 9 ? (byte) 1 : null;
      if (Objects.nonNull(envDataCache.getDevice(deviceIp, deviceSerialPort, deviceSerialAddr))) {
        return new SimpleResult<>("IP为 " + deviceIp + " 串口为 " + deviceSerialPort + " 位置上已存在其他设备");
      }
      // 检查数据汇集箱上的端口是否符合要求
      BaseResult baseResult = checkDeviceSerialPort(deviceType, deviceSerialPort);
      if (Objects.nonNull(baseResult)) {
        return baseResult;
      }
    }
    // 创建设备
    EnvDevice device = EnvDeviceConvert.getEnvDevice(request);
    device.setLinkedDataHub(linkedDataHub);
    device.setDeviceSerialAddr(deviceSerialAddr);
    device.setCreatedId(userId);
    device.setUpdatedId(userId);
    envDeviceMapper.insert(device);
    // 更新缓存
    updateDeviceCache(device, null, null, null);
    return BaseResult.success();
  }

  @Override
  public BaseResult updateEnvDevice(EnvDeviceUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    String deviceIp = request.getDeviceIp();
    String deviceNo = request.getDeviceNo();
    Byte deviceSerialPort = request.getDeviceSerialPort();
    Byte deviceSerialAddr = null;
    EnvDevice orgDevice = envDeviceMapper.selectById(id);
    if (Objects.isNull(orgDevice)) {
      return BaseResult.failed("设备不存在");
    }
    if (Objects.nonNull(deviceNo) && !StrUtil.equals(orgDevice.getDeviceNo(), deviceNo)) {
      if (envDeviceMapper.existByDeviceNo(deviceNo) > 0) {
        return new SimpleResult<>("设备编号已存在");
      }
    }
    String deviceType = orgDevice.getDeviceType();
    if (!EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(deviceType)) {
      if (Objects.isNull(deviceSerialPort)) {
        return new SimpleResult<>("设备串口不能为空");
      }
      // 数据汇集箱R485端口上的设备串口地址默认都为1
      deviceSerialAddr = deviceSerialPort < 9 ? (byte) 1 : null;
      EnvDeviceCacheModel orgCacheModel =
          envDataCache.getDevice(deviceIp, deviceSerialPort, deviceSerialAddr);
      if (Objects.nonNull(orgCacheModel) && !Objects.equals(orgCacheModel.getId(), id)) {
        return BaseResult.failed("IP为 " + deviceIp + " 串口为 " + deviceSerialPort + " 位置上已存在其他设备");
      }
    }
    // 检查数据汇集箱上的端口是否符合要求
    Boolean linkedDataHub = orgDevice.getLinkedDataHub();
    if (linkedDataHub) {
      BaseResult baseResult = checkDeviceSerialPort(orgDevice.getDeviceType(), deviceSerialPort);
      if (Objects.nonNull(baseResult)) {
        return baseResult;
      }
    }
    // 更新设备
    EnvDevice device = EnvDeviceConvert.getEnvDevice(request);
    device.setLinkedDataHub(linkedDataHub);
    device.setDeviceSerialAddr(deviceSerialAddr);
    device.setUpdatedId(userId);
    envDeviceMapper.updateById(device);
    // 更新缓存
    updateDeviceCache(
        device,
        orgDevice.getDeviceIp(),
        orgDevice.getDeviceSerialPort(),
        orgDevice.getDeviceSerialAddr());
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteEnvDevice(EnvDeviceDeleteRequest request) {
    Long id = request.getId();
    EnvDevice orgDevice = envDeviceMapper.selectById(id);
    if (Objects.isNull(orgDevice)) {
      return BaseResult.failed("设备不存在");
    }
    envDeviceMapper.deleteById(id, System.currentTimeMillis());
    // 删除相关缓存
    String deviceType = orgDevice.getDeviceType();
    String deviceNo = orgDevice.getDeviceNo();
    String deviceIp = orgDevice.getDeviceIp();
    Byte deviceSerialPort = orgDevice.getDeviceSerialPort();
    Byte deviceSerialAddr = orgDevice.getDeviceSerialAddr();
    if (EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(deviceType)) {
      envDataCache.removeDevice(deviceNo);
    } else {
      envDataCache.removeDevice(deviceIp, deviceSerialPort, deviceSerialAddr);
      if (envDataCache.countDevice(deviceIp) <= 1) {
        // 删除数据汇集箱
        envDataCache.removeDevice(deviceIp, null, null);
        connectionManager.disconnect(deviceIp);
      }
    }
    return BaseResult.success();
  }

  @Override
  public BaseResult controlEnvDevice(EnvDeviceControlRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    String controlFieldName = request.getControlFieldName();
    EnvDevice orgDevice = envDeviceMapper.selectById(id);
    // 判断
    if (Objects.isNull(orgDevice)) {
      return BaseResult.failed("设备不存在");
    }
    String deviceType = orgDevice.getDeviceType();
    EnvDeviceTypeEnum deviceTypeEnum = EnvDeviceTypeEnum.getByValue(deviceType);
    if (!EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(deviceType)) {
      EnvControlFieldEnum controlFieldEnum = null;
      Class<? extends EnvControlFieldEnum> controlFieldEnumClass = null;
      if (deviceTypeEnum != null) {
        controlFieldEnumClass = deviceTypeEnum.getControlFieldEnumClass();
      }
      if (Objects.nonNull(controlFieldEnumClass)) {
        for (EnvControlFieldEnum fieldEnum : controlFieldEnumClass.getEnumConstants()) {
          if (fieldEnum.equalsName(controlFieldName)) {
            controlFieldEnum = fieldEnum;
          }
        }
      }
      if (Objects.isNull(controlFieldEnum)) {
        return BaseResult.failed("控制字段名不存在");
      }
    }
    // 控制设备
    String deviceNo = orgDevice.getDeviceNo();
    String deviceIp = orgDevice.getDeviceIp();
    Byte deviceSerialPort = orgDevice.getDeviceSerialPort();
    Byte deviceSerialAddr = orgDevice.getDeviceSerialAddr();
    BaseResult result = BaseResult.success();
    if (EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(deviceType)) {
      // 处理门禁设备开门
      if (DoorControlDeviceControlFieldEnum.OPEN_DOOR.equalsName(controlFieldName)) {
        envDataCache.addCommand(deviceNo, "C:" + System.currentTimeMillis() + ":AC_UNLOCK");
      }
    } else {
      // 控制环控设备
      HKResponse hkResponse =
          connectionManager.sendCommand(
              deviceIp, deviceSerialPort, deviceSerialAddr, controlFieldName, null);
      if (hkResponse.isSuccess()) {
        result = BaseResult.failed(hkResponse.getMessage());
      }
    }
    // 记录操作日志
    EnvDeviceLog deviceLog = EnvDeviceConvert.getEnvDeviceLog(orgDevice);
    deviceLog.setOperateContext(controlFieldName);
    deviceLog.setCreatedId(userId);
    envDeviceLogMapper.insert(deviceLog);
    return result;
  }

  @Override
  public BaseResult controlEnvDeviceList(EnvDeviceBatchControlRequest request) {
    Long userId = AccountLocal.getUserId();
    List<Long> idList = request.getIdList();
    String controlFieldName = request.getControlFieldName();
    List<EnvDevice> deviceList = envDeviceMapper.selectListByIdList(idList, false);
    for (EnvDevice device : deviceList) {
      String deviceType = device.getDeviceType();
      String deviceNo = device.getDeviceNo();
      Byte deviceSerialPort = device.getDeviceSerialPort();
      Byte deviceSerialAddr = device.getDeviceSerialAddr();
      if (EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(deviceType)) {
        envDataCache.addCommand(deviceNo, "C:" + System.currentTimeMillis() + ":AC_UNLOCK");
      } else {
        String deviceIp = device.getDeviceIp();
        connectionManager.sendCommand(
            deviceIp, deviceSerialPort, deviceSerialAddr, controlFieldName, null);
      }
    }
    // 记录操作日志
    List<EnvDeviceLog> deviceLogList = new ArrayList<>();
    for (EnvDevice device : deviceList) {
      EnvDeviceLog deviceLog = EnvDeviceConvert.getEnvDeviceLog(device);
      deviceLog.setOperateContext(controlFieldName);
      deviceLog.setCreatedId(userId);
      deviceLogList.add(deviceLog);
    }
    envDeviceLogMapper.insertList(deviceLogList);
    return BaseResult.success();
  }

  private BaseResult checkDeviceSerialPort(String deviceType, Byte deviceSerialPort) {
    if (EnvDeviceTypeEnum.LSTCQ.equalsValue(deviceType)
        || EnvDeviceTypeEnum.HWTCQ.equalsValue(deviceType)
        || EnvDeviceTypeEnum.YGTCQ.equalsValue(deviceType)
        || EnvDeviceTypeEnum.WGTCQ.equalsValue(deviceType)) {
      byte beginPort = DeviceConstant.DATA_HUB_IN_IO_SERIAL_BEGIN_PORT;
      byte endPort = DeviceConstant.DATA_HUB_IN_IO_SERIAL_END_PORT;
      if (!(deviceSerialPort >= beginPort && deviceSerialPort <= endPort)) {
        return BaseResult.failed("该类型设备需要接在数据汇集箱的输入口上，串口区间为 [" + beginPort + ", " + endPort + "]");
      }
    } else if (EnvDeviceTypeEnum.BJQ.equalsValue(deviceType)) {
      byte beginPort = DeviceConstant.DATA_HUB_IN_RELAY_BEGIN_PORT;
      byte endPort = DeviceConstant.DATA_HUB_IN_RELAY_END_PORT;
      if (!(deviceSerialPort >= beginPort && deviceSerialPort <= endPort)) {
        return BaseResult.failed(
            "该类型设备需要接在数据汇集箱的继电器输出口上，串口区间为 [" + beginPort + ", " + endPort + "]");
      }
    } else {
      byte beginPort = DeviceConstant.DATA_HUB_R485_BEGIN_PORT;
      byte endPort = DeviceConstant.DATA_HUB_R485_END_PORT;
      if (!(deviceSerialPort >= beginPort && deviceSerialPort <= endPort)) {
        return BaseResult.failed(
            "该类型设备需要接在数据汇集箱的R485端口上，串口区间为 [" + beginPort + ", " + endPort + "]");
      }
    }
    return null;
  }

  private List<EnvDeviceVO> getEnvDeviceVOList(List<EnvDevice> list) {
    if (CollUtil.isEmpty(list)) {
      return new ArrayList<>();
    }
    List<Long> warehouseIdList =
        list.stream().map(EnvDevice::getWarehouseId).filter(Objects::nonNull).distinct().toList();
    Map<Long, String> warehouseNameMap = new HashMap<>();
    if (CollUtil.isNotEmpty(warehouseIdList)) {
      List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, false);
      warehouseNameMap =
          warehouseList.stream()
              .collect(
                  Collectors.toMap(Warehouse::getId, Warehouse::getWarehouseName, (v1, v2) -> v1));
    }
    List<EnvDeviceVO> voList = new ArrayList<>();
    for (EnvDevice device : list) {
      EnvDeviceTypeEnum deviceTypeEnum = EnvDeviceTypeEnum.getByValue(device.getDeviceType());
      EnvDeviceVO vo = EnvDeviceConvert.getEnvDeviceVO(device);
      Long warehouseId = device.getWarehouseId();
      if (warehouseId != null) {
        vo.setWarehouseName(warehouseNameMap.get(warehouseId));
      }
      // 追加缓存数据
      if (Objects.nonNull(deviceTypeEnum)) {
        vo.setDeviceGroupList(EnvDeviceGroupEnum.getValuesByCode(deviceTypeEnum.getDeviceGroup()));
        // 处理门禁设备
        if (EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(device.getDeviceType())) {
          if (processDoorControlDevice(device, vo)) {
            voList.add(vo);
            continue;
          }
        }
        // 处理环控设备
        processEnvDevice(device, deviceTypeEnum, vo);
      }
      voList.add(vo);
    }
    return voList;
  }

  private void updateDeviceCache(
      EnvDevice device, String oldDeviceIp, Byte oldDeviceSerialPort, Byte oldDeviceSerialAddr) {
    String deviceNo = device.getDeviceNo();
    if (EnvDeviceTypeEnum.DOOR_CONTROL.equalsValue(device.getDeviceType())) {
      DoorControlDeviceCacheModel doorControlDeviceCacheModel =
          EnvDeviceConvert.getDoorControlDeviceCacheModel(device);
      envDataCache.putDevice(doorControlDeviceCacheModel);
      envDataCache.getCommandQueue(deviceNo);
    } else {
      // 删除旧的
      envDataCache.removeDevice(oldDeviceIp, oldDeviceSerialPort, oldDeviceSerialAddr);
      // 添加新的
      EnvDeviceCacheModel cacheModel = EnvDeviceConvert.getEnvDeviceCacheModel(device);
      envDataCache.putDevice(cacheModel);
      // 检查是否要更新数据汇集箱缓存
      if (device.getLinkedDataHub()) {
        updateDataHubCache(device.getDeviceIp(), oldDeviceIp);
      }
    }
  }

  private void updateDataHubCache(String newDeviceIp, String oldDeviceIp) {
    if (Objects.nonNull(oldDeviceIp) && envDataCache.countDevice(oldDeviceIp) <= 1) {
      envDataCache.removeDevice(oldDeviceIp, null, null);
    }

    EnvDeviceCacheModel dataHubCacheModel = envDataCache.getDevice(newDeviceIp, null, null);
    if (Objects.isNull(dataHubCacheModel)) {
      dataHubCacheModel = new EnvDeviceCacheModel();
    }
    dataHubCacheModel.setDeviceType(EnvDeviceTypeEnum.DATA_HUB.getValue());
    dataHubCacheModel.setDeviceIp(newDeviceIp);
    envDataCache.putDevice(dataHubCacheModel);
  }

  private boolean processDoorControlDevice(EnvDevice device, EnvDeviceVO vo) {
    String deviceNo = device.getDeviceNo();
    if (StrUtil.isNotBlank(deviceNo)) {
      DoorControlDeviceCacheModel doorControlDeviceCacheModel = envDataCache.getDevice(deviceNo);
      if (Objects.nonNull(doorControlDeviceCacheModel)) {
        vo.setOnline(doorControlDeviceCacheModel.isOnline());
        // 设置门禁设备控制字段列表
        List<EnvDeviceVO.ControlFieldVO> controlFieldList = new ArrayList<>();
        controlFieldList.add(
            new EnvDeviceVO.ControlFieldVO(DoorControlDeviceControlFieldEnum.OPEN_DOOR.getName()));
        vo.setDeviceControlFieldList(controlFieldList);
        return true;
      }
    }
    return false;
  }

  private void processEnvDevice(
      EnvDevice device, EnvDeviceTypeEnum deviceTypeEnum, EnvDeviceVO vo) {
    String deviceIp = device.getDeviceIp();
    Byte deviceSerialPort = device.getDeviceSerialPort();
    Byte deviceSerialAddr = device.getDeviceSerialAddr();
    EnvDeviceCacheModel cacheModel =
        envDataCache.getDevice(deviceIp, deviceSerialPort, deviceSerialAddr);
    if (Objects.isNull(cacheModel)) {
      return;
    }
    vo.setOnline(cacheModel.isOnline());
    // 设置设备字段列表
    setDeviceFields(deviceTypeEnum, cacheModel, vo);
    // 设置设备控制字段列表
    setDeviceControlFields(deviceTypeEnum, vo);
  }

  private void setDeviceFields(
      EnvDeviceTypeEnum deviceTypeEnum, EnvDeviceCacheModel cacheModel, EnvDeviceVO vo) {
    Map<String, Object> deviceCacheFieldValuesMap = cacheModel.getFieldValuesMap();
    List<EnvDeviceVO.FieldVO> fieldList = new ArrayList<>();
    Class<? extends EnvFieldEnum> fieldEnumClass = deviceTypeEnum.getFieldEnumClass();
    if (Objects.nonNull(fieldEnumClass)) {
      for (EnvFieldEnum fieldEnum : fieldEnumClass.getEnumConstants()) {
        String name = fieldEnum.getName();
        Object value = deviceCacheFieldValuesMap.get(name);
        String displayValue = null;
        Class<? extends EnvFieldValueMapperEnum> mapperEnumClass =
            fieldEnum.getValueMapperEnumClass();
        if (Objects.nonNull(mapperEnumClass)) {
          for (EnvFieldValueMapperEnum mapperEnum : mapperEnumClass.getEnumConstants()) {
            if (mapperEnum.equalValue(value)) {
              displayValue = mapperEnum.getDisplayValue();
              break;
            }
          }
        }
        String unit = fieldEnum.getUnit();
        fieldList.add(
            new EnvDeviceVO.FieldVO(name, StrUtil.toStringOrNull(value), displayValue, unit));
      }
    }
    vo.setDeviceFieldList(fieldList);
  }

  private void setDeviceControlFields(EnvDeviceTypeEnum deviceTypeEnum, EnvDeviceVO vo) {
    List<EnvDeviceVO.ControlFieldVO> controlFieldList = new ArrayList<>();
    Class<? extends EnvControlFieldEnum> controlFieldEnumClass =
        deviceTypeEnum.getControlFieldEnumClass();
    if (Objects.nonNull(controlFieldEnumClass)) {
      for (EnvControlFieldEnum fieldEnum : controlFieldEnumClass.getEnumConstants()) {
        if (!fieldEnum.isHidden()) {
          controlFieldList.add(new EnvDeviceVO.ControlFieldVO(fieldEnum.getName()));
        }
      }
    }
    vo.setDeviceControlFieldList(controlFieldList);
  }
}
