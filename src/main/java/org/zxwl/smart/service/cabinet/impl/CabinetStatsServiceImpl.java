package org.zxwl.smart.service.cabinet.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.constant.enums.archive.ArchiveOperatorTypeEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.domain.request.cabinet.CabinetOperatorStatRequest;
import org.zxwl.smart.domain.request.cabinet.CabinetStatRequest;
import org.zxwl.smart.domain.request.cabinet.HrCabinetOperatorStatRequest;
import org.zxwl.smart.domain.request.cabinet.HrCabinetStatRequest;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.cabinet.CabinetOperatorStatVO;
import org.zxwl.smart.domain.response.cabinet.CabinetStatVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetOperatorStatVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetStatVO;
import org.zxwl.smart.mybatis.ext.archive.ArchiveShelvingStatusStat;
import org.zxwl.smart.mybatis.ext.archive.ArchiveStatExt;
import org.zxwl.smart.mybatis.ext.archive.OperatorStat;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveOperatorRecordMapper;
import org.zxwl.smart.service.cabinet.CabinetStatsService;

@Service
@AllArgsConstructor
public class CabinetStatsServiceImpl implements CabinetStatsService {

  private final ArchiveMapper archiveMapper;
  private final ArchiveOperatorRecordMapper archiveOperatorRecordMapper;

  @Override
  public SimpleResult<CabinetStatVO> getCabinetStat(CabinetStatRequest request) {
    Integer groupNo = request.getGroupNo();
    // 查询在架和不在架数量
    List<ArchiveShelvingStatusStat> archiveStatList =
        archiveMapper.selectShelvingStatusStat(null, null, null, groupNo);
    Map<String, ArchiveShelvingStatusStat> archiveStatMap =
        archiveStatList.stream()
            .collect(Collectors.toMap(ArchiveShelvingStatusStat::getShelvingStatus, v -> v));
    ArchiveShelvingStatusStat archiveInShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.IN.getValue());
    ArchiveShelvingStatusStat archiveOutShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.OUT.getValue());
    ArchiveShelvingStatusStat archivePendingOutShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    // 查询累计上/下架
    List<OperatorStat> operatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(null, null, null, groupNo, null, null);
    Map<String, OperatorStat> operatorStatMap =
        operatorStatList.stream().collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat operatorShelve = operatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat operatorUnshelve =
        operatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    // 查询今日上/下架
    List<OperatorStat> todayOperatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(
            null, null, null, groupNo, DateUtil.beginOfDay(new Date()), null);
    Map<String, OperatorStat> todayOperatorStatMap =
        todayOperatorStatList.stream()
            .collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat todayOperatorShelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat todayOperatorUnshelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    CabinetStatVO vo = new CabinetStatVO();
    vo.setInShelvingCount(Objects.nonNull(archiveInShelving) ? archiveInShelving.getCount() : 0);
    vo.setPendingOutShelvingCount(
        Objects.nonNull(archivePendingOutShelving) ? archivePendingOutShelving.getCount() : 0);
    vo.setOutShelvingCount(Objects.nonNull(archiveOutShelving) ? archiveOutShelving.getCount() : 0);
    vo.setTotalShelveCount(Objects.nonNull(operatorShelve) ? operatorShelve.getCount() : 0);
    vo.setTotalUnshelveCount(Objects.nonNull(operatorUnshelve) ? operatorUnshelve.getCount() : 0);
    vo.setTodayShelveCount(
        Objects.nonNull(todayOperatorShelve) ? todayOperatorShelve.getCount() : 0);
    vo.setTodayUnshelveCount(
        Objects.nonNull(todayOperatorUnshelve) ? todayOperatorUnshelve.getCount() : 0);
    return new SimpleResult<>(vo);
  }

  @Override
  public ListResult<CabinetOperatorStatVO> getCabinetOperatorStatList(
      CabinetOperatorStatRequest request) {
    Integer groupNo = request.getGroupNo();
    DateTime now = DateUtil.date();
    int year = DateUtil.year(now);
    List<CabinetOperatorStatVO> voList = new ArrayList<>();
    for (int month = 1; month <= 12; month++) {
      DateTime begin = DateUtil.beginOfMonth(DateUtil.parseDate(year + "-" + month + "-01"));
      Date end = DateUtil.endOfMonth(begin);
      OperatorStat shelveStat = null;
      OperatorStat unshelveStat = null;
      if (now.after(begin)) {
        List<OperatorStat> operatorStatList =
            archiveOperatorRecordMapper.selectStatByOperatorType(
                null, null, null, groupNo, begin, end);
        Map<String, OperatorStat> operatorStatMap =
            operatorStatList.stream()
                .collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
        shelveStat = operatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
        unshelveStat = operatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
      }
      CabinetOperatorStatVO vo = new CabinetOperatorStatVO();
      vo.setMonth(month);
      vo.setShelveCount(Objects.nonNull(shelveStat) ? shelveStat.getCount() : 0);
      vo.setUnshelveCount(Objects.nonNull(unshelveStat) ? unshelveStat.getCount() : 0);
      voList.add(vo);
    }
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<HrCabinetStatVO> getHrCabinetStat(HrCabinetStatRequest request) {
    Integer groupNo = request.getGroupNo();
    // 查询在架和不在架数量
    ArchiveStatExt archiveStatExt = archiveMapper.selectStat(null, groupNo, null);
    // 查询累计上/下架
    List<OperatorStat> operatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(null, null, groupNo, null, null, null);
    Map<String, OperatorStat> operatorStatMap =
        operatorStatList.stream().collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat operatorShelve = operatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat operatorUnshelve =
        operatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    // 查询今日上/下架
    List<OperatorStat> todayOperatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(
            null, null, groupNo, null, DateUtil.beginOfDay(new Date()), null);
    Map<String, OperatorStat> todayOperatorStatMap =
        todayOperatorStatList.stream()
            .collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat todayOperatorShelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat todayOperatorUnshelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    HrCabinetStatVO vo = new HrCabinetStatVO();
    vo.setTotalCount(archiveStatExt.getTotalCount());
    vo.setBindTidCount(archiveStatExt.getBindTidCount());
    vo.setUnbindTidCount(archiveStatExt.getUnbindTidCount());
    vo.setInShelvingCount(archiveStatExt.getInShelvingCount());
    vo.setShelvingPendingInCount(archiveStatExt.getShelvingPendingInCount());
    vo.setShelvingPendingOutCount(archiveStatExt.getShelvingPendingOutCount());
    vo.setOutShelvingCount(archiveStatExt.getOutShelvingCount());
    vo.setTotalShelveCount(Objects.nonNull(operatorShelve) ? operatorShelve.getCount() : 0);
    vo.setTotalUnshelveCount(Objects.nonNull(operatorUnshelve) ? operatorUnshelve.getCount() : 0);
    vo.setTodayShelveCount(
        Objects.nonNull(todayOperatorShelve) ? todayOperatorShelve.getCount() : 0);
    vo.setTodayUnshelveCount(
        Objects.nonNull(todayOperatorUnshelve) ? todayOperatorUnshelve.getCount() : 0);
    return new SimpleResult<>(vo);
  }

  @Override
  public ListResult<HrCabinetOperatorStatVO> getHrCabinetOperatorStatList(
      HrCabinetOperatorStatRequest request) {
    Integer groupNo = request.getGroupNo();
    DateTime now = DateUtil.date();
    int year = DateUtil.year(now);
    List<HrCabinetOperatorStatVO> voList = new ArrayList<>();
    for (int month = 1; month <= 12; month++) {
      DateTime begin = DateUtil.beginOfMonth(DateUtil.parseDate(year + "-" + month + "-01"));
      Date end = DateUtil.endOfMonth(begin);
      OperatorStat shelveStat = null;
      OperatorStat unshelveStat = null;
      if (now.after(begin)) {
        List<OperatorStat> operatorStatList =
            archiveOperatorRecordMapper.selectStatByOperatorType(
                null, null, groupNo, null, begin, end);
        Map<String, OperatorStat> operatorStatMap =
            operatorStatList.stream()
                .collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
        shelveStat = operatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
        unshelveStat = operatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
      }
      HrCabinetOperatorStatVO vo = new HrCabinetOperatorStatVO();
      vo.setMonth(month);
      vo.setShelveCount(Objects.nonNull(shelveStat) ? shelveStat.getCount() : 0);
      vo.setUnshelveCount(Objects.nonNull(unshelveStat) ? unshelveStat.getCount() : 0);
      voList.add(vo);
    }
    return new ListResult<>(voList);
  }
}
