package org.zxwl.smart.service.cabinet.impl;

import org.springframework.stereotype.Service;
import org.zxwl.smart.domain.request.cabinet.ErrorRecordsRequest;
import org.zxwl.smart.domain.request.cabinet.InventoryRecordsRequest;
import org.zxwl.smart.domain.request.cabinet.OperationRecordsRequest;
import org.zxwl.smart.domain.request.cabinet.OrganizeRecordsRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.cabinet.ErrorRecordsVO;
import org.zxwl.smart.domain.response.cabinet.InventoryRecordsVO;
import org.zxwl.smart.domain.response.cabinet.OperationRecordsVO;
import org.zxwl.smart.domain.response.cabinet.OrganizeRecordsVO;
import org.zxwl.smart.service.cabinet.CabinetRecordService;

@Service
public class CabinetRecordServiceImpl implements CabinetRecordService {

  @Override
  public PageResult<ErrorRecordsVO> getCabinetErrorRecords(ErrorRecordsRequest request) {
    return new PageResult<>(null, null);
  }

  @Override
  public PageResult<InventoryRecordsVO> getCabinetInventoryRecords(InventoryRecordsRequest request) {
    return new PageResult<>(null, null);
  }

  @Override
  public PageResult<OrganizeRecordsVO> getCabinetOrganizeRecords(OrganizeRecordsRequest request) {
    return new PageResult<>(null, null);
  }

  @Override
  public PageResult<OperationRecordsVO> getCabinetOperationRecords(
      OperationRecordsRequest request) {
    return new PageResult<>(null, null);
  }
}
