package org.zxwl.smart.service.cabinet.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.cabinet.CabinetConvert;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.utils.DebounceUtil;
import org.zxwl.smart.constant.consist.GlobalConstant;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.domain.request.cabinet.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupListVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupSelectVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.cabinet.*;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGridMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetLayerMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.cabinet.HrCabinetService;

@Slf4j
@Service
@AllArgsConstructor
public class HrCabinetServiceImpl implements HrCabinetService {

  private final HrCabinetGroupMapper hrCabinetGroupMapper;
  private final HrCabinetLayerMapper hrCabinetLayerMapper;
  private final HrCabinetGridMapper hrCabinetGridMapper;
  private final WarehouseMapper warehouseMapper;
  private final ArchiveMapper archiveMapper;
  private final ScheduledExecutorService storageCapacityUpdateExecutor;
  private final TransactionTemplate transactionTemplate;

  @Override
  public void updateHrCabinetUsedCapacityAsync(Integer hrCabinetGroupNo) {
    String key = "updateHrCabinetUsedCapacity_" + hrCabinetGroupNo;
    DebounceUtil.debounce(
        key,
        () -> updateHrCabinetUsedCapacityInternal(hrCabinetGroupNo),
        GlobalConstant.STORAGE_CAPACITY_UPDATE_DEBOUNCE_DELAY_MS,
        TimeUnit.MILLISECONDS,
        storageCapacityUpdateExecutor);
  }

  @Override
  public PageResult<HrCabinetGroupListVO> getHrCabinetGroupList(HrCabinetGroupListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<HrCabinetGroup> list = hrCabinetGroupMapper.selectList(organizationId, warehouseId);
    List<HrCabinetGroupListVO> voList = CabinetConvert.getHrCabinetGroupList(list);
    // 库房名字完善
    List<Long> warehouseIdList =
        voList.stream()
            .map(HrCabinetGroupListVO::getWarehouseId)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, Warehouse> warehouseMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(warehouseIdList)) {
      List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, false);
      warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    }
    for (HrCabinetGroupListVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseNo(warehouse.getWarehouseNo());
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    return new PageResult<>(voList, list);
  }

  @Override
  public ListResult<HrCabinetGroupSelectVO> getHrCabinetGroupListForSelect(
      HrCabinetGroupSelectRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    List<HrCabinetGroup> cabinetGroupList =
        hrCabinetGroupMapper.selectList(organizationId, warehouseId);
    List<Long> cabinetGroupIdList =
        cabinetGroupList.stream().map(HrCabinetGroup::getId).collect(Collectors.toList());
    List<HrCabinetLayer> cabinetLayerList = Collections.emptyList();
    if (CollUtil.isNotEmpty(cabinetGroupIdList)) {
      cabinetLayerList = hrCabinetLayerMapper.selectByCabinetGroupIdList(cabinetGroupIdList);
    }
    List<Long> cabinetLayerIdList =
        cabinetLayerList.stream().map(HrCabinetLayer::getId).collect(Collectors.toList());
    List<HrCabinetGrid> cabinetGridList = Collections.emptyList();
    if (CollUtil.isNotEmpty(cabinetGroupIdList)) {
      cabinetGridList = hrCabinetGridMapper.selectListByCabinetLayerIdList(cabinetLayerIdList);
    }
    HashMap<Long, Map<Integer, List<HrCabinetLayer>>> cabinetGroupMap =
        cabinetLayerList.stream()
            .collect(
                Collectors.groupingBy(
                    HrCabinetLayer::getCabinetGroupId,
                    HashMap::new,
                    Collectors.groupingBy(HrCabinetLayer::getCabinetNo)));
    Map<Long, List<HrCabinetGrid>> cabinetGridMap =
        cabinetGridList.stream().collect(Collectors.groupingBy(HrCabinetGrid::getCabinetLayerId));
    List<HrCabinetGroupSelectVO> voList = new ArrayList<>();
    for (HrCabinetGroup cabinetGroup : cabinetGroupList) {
      List<HrCabinetGroupSelectVO.CabinetVO> cabinetVOList = new ArrayList<>();
      Map<Integer, List<HrCabinetLayer>> cabinetMap = cabinetGroupMap.get(cabinetGroup.getId());
      for (Integer cabinetNo : cabinetMap.keySet()) {
        List<HrCabinetGroupSelectVO.LayerVO> cabinetLayerVOList = new ArrayList<>();
        List<HrCabinetLayer> pairCabinetLayerList = cabinetMap.get(cabinetNo);
        for (HrCabinetLayer cabinetLayer : pairCabinetLayerList) {
          Integer layerNo = cabinetLayer.getLayerNo();
          List<HrCabinetGroupSelectVO.GridVO> cabinetGridVOList = new ArrayList<>();
          List<HrCabinetGrid> pairCabinetGridList = cabinetGridMap.get(cabinetLayer.getId());
          for (HrCabinetGrid cabinetGrid : pairCabinetGridList) {
            HrCabinetGroupSelectVO.GridVO cabinetGridVO =
                new HrCabinetGroupSelectVO.GridVO(cabinetGrid.getGridNo());
            cabinetGridVOList.add(cabinetGridVO);
          }
          HrCabinetGroupSelectVO.LayerVO cabinetLayerVO =
              new HrCabinetGroupSelectVO.LayerVO(layerNo, cabinetGridVOList);
          cabinetLayerVOList.add(cabinetLayerVO);
        }
        HrCabinetGroupSelectVO.CabinetVO cabinetVO =
            new HrCabinetGroupSelectVO.CabinetVO(cabinetNo, cabinetLayerVOList);
        cabinetVOList.add(cabinetVO);
      }
      Integer groupNo = cabinetGroup.getGroupNo();
      HrCabinetGroupSelectVO vo = new HrCabinetGroupSelectVO(groupNo, cabinetVOList);
      voList.add(vo);
    }
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<HrCabinetGroupVO> getHrCabinetGroup(HrCabinetGroupGetRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    HrCabinetGroup cabinetGroup;
    if (Objects.nonNull(id)) {
      cabinetGroup = hrCabinetGroupMapper.selectById(id);
    } else if (Objects.nonNull(groupNo)) {
      cabinetGroup = hrCabinetGroupMapper.selectByGroupNo(groupNo);
    } else {
      return new SimpleResult<>("请求参数错误");
    }
    if (Objects.isNull(cabinetGroup)) {
      return new SimpleResult<>("柜组不存在");
    }
    if (Objects.nonNull(organizationId)
        && !Objects.equals(cabinetGroup.getOrganizationId(), organizationId)) {
      return new SimpleResult<>("该柜组不属于当前机构");
    }
    id = cabinetGroup.getId();
    groupNo = cabinetGroup.getGroupNo();
    List<HrCabinetLayer> cabinetLayerList = hrCabinetLayerMapper.selectListByCabinetGroupId(id);
    List<HrCabinetGrid> cabinetGridList = hrCabinetGridMapper.selectListByCabinetGroupId(id);
    List<String> shelvingStatusList =
        ListUtil.toList(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<Archive> archiveList =
        archiveMapper.selectListByHrCabinetGroupNo(shelvingStatusList, groupNo);
    Map<String, Archive> archiveMap =
        archiveList.stream()
            .collect(
                Collectors.toMap(
                    a ->
                        +a.getHrCabinetNo()
                            + "_"
                            + a.getHrCabinetLayerNo()
                            + "_"
                            + a.getHrCabinetGridNo(),
                    v -> v,
                    (v, v2) -> v));
    Map<Integer, List<HrCabinetLayer>> cabinetMap =
        cabinetLayerList.stream().collect(Collectors.groupingBy(HrCabinetLayer::getCabinetNo));
    Map<Long, List<HrCabinetGrid>> cabinetGridMap =
        cabinetGridList.stream().collect(Collectors.groupingBy(HrCabinetGrid::getCabinetLayerId));

    List<HrCabinetGroupVO.CabinetVO> cabinetVOList = new ArrayList<>();
    for (Integer cabinetNo : cabinetMap.keySet()) {
      List<HrCabinetGroupVO.LayerVO> cabinetLayerVOList = new ArrayList<>();
      List<HrCabinetLayer> pairCabinetLayerList = cabinetMap.get(cabinetNo);
      for (HrCabinetLayer cabinetLayer : pairCabinetLayerList) {
        Integer layerNo = cabinetLayer.getLayerNo();
        List<HrCabinetGroupVO.GridVO> cabinetGridVOList = new ArrayList<>();
        List<HrCabinetGrid> pairCabinetGridList = cabinetGridMap.get(cabinetLayer.getId());
        for (HrCabinetGrid cabinetGrid : pairCabinetGridList) {
          Integer gridNo = cabinetGrid.getGridNo();
          Archive archive = archiveMap.get(cabinetNo + "_" + layerNo + "_" + gridNo);
          HrCabinetGroupVO.GridVO cabinetGridVO = new HrCabinetGroupVO.GridVO();
          cabinetGridVO.setGridNo(gridNo);
          cabinetGridVO.setCapacity(cabinetGrid.getCapacity());
          cabinetGridVO.setUsedCapacity(cabinetGrid.getUsedCapacity());
          if (Objects.nonNull(archive)) {
            cabinetGridVO.setArchiveId(archive.getId());
            cabinetGridVO.setShelvingStatus(archive.getShelvingStatus());
            cabinetGridVO.setArchiveName(archive.getArchiveName());
            cabinetGridVO.setArchiveNo(archive.getArchiveNo());
            cabinetGridVO.setTid(archive.getTid());
          }
          cabinetGridVOList.add(cabinetGridVO);
        }
        HrCabinetGroupVO.LayerVO cabinetLayerVO = CabinetConvert.getHrCabinetLayerVO(cabinetLayer);
        cabinetLayerVO.setCabinetGridList(cabinetGridVOList);
        cabinetLayerVOList.add(cabinetLayerVO);
      }
      HrCabinetGroupVO.CabinetVO cabinetVO =
          new HrCabinetGroupVO.CabinetVO(cabinetNo, cabinetLayerVOList);
      cabinetVOList.add(cabinetVO);
    }
    Long warehouseId = cabinetGroup.getWarehouseId();
    Warehouse warehouse = warehouseMapper.selectById(warehouseId);
    HrCabinetGroupVO cabinetGroupVO = CabinetConvert.getHrCabinetGroupVO(cabinetGroup);
    if (Objects.nonNull(warehouse)) {
      cabinetGroupVO.setWarehouseNo(warehouse.getWarehouseNo());
      cabinetGroupVO.setWarehouseName(warehouse.getWarehouseName());
    }
    cabinetGroupVO.setCabinetList(cabinetVOList);
    return new SimpleResult<>(cabinetGroupVO);
  }

  @Override
  public SimpleResult<BaseCreateVO> createHrCabinetGroup(HrCabinetGroupCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    Integer cabinetNum = request.getCabinetNum();
    Integer layerNum = request.getLayerNum();
    Integer layerGridNum = request.getLayerGridNum();
    if (warehouseMapper.existById(warehouseId) == 0) {
      return new SimpleResult<>("库房不存在");
    }
    // 新建柜层和格口
    List<Pair<HrCabinetLayer, List<HrCabinetGrid>>> pairList = new ArrayList<>();
    for (int cabinetNo = 1; cabinetNo <= cabinetNum; cabinetNo++) {
      for (Integer layerNo = 1; layerNo <= layerNum; layerNo++) {
        // 新建格口
        List<HrCabinetGrid> cabinetGridList = new ArrayList<>();
        for (int griaNo = 1; griaNo <= layerGridNum; griaNo++) {
          HrCabinetGrid cabinetGrid = new HrCabinetGrid();
          cabinetGrid.setCapacity(1);
          cabinetGrid.setUsedCapacity(0);
          cabinetGrid.setGridNo(griaNo);
          cabinetGrid.setCreatedId(userId);
          cabinetGrid.setUpdatedId(userId);
          cabinetGridList.add(cabinetGrid);
        }
        // 新建柜层
        HrCabinetLayer cabinetLayer = new HrCabinetLayer();
        cabinetLayer.setCabinetNo(cabinetNo);
        cabinetLayer.setLayerNo(layerNo);
        cabinetLayer.setCabinetGridNum(layerGridNum);
        cabinetLayer.setCapacity(layerGridNum);
        cabinetLayer.setUsedCapacity(0);
        cabinetLayer.setCreatedId(userId);
        cabinetLayer.setUpdatedId(userId);
        pairList.add(new Pair<>(cabinetLayer, cabinetGridList));
      }
    }
    // 新建柜组
    int groupNo = hrCabinetGroupMapper.maxGroupNo();
    HrCabinetGroup cabinetGroup = CabinetConvert.getHrCabinetGroup(request);
    cabinetGroup.setOrganizationId(organizationId);
    cabinetGroup.setGroupNo(groupNo + 1);
    cabinetGroup.setCapacity(
        pairList.stream()
            .map(Pair::getLeft)
            .map(HrCabinetLayer::getCapacity)
            .reduce(0, Integer::sum));
    cabinetGroup.setUsedCapacity(0);
    cabinetGroup.setCreatedId(userId);
    cabinetGroup.setUpdatedId(userId);
    transactionTemplate.execute(
        status -> {
          // 柜组插入
          hrCabinetGroupMapper.insert(cabinetGroup);
          // 柜子插入
          Long groupId = cabinetGroup.getId();
          List<HrCabinetLayer> cabinetLayerList =
              pairList.stream()
                  .map(Pair::getLeft)
                  .peek(cabinet -> cabinet.setCabinetGroupId(groupId))
                  .collect(Collectors.toList());
          hrCabinetLayerMapper.insertList(cabinetLayerList);
          // 柜口插入
          List<HrCabinetGrid> cabinetGridList =
              pairList.stream()
                  .flatMap(
                      pair -> {
                        HrCabinetLayer cabinetLayer = pair.getLeft();
                        Long cabinetLayerId = cabinetLayer.getId();
                        return pair.getRight().stream()
                            .peek(
                                cabinetGrid -> {
                                  cabinetGrid.setCabinetGroupId(groupId);
                                  cabinetGrid.setCabinetLayerId(cabinetLayerId);
                                });
                      })
                  .collect(Collectors.toList());
          hrCabinetGridMapper.insertList(cabinetGridList);
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(cabinetGroup.getId()));
  }

  @Override
  public BaseResult updateHrCabinetGroup(HrCabinetGroupUpdateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    HrCabinetGroup orgCabinetGroup = hrCabinetGroupMapper.selectById(id);
    if (Objects.isNull(orgCabinetGroup)) {
      return BaseResult.failed("柜组不存在");
    }
    if (!Objects.equals(orgCabinetGroup.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该柜组不属于当前机构");
    }
    if (!groupNo.equals(orgCabinetGroup.getGroupNo())) {
      if (hrCabinetGroupMapper.existByGroupNo(groupNo) > 0) {
        return BaseResult.failed("柜组编号已存在");
      }
    }
    HrCabinetGroup cabinetGroup = CabinetConvert.getHrCabinetGroup(request);
    cabinetGroup.setUpdatedId(userId);
    hrCabinetGroupMapper.updateById(cabinetGroup);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteHrCabinetGroup(HrCabinetGroupDeleteRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    HrCabinetGroup orgCabinetGroup = hrCabinetGroupMapper.selectById(id);
    if (Objects.isNull(orgCabinetGroup)) {
      return BaseResult.failed("柜组不存在");
    }
    if (!Objects.equals(orgCabinetGroup.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该柜组不属于当前机构");
    }
    // todo: 检查柜子是否关联有上架档案
    long deleteAt = System.currentTimeMillis();
    hrCabinetGroupMapper.deleteById(id, deleteAt);
    hrCabinetLayerMapper.deleteByCabinetGroupId(id);
    hrCabinetGridMapper.deleteByCabinetGroupId(id);
    return BaseResult.success();
  }

  @Override
  public BaseResult controlHrCabinetGroup(HrCabinetGroupControlRequest request) {
    // todo: 待实现
    return BaseResult.success();
  }

  /** 人事柜容量更新的内部实现 */
  public void updateHrCabinetUsedCapacityInternal(Integer hrCabinetGroupNo) {
    List<String> shelvingStatusList =
        List.of(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<HrCabinetGroup> hrCabinetGroupList =
        hrCabinetGroupMapper.selectListForStatTask(hrCabinetGroupNo);
    hrCabinetGroupList.forEach(hrCabinetGroup -> hrCabinetGroup.setUsedCapacity(0));
    for (HrCabinetGroup hrCabinetGroup : hrCabinetGroupList) {
      Long id = hrCabinetGroup.getId();
      Integer groupNo = hrCabinetGroup.getGroupNo();
      List<HrCabinetLayer> hrCabinetLayerList =
          hrCabinetLayerMapper.selectListByCabinetGroupIdForStatTask(id);
      hrCabinetLayerList.forEach(hrCabinetLayer -> hrCabinetLayer.setUsedCapacity(0));
      Map<String, HrCabinetLayer> hrCabinetLayerMap =
          hrCabinetLayerList.stream()
              .collect(
                  Collectors.toMap(
                      l -> l.getCabinetNo() + "_" + l.getLayerNo(), l -> l, (l, l2) -> l));
      List<HrCabinetGrid> hrCabinetGridList =
          hrCabinetGridMapper.selectListByCabinetGroupIdForStatTask(id);
      hrCabinetGridList.forEach(hrCabinetGrid -> hrCabinetGrid.setUsedCapacity(0));
      Map<String, HrCabinetGrid> hrCabinetGridMap =
          hrCabinetGridList.stream()
              .collect(
                  Collectors.toMap(
                      g -> g.getCabinetLayerId() + "_" + g.getGridNo(), r -> r, (r, r2) -> r));
      List<Archive> archiveList =
          archiveMapper.selectListByGroupNoForStatTask(shelvingStatusList, null, groupNo, null);
      // 统计每个位置上的档案数量
      for (Archive archive : archiveList) {
        // 累加组已用容量
        hrCabinetGroup.setUsedCapacity(hrCabinetGroup.getUsedCapacity() + 1);
        // 累加层位已用容量
        HrCabinetLayer hrCabinetLayer =
            hrCabinetLayerMap.get(archive.getHrCabinetNo() + "_" + archive.getHrCabinetLayerNo());
        if (Objects.isNull(hrCabinetLayer)) {
          continue;
        }
        hrCabinetLayer.setUsedCapacity(hrCabinetLayer.getUsedCapacity() + 1);
        // 累加格口已用容量
        HrCabinetGrid hrCabinetGrid =
            hrCabinetGridMap.get(hrCabinetLayer.getId() + "_" + archive.getHrCabinetGridNo());
        if (Objects.isNull(hrCabinetGrid)) {
          continue;
        }
        hrCabinetGrid.setUsedCapacity(hrCabinetGrid.getUsedCapacity() + 1);
      }
      // 更新数据库
      if (CollUtil.isNotEmpty(hrCabinetLayerList)) {
        hrCabinetLayerMapper.updateUsedCapacityForStatTask(hrCabinetLayerList);
      }
      if (CollUtil.isNotEmpty(hrCabinetGridList)) {
        // 分批更新，避免数据量过大导致性能问题
        for (int i = 0; i < hrCabinetGridList.size(); i += GlobalConstant.BATCH_SQL_MAX_SIZE) {
          int endIndex = Math.min(i + GlobalConstant.BATCH_SQL_MAX_SIZE, hrCabinetGridList.size());
          List<HrCabinetGrid> batch = hrCabinetGridList.subList(i, endIndex);
          hrCabinetGridMapper.updateUsedCapacityForStatTask(batch);
        }
      }
    }
    if (CollUtil.isNotEmpty(hrCabinetGroupList)) {
      hrCabinetGroupMapper.updateUsedCapacityForStatTask(hrCabinetGroupList);
    }
    log.info(
        "人事柜容量更新完成，组编号：{}",
        Arrays.toString(hrCabinetGroupList.stream().map(HrCabinetGroup::getGroupNo).toArray()));
  }
}
