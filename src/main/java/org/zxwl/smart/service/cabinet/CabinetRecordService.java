package org.zxwl.smart.service.cabinet;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.cabinet.ErrorRecordsRequest;
import org.zxwl.smart.domain.request.cabinet.InventoryRecordsRequest;
import org.zxwl.smart.domain.request.cabinet.OperationRecordsRequest;
import org.zxwl.smart.domain.request.cabinet.OrganizeRecordsRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.cabinet.ErrorRecordsVO;
import org.zxwl.smart.domain.response.cabinet.InventoryRecordsVO;
import org.zxwl.smart.domain.response.cabinet.OperationRecordsVO;
import org.zxwl.smart.domain.response.cabinet.OrganizeRecordsVO;

@Validated
public interface CabinetRecordService {

  PageResult<ErrorRecordsVO> getCabinetErrorRecords(@NotNull @Valid ErrorRecordsRequest request);

  PageResult<InventoryRecordsVO> getCabinetInventoryRecords(
      @NotNull @Valid InventoryRecordsRequest request);

  PageResult<OrganizeRecordsVO> getCabinetOrganizeRecords(
      @NotNull @Valid OrganizeRecordsRequest request);

  PageResult<OperationRecordsVO> getCabinetOperationRecords(
      @NotNull @Valid OperationRecordsRequest request);
}
