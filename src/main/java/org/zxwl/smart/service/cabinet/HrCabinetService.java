package org.zxwl.smart.service.cabinet;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.cabinet.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupListVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupSelectVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupVO;

@Validated
public interface HrCabinetService {

  /**
   * 异步执行人事柜容量更新，带防抖功能
   *
   * @param hrCabinetGroupNo 人事柜组编号
   */
  void updateHrCabinetUsedCapacityAsync(Integer hrCabinetGroupNo);

  PageResult<HrCabinetGroupListVO> getHrCabinetGroupList(
      @NotNull @Valid HrCabinetGroupListRequest request);

  ListResult<HrCabinetGroupSelectVO> getHrCabinetGroupListForSelect(
      @NotNull @Valid HrCabinetGroupSelectRequest request);

  SimpleResult<HrCabinetGroupVO> getHrCabinetGroup(
      @NotNull @Valid HrCabinetGroupGetRequest request);

  SimpleResult<BaseCreateVO> createHrCabinetGroup(
      @NotNull @Valid HrCabinetGroupCreateRequest request);

  BaseResult updateHrCabinetGroup(@NotNull @Valid HrCabinetGroupUpdateRequest request);

  BaseResult deleteHrCabinetGroup(@NotNull @Valid HrCabinetGroupDeleteRequest request);

  BaseResult controlHrCabinetGroup(@NotNull @Valid HrCabinetGroupControlRequest request);
}
