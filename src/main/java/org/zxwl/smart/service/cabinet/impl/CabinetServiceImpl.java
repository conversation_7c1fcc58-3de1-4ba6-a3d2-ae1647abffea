package org.zxwl.smart.service.cabinet.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.cabinet.CabinetConvert;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.utils.DebounceUtil;
import org.zxwl.smart.constant.consist.GlobalConstant;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.constant.enums.cabinet.CabinetGridStatus;
import org.zxwl.smart.domain.request.cabinet.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.cabinet.CabinetGroupListVO;
import org.zxwl.smart.domain.response.cabinet.CabinetGroupSelectVO;
import org.zxwl.smart.domain.response.cabinet.CabinetGroupVO;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.cabinet.Cabinet;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGrid;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGroup;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGridMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.cabinet.CabinetService;

@Slf4j
@Service
@AllArgsConstructor
public class CabinetServiceImpl implements CabinetService {

  private final CabinetGroupMapper cabinetGroupMapper;
  private final CabinetMapper cabinetMapper;
  private final CabinetGridMapper cabinetGridMapper;
  private final WarehouseMapper warehouseMapper;
  private final ArchiveMapper archiveMapper;
  private final ScheduledExecutorService storageCapacityUpdateExecutor;
  private final TransactionTemplate transactionTemplate;

  @Override
  public void updateCabinetUsedCapacityAsync(Integer cabinetGroupNo) {
    String key = "updateCabinetUsedCapacity_" + cabinetGroupNo;
    DebounceUtil.debounce(
        key,
        () -> updateCabinetUsedCapacityInternal(cabinetGroupNo),
        GlobalConstant.STORAGE_CAPACITY_UPDATE_DEBOUNCE_DELAY_MS,
        TimeUnit.MILLISECONDS,
        storageCapacityUpdateExecutor);
  }

  @Override
  public PageResult<CabinetGroupListVO> getCabinetGroupList(CabinetGroupListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<CabinetGroup> list = cabinetGroupMapper.selectList(organizationId, warehouseId);
    List<CabinetGroupListVO> voList = CabinetConvert.getCabinetGroupListVOList(list);
    // 库房名字完善
    List<Long> warehouseIdList =
        voList.stream()
            .map(CabinetGroupListVO::getWarehouseId)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, Warehouse> warehouseMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(warehouseIdList)) {
      List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, false);
      warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    }
    for (CabinetGroupListVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseNo(warehouse.getWarehouseNo());
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    return new PageResult<>(voList, list);
  }

  @Override
  public ListResult<CabinetGroupSelectVO> getCabinetGroupListForSelect(
      CabinetGroupSelectRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    List<CabinetGroup> cabinetGroupList =
        cabinetGroupMapper.selectList(organizationId, warehouseId);
    List<Long> cabinetGroupIdList =
        cabinetGroupList.stream().map(CabinetGroup::getId).collect(Collectors.toList());
    List<Cabinet> cabinetList = Collections.emptyList();
    if (CollUtil.isNotEmpty(cabinetGroupIdList)) {
      cabinetList = cabinetMapper.selectByCabinetGroupIdList(cabinetGroupIdList);
    }
    List<Long> cabinetIdList =
        cabinetList.stream().map(Cabinet::getId).collect(Collectors.toList());
    List<CabinetGrid> cabinetGridList = Collections.emptyList();
    if (CollUtil.isNotEmpty(cabinetGroupIdList)) {
      cabinetGridList = cabinetGridMapper.selectListByCabinetIdList(cabinetIdList);
    }
    Map<Long, List<Cabinet>> cabinetMap =
        cabinetList.stream().collect(Collectors.groupingBy(Cabinet::getCabinetGroupId));
    Map<Long, List<CabinetGrid>> cabinetGridMap =
        cabinetGridList.stream().collect(Collectors.groupingBy(CabinetGrid::getCabinetId));
    List<CabinetGroupSelectVO> voList = new ArrayList<>();
    for (CabinetGroup cabinetGroup : cabinetGroupList) {
      List<CabinetGroupSelectVO.CabinetVO> cabinetVOList = new ArrayList<>();
      List<Cabinet> pairCabinetList = cabinetMap.get(cabinetGroup.getId());
      for (Cabinet cabinet : pairCabinetList) {
        Integer cabinetNo = cabinet.getCabinetNo();
        List<CabinetGroupSelectVO.GridVO> cabinetGridVOList = new ArrayList<>();
        List<CabinetGrid> pairCabinetGridList = cabinetGridMap.get(cabinet.getId());
        for (CabinetGrid cabinetGrid : pairCabinetGridList) {
          Integer gridNo = cabinetGrid.getGridNo();
          CabinetGroupSelectVO.GridVO cabinetGridVO = new CabinetGroupSelectVO.GridVO(gridNo);
          cabinetGridVO.setGridNo(gridNo);
          cabinetGridVOList.add(cabinetGridVO);
        }
        CabinetGroupSelectVO.CabinetVO cabinetVO = new CabinetGroupSelectVO.CabinetVO();
        cabinetVO.setCabinetNo(cabinetNo);
        cabinetVO.setMainCabinet(cabinet.getMainCabinet());
        cabinetVO.setCabinetGridList(cabinetGridVOList);
        cabinetVOList.add(cabinetVO);
      }
      CabinetGroupSelectVO cabinetGroupVO = new CabinetGroupSelectVO();
      cabinetGroupVO.setGroupNo(cabinetGroup.getGroupNo());
      cabinetGroupVO.setCabinetList(cabinetVOList);
      voList.add(cabinetGroupVO);
    }
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<CabinetGroupVO> getCabinetGroup(CabinetGroupGetRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    CabinetGroup cabinetGroup;
    if (Objects.nonNull(id)) {
      cabinetGroup = cabinetGroupMapper.selectById(id);
    } else if (Objects.nonNull(groupNo)) {
      cabinetGroup = cabinetGroupMapper.selectByGroupNo(groupNo);
    } else {
      return new SimpleResult<>("请求参数错误");
    }
    if (Objects.isNull(cabinetGroup)) {
      return new SimpleResult<>("柜组不存在");
    }
    if (Objects.nonNull(organizationId)
        && !Objects.equals(cabinetGroup.getOrganizationId(), organizationId)) {
      return new SimpleResult<>("该柜组不属于当前机构");
    }
    id = cabinetGroup.getId();
    groupNo = cabinetGroup.getGroupNo();
    List<Cabinet> cabinetList = cabinetMapper.selectListByCabinetGroupId(id);
    List<CabinetGrid> cabinetGridList = cabinetGridMapper.selectListByCabinetGroupId(id);
    List<String> shelvingStatusList =
        ListUtil.toList(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<Archive> archiveList =
        archiveMapper.selectListByCabinetGroupNo(shelvingStatusList, groupNo);
    Map<String, List<Archive>> archiveMap =
        archiveList.stream()
            .collect(Collectors.groupingBy(a -> a.getCabinetNo() + "_" + a.getCabinetGridNo()));
    Map<Long, List<CabinetGrid>> cabinetGridMap =
        cabinetGridList.stream().collect(Collectors.groupingBy(CabinetGrid::getCabinetId));
    List<CabinetGroupVO.CabinetVO> cabinetVOList = new ArrayList<>();
    for (Cabinet cabinet : cabinetList) {
      Integer cabinetNo = cabinet.getCabinetNo();
      List<CabinetGrid> pairCabinetGridList = cabinetGridMap.get(cabinet.getId());
      List<CabinetGroupVO.GridVO> cabinetGridVOList = new ArrayList<>();
      for (CabinetGrid cabinetGrid : pairCabinetGridList) {
        Integer gridNo = cabinetGrid.getGridNo();
        List<Archive> pairArchiveList = archiveMap.get(cabinetNo + "_" + gridNo);
        CabinetGroupVO.GridVO cabinetGridVO = CabinetConvert.getCabinetGridVO(cabinetGrid);
        List<CabinetGroupVO.ArchiveVO> archiveVOList =
            CabinetConvert.getArchiveVOList(pairArchiveList);
        cabinetGridVO.setArchiveList(archiveVOList);
        cabinetGridVOList.add(cabinetGridVO);
      }
      CabinetGroupVO.CabinetVO cabinetVO = new CabinetGroupVO.CabinetVO();
      cabinetVO.setId(cabinet.getId());
      cabinetVO.setCabinetNo(cabinetNo);
      cabinetVO.setMainCabinet(cabinet.getMainCabinet());
      cabinetVO.setCabinetGridNum(cabinet.getCabinetGridNum());
      cabinetVO.setCapacity(cabinet.getCapacity());
      cabinetVO.setUsedCapacity(cabinet.getUsedCapacity());
      cabinetVO.setCabinetGridList(cabinetGridVOList);
      cabinetVOList.add(cabinetVO);
    }
    Long warehouseId = cabinetGroup.getWarehouseId();
    Warehouse warehouse = warehouseMapper.selectById(warehouseId);
    CabinetGroupVO cabinetGroupVO = CabinetConvert.getCabinetGroupVO(cabinetGroup);
    if (Objects.nonNull(warehouse)) {
      cabinetGroupVO.setWarehouseNo(warehouse.getWarehouseNo());
      cabinetGroupVO.setWarehouseName(warehouse.getWarehouseName());
    }
    cabinetGroupVO.setCabinetList(cabinetVOList);
    return new SimpleResult<>(cabinetGroupVO);
  }

  @Override
  public SimpleResult<BaseCreateVO> createCabinetGroup(CabinetGroupCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    Integer mainCabinetNo = request.getMainCabinetNo();
    Integer subCabinetCount = request.getSubCabinetCount();
    Integer mainCabinetGridCount = request.getMainCabinetGridCount();
    Integer subCabinetGridCount = request.getSubCabinetGridCount();
    Integer cabinetGridCapacity = request.getCabinetGridCapacity();
    if (warehouseMapper.existById(warehouseId) == 0) {
      return new SimpleResult<>("库房不存在");
    }
    if (mainCabinetNo > (subCabinetCount + 1)) {
      return new SimpleResult<>("主柜编号不能大于柜体数量");
    }
    if (subCabinetCount > 0 && subCabinetGridCount == 0) {
      return new SimpleResult<>("副柜数量不能为0或空");
    }
    // 新建柜体和格口
    List<Pair<Cabinet, List<CabinetGrid>>> pairList = new ArrayList<>();
    for (int i = 1; i <= subCabinetCount + 1; i++) {
      boolean isMainCabinet = i == mainCabinetNo;
      int cabinetGridNum = isMainCabinet ? mainCabinetGridCount : subCabinetGridCount;
      // 新建格口
      List<CabinetGrid> cabinetGridList = new ArrayList<>();
      for (int j = 1; j <= cabinetGridNum; j++) {
        CabinetGrid cabinetGrid = new CabinetGrid();
        cabinetGrid.setCapacity(cabinetGridCapacity);
        cabinetGrid.setUsedCapacity(0);
        cabinetGrid.setGridStatus(CabinetGridStatus.NORMAL.getValue());
        cabinetGrid.setGridNo(j);
        cabinetGrid.setCreatedId(userId);
        cabinetGrid.setUpdatedId(userId);
        cabinetGridList.add(cabinetGrid);
      }
      // 新建柜体
      Cabinet cabinet = new Cabinet();
      cabinet.setCabinetNo(i);
      cabinet.setMainCabinet(isMainCabinet);
      cabinet.setCabinetGridNum(cabinetGridNum);
      cabinet.setCapacity(cabinetGridNum * cabinetGridCapacity);
      cabinet.setUsedCapacity(0);
      cabinet.setCreatedId(userId);
      cabinet.setUpdatedId(userId);
      pairList.add(new Pair<>(cabinet, cabinetGridList));
    }
    // 新建柜组
    int groupNo = cabinetGroupMapper.maxGroupNo();
    CabinetGroup cabinetGroup = CabinetConvert.getCabinetGroup(request);
    cabinetGroup.setOrganizationId(organizationId);
    cabinetGroup.setGroupNo(groupNo + 1);
    cabinetGroup.setMainCabinetNo(mainCabinetNo);
    cabinetGroup.setCapacity(
        pairList.stream().map(Pair::getLeft).map(Cabinet::getCapacity).reduce(0, Integer::sum));
    cabinetGroup.setUsedCapacity(0);
    cabinetGroup.setCreatedId(userId);
    cabinetGroup.setUpdatedId(userId);
    transactionTemplate.execute(
        status -> {
          // 柜组插入
          cabinetGroupMapper.insert(cabinetGroup);
          // 柜子插入
          Long groupId = cabinetGroup.getId();
          List<Cabinet> cabinetList =
              pairList.stream()
                  .map(Pair::getLeft)
                  .peek(cabinet -> cabinet.setCabinetGroupId(groupId))
                  .collect(Collectors.toList());
          cabinetMapper.insertList(cabinetList);
          // 柜口插入
          List<CabinetGrid> cabinetGridList =
              pairList.stream()
                  .flatMap(
                      pair -> {
                        Cabinet cabinet = pair.getLeft();
                        Long cabinetId = cabinet.getId();
                        return pair.getRight().stream()
                            .peek(
                                cabinetGrid -> {
                                  cabinetGrid.setCabinetGroupId(groupId);
                                  cabinetGrid.setCabinetId(cabinetId);
                                });
                      })
                  .collect(Collectors.toList());
          cabinetGridMapper.insertList(cabinetGridList);
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(cabinetGroup.getId()));
  }

  @Override
  public BaseResult updateCabinetGroup(CabinetGroupUpdateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    CabinetGroup orgCabinetGroup = cabinetGroupMapper.selectById(id);
    if (Objects.isNull(orgCabinetGroup)) {
      return BaseResult.failed("柜组不存在");
    }
    if (!Objects.equals(orgCabinetGroup.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该柜组不属于当前机构");
    }
    if (!groupNo.equals(orgCabinetGroup.getGroupNo())) {
      if (cabinetGroupMapper.existByGroupNo(groupNo) > 0) {
        return BaseResult.failed("柜组编号已存在");
      }
    }
    CabinetGroup cabinetGroup = CabinetConvert.getCabinetGroup(request);
    cabinetGroup.setUpdatedId(userId);
    cabinetGroupMapper.updateById(cabinetGroup);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteCabinetGroup(CabinetGroupDeleteRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    CabinetGroup orgCabinetGroup = cabinetGroupMapper.selectById(id);
    if (Objects.isNull(orgCabinetGroup)) {
      return BaseResult.failed("柜组不存在");
    }
    if (!Objects.equals(orgCabinetGroup.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该柜组不属于当前机构");
    }
    // todo: 检查柜子是否关联有上架档案
    long deleteAt = System.currentTimeMillis();
    cabinetGroupMapper.deleteById(id, deleteAt);
    cabinetMapper.deleteByCabinetGroupId(id);
    cabinetGridMapper.deleteByCabinetGroupId(id);
    return BaseResult.success();
  }

  @Override
  public BaseResult controlCabinetGroup(CabinetGroupControlRequest request) {
    // todo: 待实现
    return BaseResult.success();
  }

  /** 档案柜容量更新的内部实现 */
  private void updateCabinetUsedCapacityInternal(Integer cabinetGroupNo) {
    List<String> shelvingStatusList =
        List.of(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<CabinetGroup> cabinetGroupList = cabinetGroupMapper.selectListForStatTask(cabinetGroupNo);
    cabinetGroupList.forEach(cabinetGroup -> cabinetGroup.setUsedCapacity(0));
    for (CabinetGroup cabinetGroup : cabinetGroupList) {
      Long id = cabinetGroup.getId();
      Integer groupNo = cabinetGroup.getGroupNo();
      List<Cabinet> cabinetList = cabinetMapper.selectListByCabinetGroupIdForStatTask(id);
      cabinetList.forEach(cabinet -> cabinet.setUsedCapacity(0));
      Map<Integer, Cabinet> cabinetMap =
          cabinetList.stream()
              .collect(Collectors.toMap(Cabinet::getCabinetNo, c -> c, (c, c2) -> c));
      List<CabinetGrid> cabinetGridList =
          cabinetGridMapper.selectListByCabinetGroupIdForStatTask(id);
      cabinetGridList.forEach(cabinetGrid -> cabinetGrid.setUsedCapacity(0));
      Map<String, CabinetGrid> cabinetGridMap =
          cabinetGridList.stream()
              .collect(
                  Collectors.toMap(
                      g -> g.getCabinetId() + "_" + g.getGridNo(), r -> r, (r, r2) -> r));
      List<Archive> archiveList =
          archiveMapper.selectListByGroupNoForStatTask(shelvingStatusList, null, null, groupNo);
      // 统计每个位置上的档案数量
      for (Archive archive : archiveList) {
        // 累加柜组已用容量
        cabinetGroup.setUsedCapacity(cabinetGroup.getUsedCapacity() + 1);
        // 累加柜已用容量
        Cabinet cabinet = cabinetMap.get(archive.getCabinetNo());
        if (Objects.isNull(cabinet)) {
          continue;
        }
        cabinet.setUsedCapacity(cabinet.getUsedCapacity() + 1);
        // 累加格口已用容量
        CabinetGrid cabinetGrid =
            cabinetGridMap.get(cabinet.getId() + "_" + archive.getCabinetGridNo());
        if (Objects.isNull(cabinetGrid)) {
          continue;
        }
        cabinetGrid.setUsedCapacity(cabinetGrid.getUsedCapacity() + 1);
      }
      // 更新数据库
      if (CollUtil.isNotEmpty(cabinetGroupList)) {
        cabinetMapper.updateUsedCapacityForStatTask(cabinetList);
      }
      if (CollUtil.isNotEmpty(cabinetGridList)) {
        // 分批更新，避免数据量过大导致性能问题
        for (int i = 0; i < cabinetGridList.size(); i += GlobalConstant.BATCH_SQL_MAX_SIZE) {
          int endIndex = Math.min(i + GlobalConstant.BATCH_SQL_MAX_SIZE, cabinetGridList.size());
          List<CabinetGrid> batch = cabinetGridList.subList(i, endIndex);
          cabinetGridMapper.updateUsedCapacityForStatTask(batch);
        }
      }
    }
    if (CollUtil.isNotEmpty(cabinetGroupList)) {
      cabinetGroupMapper.updateUsedCapacityForStatTask(cabinetGroupList);
    }
    log.info(
        "档案柜容量更新完成，组编号：{}",
        Arrays.toString(cabinetGroupList.stream().map(CabinetGroup::getGroupNo).toArray()));
  }
}
