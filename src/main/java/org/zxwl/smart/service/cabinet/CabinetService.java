package org.zxwl.smart.service.cabinet;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.cabinet.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.cabinet.CabinetGroupListVO;
import org.zxwl.smart.domain.response.cabinet.CabinetGroupSelectVO;
import org.zxwl.smart.domain.response.cabinet.CabinetGroupVO;

@Validated
public interface CabinetService {

  /**
   * 异步执行档案柜容量更新，带防抖功能
   *
   * @param cabinetGroupNo 档案柜组编号
   */
  void updateCabinetUsedCapacityAsync(Integer cabinetGroupNo);

  PageResult<CabinetGroupListVO> getCabinetGroupList(
      @NotNull @Valid CabinetGroupListRequest request);

  ListResult<CabinetGroupSelectVO> getCabinetGroupListForSelect(
      @NotNull @Valid CabinetGroupSelectRequest request);

  SimpleResult<CabinetGroupVO> getCabinetGroup(@NotNull @Valid CabinetGroupGetRequest request);

  SimpleResult<BaseCreateVO> createCabinetGroup(@NotNull @Valid CabinetGroupCreateRequest request);

  BaseResult updateCabinetGroup(@NotNull @Valid CabinetGroupUpdateRequest request);

  BaseResult deleteCabinetGroup(@NotNull @Valid CabinetGroupDeleteRequest request);

  BaseResult controlCabinetGroup(@NotNull @Valid CabinetGroupControlRequest request);
}
