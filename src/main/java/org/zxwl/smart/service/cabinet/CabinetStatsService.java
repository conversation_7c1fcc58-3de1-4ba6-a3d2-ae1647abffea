package org.zxwl.smart.service.cabinet;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.cabinet.CabinetOperatorStatRequest;
import org.zxwl.smart.domain.request.cabinet.CabinetStatRequest;
import org.zxwl.smart.domain.request.cabinet.HrCabinetOperatorStatRequest;
import org.zxwl.smart.domain.request.cabinet.HrCabinetStatRequest;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.cabinet.CabinetOperatorStatVO;
import org.zxwl.smart.domain.response.cabinet.CabinetStatVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetOperatorStatVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetStatVO;

@Validated
public interface CabinetStatsService {

  SimpleResult<CabinetStatVO> getCabinetStat(@NotNull @Valid CabinetStatRequest request);

  ListResult<CabinetOperatorStatVO> getCabinetOperatorStatList(
      @NotNull @Valid CabinetOperatorStatRequest request);

  SimpleResult<HrCabinetStatVO> getHrCabinetStat(@NotNull @Valid HrCabinetStatRequest request);

  ListResult<HrCabinetOperatorStatVO> getHrCabinetOperatorStatList(
      @NotNull @Valid HrCabinetOperatorStatRequest request);
}
