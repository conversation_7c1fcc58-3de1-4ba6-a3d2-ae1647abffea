package org.zxwl.smart.service.rack.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.rack.RackConvert;
import org.zxwl.smart.common.utils.LocationUtil;
import org.zxwl.smart.domain.request.rack.RackLayerBindArchiveTypeRequest;
import org.zxwl.smart.domain.request.rack.RackLayerBindTIDRequest;
import org.zxwl.smart.domain.request.rack.RackLayerListRequest;
import org.zxwl.smart.domain.request.rack.RackLayerRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.rack.RackLayerListVO;
import org.zxwl.smart.domain.response.rack.RackLayerVO;
import org.zxwl.smart.mybatis.entity.archive.ArchiveType;
import org.zxwl.smart.mybatis.entity.rack.RackGrid;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;
import org.zxwl.smart.mybatis.entity.rack.RackLayer;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.ext.rack.RackLayerExt;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGridMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackLayerMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.rack.RackLayerService;

@Service
@AllArgsConstructor
public class RackLayerServiceImpl implements RackLayerService {

  private final RackLayerMapper rackLayerMapper;
  private final RackGridMapper rackGridMapper;
  private final RackGroupMapper rackGroupMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final WarehouseMapper warehouseMapper;

  @Override
  public PageResult<RackLayerListVO> getRackLayerList(RackLayerListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long rackGroupId = request.getRackGroupId();
    Integer rackGroupNo = request.getRackGroupNo();
    Integer columnNo = request.getColumnNo();
    Integer panelNo = request.getPanelNo();
    Integer sectionNo = request.getSectionNo();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    RackGroup rackGroup;
    if (Objects.nonNull(rackGroupId)) {
      rackGroup = rackGroupMapper.selectById(rackGroupId);
    } else if (Objects.nonNull(rackGroupNo)) {
      rackGroup = rackGroupMapper.selectByGroupNo(rackGroupNo);
    } else {
      return new PageResult<>("请求参数错误，架组id和架组编号不能同时为空");
    }
    if (Objects.isNull(rackGroup)) {
      return new PageResult<>(BaseResult.failed("密集架组不存在或已被删除"));
    }
    if (Objects.nonNull(organizationId)
        && !Objects.equals(organizationId, rackGroup.getOrganizationId())) {
      return new PageResult<>(BaseResult.failed("密集架组不属于当前机构"));
    }
    rackGroupId = rackGroup.getId();
    PageHelper.startPage(pageNum, pageSize);
    List<RackLayerExt> rackLayerList =
        rackLayerMapper.selectList(rackGroupId, columnNo, panelNo, sectionNo);
    List<RackLayerListVO> voList = getRackLayerListVOList(rackLayerList);
    return new PageResult<>(voList, rackLayerList);
  }

  @Override
  public SimpleResult<RackLayerVO> getRackLayer(RackLayerRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    String tid = request.getTid();
    RackLayer rackLayer = rackLayerMapper.selectByIdOrLayerTid(null, tid);
    if (Objects.isNull(rackLayer)) {
      return new SimpleResult<>("层位不存在");
    }
    Long archiveTypeId = rackLayer.getArchiveTypeId();
    ArchiveType archiveType = null;
    if (Objects.nonNull(archiveTypeId)) {
      archiveType = archiveTypeMapper.selectById(archiveTypeId, true);
    }
    Long rackGroupId = rackLayer.getRackGroupId();
    RackGroup rackGroup = rackGroupMapper.selectById(rackGroupId);
    if (Objects.isNull(rackGroup)) {
      return new SimpleResult<>("密集架组不存在或已被删除");
    }
    if (!Objects.equals(rackGroup.getOrganizationId(), organizationId)) {
      return new SimpleResult<>("该密集架不属于当前机构");
    }
    List<RackGrid> rackGridList = rackGridMapper.selectListByRackLayerId(rackLayer.getId());
    RackLayerVO vo = RackConvert.getRackLayerVO(rackLayer);
    if (Objects.nonNull(archiveType)) {
      vo.setArchiveTypeName(archiveType.getTypeName());
    }
    vo.setRackGroupNo(rackGroup.getGroupNo());
    vo.setLocation(
        LocationUtil.locationForRack(
            rackGroup.getGroupNo(),
            rackLayer.getColumnNo(),
            rackLayer.getPanelNo(),
            rackLayer.getSectionNo(),
            rackLayer.getLayerNo(),
            null));
    vo.setRackGridList(RackConvert.getRackLayerGridVOList(rackGridList));
    return new SimpleResult<>(vo);
  }

  @Override
  public BaseResult bindRackLayerArchiveType(RackLayerBindArchiveTypeRequest request) {
    Long archivesTypeId = request.getArchivesTypeId();
    List<Long> idList = request.getIdList();
    if (archiveTypeMapper.existById(archivesTypeId) == 0) {
      return BaseResult.failed("关联门类不存在");
    }
    rackLayerMapper.updateBatchArchiveTypeId(idList, archivesTypeId);
    return BaseResult.success();
  }

  @Override
  public BaseResult bindRackLayerTID(RackLayerBindTIDRequest request) {
    Long id = request.getId();
    String layerTid = request.getLayerTid();
    RackLayer orgRackLayer = rackLayerMapper.selectByIdOrLayerTid(id, null);
    if (Objects.isNull(orgRackLayer)) {
      return BaseResult.failed("层位不存在");
    }
    if (!StrUtil.equals(orgRackLayer.getLayerTid(), layerTid)) {
      if (rackLayerMapper.existByTid(layerTid) > 0) {
        return BaseResult.failed("层位TID已存在");
      }
    }
    RackLayer rackLayer = new RackLayer();
    rackLayer.setId(id);
    rackLayer.setLayerTid(layerTid);
    rackLayerMapper.updateByIdSelective(rackLayer);
    return BaseResult.success();
  }

  private List<RackLayerListVO> getRackLayerListVOList(List<RackLayerExt> rackLayerList) {
    if (CollUtil.isEmpty(rackLayerList)) {
      return new ArrayList<>();
    }
    List<Long> warehouseIdList =
        rackLayerList.stream()
            .map(RackLayerExt::getWarehouseId)
            .distinct()
            .collect(Collectors.toList());
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    List<Long> archiveTypeIdList =
        rackLayerList.stream()
            .map(RackLayer::getArchiveTypeId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, ArchiveType> archiveTypeMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(archiveTypeIdList)) {
      List<ArchiveType> archiveTypeList = archiveTypeMapper.selectNameList(archiveTypeIdList, true);
      archiveTypeMap =
          archiveTypeList.stream().collect(Collectors.toMap(ArchiveType::getId, v -> v));
    }

    List<RackLayerListVO> voList = RackConvert.getRackLayerListVOList(rackLayerList);
    for (RackLayerListVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      ArchiveType archiveType = archiveTypeMap.get(vo.getArchiveTypeId());
      vo.setWarehouseName(warehouse.getWarehouseName());
      if (Objects.nonNull(archiveType)) {
        vo.setArchiveTypeName(archiveType.getTypeName());
      }
      vo.setLocation(
          LocationUtil.locationForRack(
              vo.getRackGroupNo(),
              vo.getColumnNo(),
              vo.getPanelNo(),
              vo.getSectionNo(),
              vo.getLayerNo(),
              null));
    }
    return voList;
  }
}
