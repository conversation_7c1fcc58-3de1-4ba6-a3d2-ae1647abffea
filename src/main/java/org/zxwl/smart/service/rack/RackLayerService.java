package org.zxwl.smart.service.rack;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.rack.RackLayerBindArchiveTypeRequest;
import org.zxwl.smart.domain.request.rack.RackLayerBindTIDRequest;
import org.zxwl.smart.domain.request.rack.RackLayerListRequest;
import org.zxwl.smart.domain.request.rack.RackLayerRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.rack.RackLayerListVO;
import org.zxwl.smart.domain.response.rack.RackLayerVO;

@Validated
public interface RackLayerService {

  PageResult<RackLayerListVO> getRackLayerList(@NotNull @Valid RackLayerListRequest request);

  SimpleResult<RackLayerVO> getRackLayer(@NotNull @Valid RackLayerRequest request);

  BaseResult bindRackLayerArchiveType(@NotNull @Valid RackLayerBindArchiveTypeRequest request);

  BaseResult bindRackLayerTID(@NotNull @Valid RackLayerBindTIDRequest request);
}
