package org.zxwl.smart.service.rack;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.rack.RackColumnCreateRequest;
import org.zxwl.smart.domain.request.rack.RackColumnDeleteRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;

@Validated
public interface RackColumnService {

  SimpleResult<BaseCreateVO> createRackColumn(@NotNull @Valid RackColumnCreateRequest request);

  BaseResult deleteRackColumn(@NotNull @Valid RackColumnDeleteRequest request);
}
