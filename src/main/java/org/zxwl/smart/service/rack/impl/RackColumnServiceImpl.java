package org.zxwl.smart.service.rack.impl;

import org.springframework.stereotype.Service;
import org.zxwl.smart.domain.request.rack.RackColumnCreateRequest;
import org.zxwl.smart.domain.request.rack.RackColumnDeleteRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.service.rack.RackColumnService;

@Service
public class RackColumnServiceImpl implements RackColumnService {

  @Override
  public SimpleResult<BaseCreateVO> createRackColumn(RackColumnCreateRequest request) {
    return new SimpleResult<>("暂未实现，需要请告知服务端");
  }

  @Override
  public BaseResult deleteRackColumn(RackColumnDeleteRequest request) {
    return BaseResult.failed("暂未实现，需要请告知服务端");
  }
}
