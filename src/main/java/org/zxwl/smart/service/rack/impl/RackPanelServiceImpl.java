package org.zxwl.smart.service.rack.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.domain.request.rack.RackPanelListRequest;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.rack.RackPanelVO;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;
import org.zxwl.smart.mybatis.entity.rack.RackLayer;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackLayerMapper;
import org.zxwl.smart.service.rack.RackPanelService;

@Service
@AllArgsConstructor
public class RackPanelServiceImpl implements RackPanelService {

  private final RackGroupMapper rackGroupMapper;
  private final RackLayerMapper rackLayerMapper;

  @Override
  public ListResult<RackPanelVO> getRackPanelList(RackPanelListRequest request) {
    Long warehouseId = request.getWarehouseId();
    Integer groupNo = request.getGroupNo();
    List<RackGroup> rackGroupList = rackGroupMapper.selectListByWarehouseId(warehouseId, groupNo);
    List<Long> rackGroupIdList = rackGroupList.stream().map(RackGroup::getId).toList();
    if (CollUtil.isEmpty(rackGroupIdList)) {
      return new ListResult<>(new ArrayList<>());
    }
    List<RackLayer> rackLayerList = rackLayerMapper.selectListByGroupIdList(rackGroupIdList);
    Map<Long, RackGroup> rackGroupMap =
        rackGroupList.stream().collect(Collectors.toMap(RackGroup::getId, r -> r));
    Map<String, List<RackLayer>> rackLayerMap =
        rackLayerList.stream()
            .collect(
                Collectors.groupingBy(
                    l -> l.getRackGroupId() + "_" + l.getColumnNo() + "_" + l.getPanelNo()));
    List<RackPanelVO> voList = new ArrayList<>();
    rackLayerMap.forEach(
        (key, value) -> {
          Long rackGroupId = value.get(0).getRackGroupId();
          RackGroup rackGroup = rackGroupMap.get(rackGroupId);
          RackPanelVO vo = new RackPanelVO();
          vo.setGroupNo(rackGroup.getGroupNo());
          vo.setColumnNo(value.get(0).getColumnNo());
          vo.setPanelNo(value.get(0).getPanelNo());
          vo.setCapacity(value.stream().mapToInt(RackLayer::getCapacity).sum());
          vo.setUsedCapacity(value.stream().mapToInt(RackLayer::getUsedCapacity).sum());
          voList.add(vo);
        });
    voList.sort(
        Comparator.comparing(RackPanelVO::getGroupNo)
            .thenComparing(RackPanelVO::getColumnNo)
            .thenComparing(RackPanelVO::getPanelNo));
    return new ListResult<>(voList);
  }
}
