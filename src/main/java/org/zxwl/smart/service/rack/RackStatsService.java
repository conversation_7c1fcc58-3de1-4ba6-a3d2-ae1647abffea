package org.zxwl.smart.service.rack;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.rack.RackStatRequest;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.rack.RackStatVO;

@Validated
public interface RackStatsService {

  SimpleResult<RackStatVO> getRackStat(@NotNull @Valid RackStatRequest request);
}
