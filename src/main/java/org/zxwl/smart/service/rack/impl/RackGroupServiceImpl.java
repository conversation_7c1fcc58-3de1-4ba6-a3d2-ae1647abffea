package org.zxwl.smart.service.rack.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.rack.RackConvert;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.utils.DebounceUtil;
import org.zxwl.smart.constant.consist.GlobalConstant;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.constant.enums.rack.RackTypeEnum;
import org.zxwl.smart.constant.enums.rack.RackVendorEnum;
import org.zxwl.smart.domain.request.rack.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.rack.*;
import org.zxwl.smart.mybatis.entity.archive.Archive;
import org.zxwl.smart.mybatis.entity.rack.RackGrid;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;
import org.zxwl.smart.mybatis.entity.rack.RackLayer;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGridMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackLayerMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.rack.RackGroupService;

@Slf4j
@Service
@AllArgsConstructor
public class RackGroupServiceImpl implements RackGroupService {

  private final RackGroupMapper rackGroupMapper;
  private final RackLayerMapper rackLayerMapper;
  private final WarehouseMapper warehouseMapper;
  private final RackGridMapper rackGridMapper;
  private final ArchiveMapper archiveMapper;
  private final ScheduledExecutorService storageCapacityUpdateExecutor;
  private final TransactionTemplate transactionTemplate;

  @Override
  public void updateRackUsedCapacityAsync(Integer rackGroupNo) {
    String key = "updateRackUsedCapacity_" + rackGroupNo;
    DebounceUtil.debounce(
        key,
        () -> updateRackUsedCapacityInternal(rackGroupNo),
        GlobalConstant.STORAGE_CAPACITY_UPDATE_DEBOUNCE_DELAY_MS,
        TimeUnit.MILLISECONDS,
        storageCapacityUpdateExecutor);
  }

  @Override
  public ListResult<RackVendorVO> getRackVendorList() {
    List<RackVendorVO> voList =
        Arrays.stream(RackVendorEnum.values())
            .map(rackVendorEnum -> new RackVendorVO(rackVendorEnum.getValue()))
            .collect(Collectors.toList());
    return new ListResult<>(voList);
  }

  @Override
  public ListResult<RackTypeVO> getRackTypeList() {
    List<RackTypeVO> voList =
        Arrays.stream(RackTypeEnum.values())
            .map(
                rackTypeEnum -> new RackTypeVO(rackTypeEnum.getValue(), rackTypeEnum.getGridFlag()))
            .collect(Collectors.toList());
    return new ListResult<>(voList);
  }

  @Override
  public PageResult<RackGroupListVO> getRackGroupList(RackGroupListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    String rackType = request.getRackType();
    String rackVendor = request.getRackVendor();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<RackGroup> rackGroupList =
        rackGroupMapper.selectList(organizationId, warehouseId, rackType, rackVendor);
    List<RackGroupListVO> voList = RackConvert.getRackGroupVOList(rackGroupList);
    // 库房名字补充
    List<Long> warehouseIdList =
        voList.stream()
            .map(RackGroupListVO::getWarehouseId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, Warehouse> warehouseMap = Collections.emptyMap();
    if (CollUtil.isNotEmpty(warehouseIdList)) {
      List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, false);
      warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    }
    for (RackGroupListVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseNo(warehouse.getWarehouseNo());
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    return new PageResult<>(voList, rackGroupList);
  }

  @Override
  public ListResult<RackGroupSelectVO> getRackGroupListForSelect(
      RackGroupListForSelectRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long warehouseId = request.getWarehouseId();
    List<RackGroup> rackGroupList =
        rackGroupMapper.selectList(organizationId, warehouseId, null, null);
    List<Long> rackGroupIdList =
        rackGroupList.stream().map(RackGroup::getId).collect(Collectors.toList());
    List<RackLayer> rackLayerList = Collections.emptyList();
    if (CollUtil.isNotEmpty(rackGroupIdList)) {
      rackLayerList = rackLayerMapper.selectListByGroupIdList(rackGroupIdList);
    }
    List<Long> rackLayerIdList =
        rackLayerList.stream().map(RackLayer::getId).collect(Collectors.toList());
    List<RackGrid> rackGridList = Collections.emptyList();
    if (CollUtil.isNotEmpty(rackLayerIdList)) {
      rackGridList = rackGridMapper.selectListByRackLayerIdList(rackLayerIdList);
    }
    Map<Long, List<RackLayer>> rackGroupMap =
        rackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getRackGroupId));
    Map<Long, List<RackGrid>> rackGridMap =
        rackGridList.stream().collect(Collectors.groupingBy(RackGrid::getRackLayerId));
    List<RackGroupSelectVO> voList = new ArrayList<>();
    for (RackGroup rackGroup : rackGroupList) {
      Long id = rackGroup.getId();
      Integer groupNo = rackGroup.getGroupNo();
      Map<Integer, List<RackLayer>> rackColumnMap =
          rackGroupMap.get(id).stream().collect(Collectors.groupingBy(RackLayer::getColumnNo));
      List<RackGroupSelectVO.ColumnVO> columnVOList = new ArrayList<>();
      for (Integer columnNo : rackColumnMap.keySet()) {
        // 列
        List<RackLayer> columnRackLayerList = rackColumnMap.get(columnNo);
        Map<Integer, List<RackLayer>> rackPanelMap =
            columnRackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getPanelNo));
        List<RackGroupSelectVO.PanelVO> panelVOList = new ArrayList<>();
        for (Integer panelNo : rackPanelMap.keySet()) {
          // 面
          List<RackLayer> panelRackLayerList = rackPanelMap.get(panelNo);
          Map<Integer, List<RackLayer>> sectionLayerMap =
              panelRackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getSectionNo));
          List<RackGroupSelectVO.SectionVO> sectionVOList = new ArrayList<>();
          for (Integer sectionNo : sectionLayerMap.keySet()) {
            // 节
            List<RackLayer> layerRackLayerList = sectionLayerMap.get(sectionNo);
            List<RackGroupSelectVO.LayerVO> layerVOList = new ArrayList<>();
            for (RackLayer rackLayer : layerRackLayerList) {
              // 层
              Integer layerNo = rackLayer.getLayerNo();
              List<RackGrid> pairRackGridList =
                  rackGridMap.getOrDefault(rackLayer.getId(), Collections.emptyList());
              List<RackGroupSelectVO.GridVO> rackGridVOList = new ArrayList<>();
              for (RackGrid rackGrid : pairRackGridList) {
                Integer gridNo = rackGrid.getGridNo();
                RackGroupSelectVO.GridVO rackGridVO = new RackGroupSelectVO.GridVO(gridNo);
                rackGridVO.setGridNo(gridNo);
                rackGridVOList.add(rackGridVO);
              }
              RackGroupSelectVO.LayerVO layerVO =
                  new RackGroupSelectVO.LayerVO(layerNo, rackGridVOList);
              layerVOList.add(layerVO);
            }
            RackGroupSelectVO.SectionVO sectionVO =
                new RackGroupSelectVO.SectionVO(sectionNo, layerVOList);
            sectionVOList.add(sectionVO);
          }
          RackGroupSelectVO.PanelVO panelVO = new RackGroupSelectVO.PanelVO(panelNo, sectionVOList);
          panelVO.setRackSectionList(sectionVOList);
          panelVOList.add(panelVO);
        }
        RackGroupSelectVO.ColumnVO columnVO = new RackGroupSelectVO.ColumnVO(columnNo, panelVOList);
        columnVOList.add(columnVO);
      }
      RackGroupSelectVO vo = new RackGroupSelectVO();
      vo.setRackType(rackGroup.getRackType());
      vo.setRackVendor(rackGroup.getRackVendor());
      vo.setGroupNo(groupNo);
      vo.setRackColumnList(columnVOList);
      voList.add(vo);
    }
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<RackGroupVO> getRackGroup(RackGroupGetRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    Integer requestColumnNo = request.getColumnNo();
    RackGroup rackGroup;
    if (Objects.nonNull(id)) {
      rackGroup = rackGroupMapper.selectById(id);
    } else if (Objects.nonNull(groupNo)) {
      rackGroup = rackGroupMapper.selectByGroupNo(groupNo);
    } else {
      return new SimpleResult<>("请求参数错误");
    }
    if (Objects.isNull(rackGroup)) {
      return new SimpleResult<>("架组不存在");
    }
    if (Objects.nonNull(organizationId)
        && !Objects.equals(rackGroup.getOrganizationId(), organizationId)) {
      return new SimpleResult<>("该架组不属于当前机构");
    }
    id = rackGroup.getId();
    groupNo = rackGroup.getGroupNo();
    List<RackLayer> rackLayerList = rackLayerMapper.selectListByRackGroupId(id, requestColumnNo);
    List<String> shelvingStatusList =
        ListUtil.toList(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<Archive> archiveList =
        archiveMapper.selectListByRackGroupNo(shelvingStatusList, groupNo, requestColumnNo);
    Map<String, List<Archive>> archiveMap =
        archiveList.stream()
            .collect(
                Collectors.groupingBy(
                    a ->
                        +a.getRackColumnNo()
                            + "_"
                            + a.getRackPanelNo()
                            + "_"
                            + a.getRackSectionNo()
                            + "_"
                            + a.getRackLayerNo()));
    Map<Integer, List<RackLayer>> rackColumnMap =
        rackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getColumnNo));
    List<RackGroupVO.ColumnVO> columnVOList = new ArrayList<>();
    for (Integer columnNo : rackColumnMap.keySet()) {
      // 列
      List<RackLayer> columnRackLayerList = rackColumnMap.get(columnNo);
      Map<Integer, List<RackLayer>> rackPanelMap =
          columnRackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getPanelNo));
      List<RackGroupVO.PanelVO> panelVOList = new ArrayList<>();
      for (Integer panelNo : rackPanelMap.keySet()) {
        // 面
        List<RackLayer> panelRackLayerList = rackPanelMap.get(panelNo);
        Map<Integer, List<RackLayer>> sectionLayerMap =
            panelRackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getSectionNo));
        List<RackGroupVO.SectionVO> sectionVOList = new ArrayList<>();
        for (Integer sectionNo : sectionLayerMap.keySet()) {
          // 节
          List<RackLayer> layerRackLayerList = sectionLayerMap.get(sectionNo);
          List<RackGroupVO.LayerVO> layerVOList = new ArrayList<>();
          for (RackLayer rackLayer : layerRackLayerList) {
            // 层
            Integer layerNo = rackLayer.getLayerNo();
            List<Archive> pairArchiveList =
                archiveMap.get(columnNo + "_" + panelNo + "_" + sectionNo + "_" + layerNo);
            List<RackGroupVO.ArchiveVO> archiveVOList =
                RackConvert.getArchiveVOList(pairArchiveList);
            RackGroupVO.LayerVO layerVO = RackConvert.getRackGroupLayerVO(rackLayer);
            layerVO.setArchiveList(archiveVOList);
            layerVOList.add(layerVO);
          }
          RackGroupVO.SectionVO sectionVO = new RackGroupVO.SectionVO(sectionNo, layerVOList);
          sectionVOList.add(sectionVO);
        }
        RackGroupVO.PanelVO panelVO = new RackGroupVO.PanelVO(panelNo, sectionVOList);
        panelVO.setRackSectionList(sectionVOList);
        panelVOList.add(panelVO);
      }
      RackGroupVO.ColumnVO columnVO = new RackGroupVO.ColumnVO(columnNo, panelVOList);
      columnVOList.add(columnVO);
    }
    RackGroupVO vo = RackConvert.getRackGroupVO(rackGroup);
    vo.setRackColumnList(columnVOList);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<BoxRackGroupVO> getBoxRackGroup(BoxRackGroupGetRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    Integer requestColumnNo = request.getColumnNo();
    RackGroup rackGroup;
    if (Objects.nonNull(id)) {
      rackGroup = rackGroupMapper.selectById(id);
    } else if (Objects.nonNull(groupNo)) {
      rackGroup = rackGroupMapper.selectByGroupNo(groupNo);
    } else {
      return new SimpleResult<>("请求参数错误");
    }
    if (Objects.isNull(rackGroup)) {
      return new SimpleResult<>("架组不存在");
    }
    if (Objects.nonNull(organizationId)
        && !Objects.equals(rackGroup.getOrganizationId(), organizationId)) {
      return new SimpleResult<>("该架组不属于当前机构");
    }
    id = rackGroup.getId();
    groupNo = rackGroup.getGroupNo();
    List<RackLayer> rackLayerList = rackLayerMapper.selectListByRackGroupId(id, requestColumnNo);
    List<Long> rackLayerIdList =
        rackLayerList.stream().map(RackLayer::getId).collect(Collectors.toList());
    List<RackGrid> rackGridList = Collections.emptyList();
    if (CollUtil.isNotEmpty(rackLayerIdList)) {
      rackGridList = rackGridMapper.selectListByRackLayerIdList(rackLayerIdList);
    }
    List<String> shelvingStatusList =
        ListUtil.toList(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<Archive> archiveList =
        archiveMapper.selectListByRackGroupNo(shelvingStatusList, groupNo, requestColumnNo);
    Map<String, Archive> archiveMap =
        archiveList.stream()
            .collect(
                Collectors.toMap(
                    a ->
                        +a.getRackColumnNo()
                            + "_"
                            + a.getRackPanelNo()
                            + "_"
                            + a.getRackSectionNo()
                            + "_"
                            + a.getRackLayerNo()
                            + "_"
                            + a.getRackGridNo(),
                    v -> v,
                    (v, v2) -> v2));
    Map<Integer, List<RackLayer>> rackColumnMap =
        rackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getColumnNo));
    Map<Long, List<RackGrid>> rackGridMap =
        rackGridList.stream().collect(Collectors.groupingBy(RackGrid::getRackLayerId));
    List<BoxRackGroupVO.BoxColumnVO> columnVOList = new ArrayList<>();
    for (Integer columnNo : rackColumnMap.keySet()) {
      // 列
      List<RackLayer> columnRackLayerList = rackColumnMap.get(columnNo);
      Map<Integer, List<RackLayer>> rackPanelMap =
          columnRackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getPanelNo));
      List<BoxRackGroupVO.BoxPanelVO> panelVOList = new ArrayList<>();
      for (Integer panelNo : rackPanelMap.keySet()) {
        // 面
        List<RackLayer> panelRackLayerList = rackPanelMap.get(panelNo);
        Map<Integer, List<RackLayer>> sectionLayerMap =
            panelRackLayerList.stream().collect(Collectors.groupingBy(RackLayer::getSectionNo));
        List<BoxRackGroupVO.BoxSectionVO> sectionVOList = new ArrayList<>();
        for (Integer sectionNo : sectionLayerMap.keySet()) {
          // 节
          List<RackLayer> layerRackLayerList = sectionLayerMap.get(sectionNo);
          List<BoxRackGroupVO.BoxLayerVO> layerVOList = new ArrayList<>();
          for (RackLayer rackLayer : layerRackLayerList) {
            // 层
            Integer layerNo = rackLayer.getLayerNo();
            List<RackGrid> pairRackGridList =
                rackGridMap.getOrDefault(rackLayer.getId(), Collections.emptyList());
            List<BoxRackGroupVO.BoxGridVO> rackGridVOList = new ArrayList<>();
            for (RackGrid rackGrid : pairRackGridList) {
              Integer gridNo = rackGrid.getGridNo();
              BoxRackGroupVO.BoxGridVO rackGridVO = new BoxRackGroupVO.BoxGridVO();
              rackGridVO.setGridNo(gridNo);
              Archive archive =
                  archiveMap.get(
                      columnNo + "_" + panelNo + "_" + sectionNo + "_" + layerNo + "_" + gridNo);
              if (Objects.nonNull(archive)) {
                rackGridVO.setArchiveId(archive.getId());
                rackGridVO.setShelvingStatus(archive.getShelvingStatus());
                rackGridVO.setArchiveName(archive.getArchiveName());
                rackGridVO.setArchiveNo(archive.getArchiveNo());
                rackGridVO.setTid(archive.getTid());
              }
              rackGridVOList.add(rackGridVO);
            }
            BoxRackGroupVO.BoxLayerVO layerVO = RackConvert.getBoxRackGroupLayerVO(rackLayer);
            layerVO.setRackGridList(rackGridVOList);
            layerVOList.add(layerVO);
          }
          BoxRackGroupVO.BoxSectionVO sectionVO =
              new BoxRackGroupVO.BoxSectionVO(sectionNo, layerVOList);
          sectionVOList.add(sectionVO);
        }
        BoxRackGroupVO.BoxPanelVO panelVO = new BoxRackGroupVO.BoxPanelVO(panelNo, sectionVOList);
        panelVO.setRackSectionList(sectionVOList);
        panelVOList.add(panelVO);
      }
      BoxRackGroupVO.BoxColumnVO columnVO = new BoxRackGroupVO.BoxColumnVO(columnNo, panelVOList);
      columnVOList.add(columnVO);
    }
    BoxRackGroupVO vo = RackConvert.getBoxRackGroupVO(rackGroup);
    vo.setRackColumnList(columnVOList);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<BaseCreateVO> createRackGroup(RackGroupCreateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long warehouseId = request.getWarehouseId();
    String rackType = request.getRackType();
    Integer columnNum = request.getColumnNum();
    Integer panelNum = request.getPanelNum();
    Integer sectionNum = request.getSectionNum();
    Integer layerNum = request.getLayerNum();
    Integer gridNum = request.getGridNum();
    Integer fixedColumnNo = request.getFixedColumnNo();
    if (warehouseMapper.existById(warehouseId) == 0) {
      return new SimpleResult<>("库房不存在");
    }
    RackTypeEnum rackTypeEnum = RackTypeEnum.getEnumByValue(rackType);
    assert rackTypeEnum != null;
    if (rackTypeEnum.getGridFlag() && gridNum <= 0) {
      return new SimpleResult<>(rackTypeEnum.getValue() + "格口数不能为0");
    }
    if (!rackTypeEnum.getGridFlag() && gridNum > 0) {
      return new SimpleResult<>(rackTypeEnum.getValue() + "不能存在格口");
    }
    if (fixedColumnNo > columnNum) {
      return new SimpleResult<>("固定列号不能大于列数");
    }
    // 新建架层
    List<Pair<RackLayer, List<RackGrid>>> pairList = new ArrayList<>();
    for (int columnNo = 1; columnNo <= columnNum; columnNo++) {
      for (int panelNo = 1; panelNo <= panelNum; panelNo++) {
        for (int sectionNo = 1; sectionNo <= sectionNum; sectionNo++) {
          for (int layerNo = 1; layerNo <= layerNum; layerNo++) {
            // 新建格口
            List<RackGrid> rackGridList = new ArrayList<>();
            for (int gridNo = 1; gridNo <= gridNum; gridNo++) {
              RackGrid rackGrid = new RackGrid();
              rackGrid.setGridNo(gridNo);
              rackGrid.setCapacity(1);
              rackGrid.setUsedCapacity(0);
              rackGrid.setCreatedId(userId);
              rackGridList.add(rackGrid);
            }
            // 新建架层
            RackLayer rackLayer = new RackLayer();
            rackLayer.setColumnNo(columnNo);
            rackLayer.setPanelNo(panelNo);
            rackLayer.setSectionNo(sectionNo);
            rackLayer.setLayerNo(layerNo);
            rackLayer.setCapacity(gridNum > 0 ? gridNum : 100);
            rackLayer.setUsedCapacity(0);
            rackLayer.setCreatedId(userId);
            pairList.add(new Pair<>(rackLayer, rackGridList));
          }
        }
      }
    }
    // 新建架组
    int groupNo = rackGroupMapper.maxGroupNo();
    RackGroup rackGroup = RackConvert.getRackGroup(request);
    rackGroup.setOrganizationId(organizationId);
    rackGroup.setGroupNo(groupNo + 1);
    rackGroup.setCapacity(
        pairList.stream().map(Pair::getLeft).map(RackLayer::getCapacity).reduce(0, Integer::sum));
    rackGroup.setUsedCapacity(0);
    rackGroup.setCreatedId(userId);
    rackGroup.setUpdatedId(userId);
    // 入库
    transactionTemplate.execute(
        status -> {
          rackGroupMapper.insert(rackGroup);
          Long rackGroupId = rackGroup.getId();
          List<RackLayer> rackLayerList =
              pairList.stream()
                  .map(Pair::getLeft)
                  .peek(layer -> layer.setRackGroupId(rackGroupId))
                  .collect(Collectors.toList());
          rackLayerMapper.insertList(rackLayerList);
          List<RackGrid> rackGridList =
              pairList.stream()
                  .flatMap(
                      pair -> {
                        RackLayer rackLayer = pair.getLeft();
                        Long rackLayerId = rackLayer.getId();
                        return pair.getRight().stream()
                            .peek(
                                grid -> {
                                  grid.setRackGroupId(rackGroupId);
                                  grid.setRackLayerId(rackLayerId);
                                });
                      })
                  .collect(Collectors.toList());
          if (CollUtil.isNotEmpty(rackGridList)) {
            rackGridMapper.insertList(rackGridList);
          }
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(rackGroup.getId()));
  }

  @Override
  public BaseResult updateRackGroup(RackGroupUpdateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Integer groupNo = request.getGroupNo();
    Integer fixedColumnNo = request.getFixedColumnNo();
    RackGroup orgRackGroup = rackGroupMapper.selectById(id);
    if (Objects.isNull(orgRackGroup)) {
      return BaseResult.failed("密集架组不存在");
    }
    if (!Objects.equals(orgRackGroup.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该密集架不属于当前机构");
    }
    if (!groupNo.equals(orgRackGroup.getGroupNo())) {
      if (rackGroupMapper.existByGroupNo(groupNo) > 0) {
        return BaseResult.failed("该密集架组组编号已存在");
      }
    }
    if (!fixedColumnNo.equals(orgRackGroup.getFixedColumnNo())
        && fixedColumnNo > orgRackGroup.getColumnNum()) {
      return BaseResult.failed("固定列号不能大于列数");
    }
    RackGroup rackGroup = RackConvert.getRackGroup(request);
    rackGroup.setUpdatedId(userId);
    rackGroupMapper.updateById(rackGroup);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteRackGroup(RackGroupDeleteRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    RackGroup orgRackGroup = rackGroupMapper.selectById(id);
    if (Objects.isNull(orgRackGroup)) {
      return BaseResult.failed("密集架组不存在");
    }
    if (!Objects.equals(orgRackGroup.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该密集架不属于当前机构");
    }
    // todo: 检查密集架上是否存放有档案
    long deleteAt = System.currentTimeMillis();
    rackGroupMapper.deleteById(id, deleteAt);
    rackLayerMapper.deleteByRackGroupId(id);
    rackGridMapper.deleteByRackGroupId(id);
    return BaseResult.success();
  }

  @Override
  public BaseResult controlRackGroup(RackGroupControlRequest request) {
    return BaseResult.success();
  }

  /** 密集架容量更新的内部实现 */
  private void updateRackUsedCapacityInternal(Integer rackGroupNo) {
    List<String> shelvingStatusList =
        List.of(
            ArchiveShelvingStatusEnum.IN.getValue(),
            ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    List<RackGroup> rackGroupList = rackGroupMapper.selectListForStatTask(rackGroupNo);
    rackGroupList.forEach(rackGroup -> rackGroup.setUsedCapacity(0));
    for (RackGroup rackGroup : rackGroupList) {
      Long id = rackGroup.getId();
      Integer groupNo = rackGroup.getGroupNo();
      List<RackLayer> rackLayerList = rackLayerMapper.selectListByRackGroupIdForStatTask(id);
      rackLayerList.forEach(rackLayer -> rackLayer.setUsedCapacity(0));
      Map<String, RackLayer> rackLayerMap =
          rackLayerList.stream()
              .collect(
                  Collectors.toMap(
                      l ->
                          l.getColumnNo()
                              + "_"
                              + l.getPanelNo()
                              + "_"
                              + l.getSectionNo()
                              + "_"
                              + l.getLayerNo(),
                      l -> l,
                      (l, l2) -> l));
      List<RackGrid> rackGridList = rackGridMapper.selectListByRackGroupIdForStatTask(id);
      rackGridList.forEach(rackGrid -> rackGrid.setUsedCapacity(0));
      Map<String, RackGrid> rackGridMap =
          rackGridList.stream()
              .collect(
                  Collectors.toMap(
                      g -> g.getRackLayerId() + "_" + g.getGridNo(), r -> r, (r, r2) -> r));
      List<Archive> archiveList =
          archiveMapper.selectListByGroupNoForStatTask(shelvingStatusList, groupNo, null, null);
      // 统计每个位置上的档案数量
      for (Archive archive : archiveList) {
        // 累加组已用容量
        rackGroup.setUsedCapacity(rackGroup.getUsedCapacity() + 1);
        // 累加层位已用容量
        RackLayer rackLayer =
            rackLayerMap.get(
                archive.getRackColumnNo()
                    + "_"
                    + archive.getRackPanelNo()
                    + "_"
                    + archive.getRackSectionNo()
                    + "_"
                    + archive.getRackLayerNo());
        if (Objects.isNull(rackLayer)) {
          continue;
        }
        rackLayer.setUsedCapacity(rackLayer.getUsedCapacity() + 1);
        // 累加格口已用容量
        RackGrid rackGrid = rackGridMap.get(rackLayer.getId() + "_" + archive.getRackGridNo());
        if (Objects.isNull(rackGrid)) {
          continue;
        }
        rackGrid.setUsedCapacity(rackGrid.getUsedCapacity() + 1);
      }
      // 更新数据库
      if (CollUtil.isNotEmpty(rackLayerList)) {
        rackLayerMapper.updateUsedCapacityForStatTask(rackLayerList);
      }
      if (CollUtil.isNotEmpty(rackGridList)) {
        // 分批更新，避免数据量过大导致性能问题
        for (int i = 0; i < rackGridList.size(); i += GlobalConstant.BATCH_SQL_MAX_SIZE) {
          int endIndex = Math.min(i + GlobalConstant.BATCH_SQL_MAX_SIZE, rackGridList.size());
          List<RackGrid> batch = rackGridList.subList(i, endIndex);
          rackGridMapper.updateUsedCapacityForStatTask(batch);
        }
      }
    }
    if (CollUtil.isNotEmpty(rackGroupList)) {
      rackGroupMapper.updateUsedCapacityForStatTask(rackGroupList);
    }
    log.info(
        "密集架容量更新完成，组编号：{}",
        Arrays.toString(rackGroupList.stream().map(RackGroup::getGroupNo).toArray()));
  }
}
