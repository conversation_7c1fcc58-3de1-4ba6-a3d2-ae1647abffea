package org.zxwl.smart.service.rack;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.rack.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.rack.*;

@Validated
public interface RackGroupService {

  /**
   * 异步执行密集架容量更新，带防抖功能
   *
   * @param rackGroupNo 密集架组编号
   */
  void updateRackUsedCapacityAsync(Integer rackGroupNo);

  ListResult<RackVendorVO> getRackVendorList();

  ListResult<RackTypeVO> getRackTypeList();

  PageResult<RackGroupListVO> getRackGroupList(@NotNull @Valid RackGroupListRequest request);

  ListResult<RackGroupSelectVO> getRackGroupListForSelect(
      @NotNull @Valid RackGroupListForSelectRequest request);

  SimpleResult<RackGroupVO> getRackGroup(@NotNull @Valid RackGroupGetRequest request);

  SimpleResult<BoxRackGroupVO> getBoxRackGroup(@NotNull @Valid BoxRackGroupGetRequest request);

  SimpleResult<BaseCreateVO> createRackGroup(@NotNull @Valid RackGroupCreateRequest request);

  BaseResult updateRackGroup(@NotNull @Valid RackGroupUpdateRequest request);

  BaseResult deleteRackGroup(@NotNull @Valid RackGroupDeleteRequest request);

  BaseResult controlRackGroup(@NotNull @Valid RackGroupControlRequest request);
}
