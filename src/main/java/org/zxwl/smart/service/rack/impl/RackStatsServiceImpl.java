package org.zxwl.smart.service.rack.impl;

import cn.hutool.core.date.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.constant.enums.archive.ArchiveOperatorTypeEnum;
import org.zxwl.smart.constant.enums.archive.ArchiveShelvingStatusEnum;
import org.zxwl.smart.domain.request.rack.RackStatRequest;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.rack.RackStatVO;
import org.zxwl.smart.mybatis.ext.archive.ArchiveShelvingStatusStat;
import org.zxwl.smart.mybatis.ext.archive.OperatorStat;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveOperatorRecordMapper;
import org.zxwl.smart.service.rack.RackStatsService;

@Service
@AllArgsConstructor
public class RackStatsServiceImpl implements RackStatsService {

  private final ArchiveMapper archiveMapper;
  private final ArchiveOperatorRecordMapper archiveOperatorRecordMapper;

  @Override
  public SimpleResult<RackStatVO> getRackStat(RackStatRequest request) {
    Integer groupNo = request.getGroupNo();
    // 查询在架和不在架数量
    List<ArchiveShelvingStatusStat> archiveStatList =
        archiveMapper.selectShelvingStatusStat(null, groupNo, null, null);
    Map<String, ArchiveShelvingStatusStat> archiveStatMap =
        archiveStatList.stream()
            .collect(Collectors.toMap(ArchiveShelvingStatusStat::getShelvingStatus, v -> v));
    ArchiveShelvingStatusStat archiveInShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.IN.getValue());
    ArchiveShelvingStatusStat archiveOutShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.OUT.getValue());
    ArchiveShelvingStatusStat archivePendingInShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.PENDING_IN.getValue());
    ArchiveShelvingStatusStat archivePendingOutShelving =
        archiveStatMap.get(ArchiveShelvingStatusEnum.PENDING_OUT.getValue());
    // 查询今日上/下架
    List<OperatorStat> todayOperatorStatList =
        archiveOperatorRecordMapper.selectStatByOperatorType(
            null, groupNo, null, null, DateUtil.beginOfDay(new Date()), null);
    Map<String, OperatorStat> todayOperatorStatMap =
        todayOperatorStatList.stream()
            .collect(Collectors.toMap(OperatorStat::getOperatorType, v -> v));
    OperatorStat todayOperatorShelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.SHELVE.getValue());
    OperatorStat todayOperatorUnshelve =
        todayOperatorStatMap.get(ArchiveOperatorTypeEnum.UNSHELVE.getValue());
    RackStatVO vo = new RackStatVO();
    vo.setInShelvingCount(Objects.nonNull(archiveInShelving) ? archiveInShelving.getCount() : 0);
    vo.setOutShelvingCount(Objects.nonNull(archiveOutShelving) ? archiveOutShelving.getCount() : 0);
    vo.setPendingInShelvingCount(
        Objects.nonNull(archivePendingInShelving) ? archivePendingInShelving.getCount() : 0);
    vo.setPendingOutShelvingCount(
        Objects.nonNull(archivePendingOutShelving) ? archivePendingOutShelving.getCount() : 0);
    vo.setTodayShelveCount(
        Objects.nonNull(todayOperatorShelve) ? todayOperatorShelve.getCount() : 0);
    vo.setTodayUnshelveCount(
        Objects.nonNull(todayOperatorUnshelve) ? todayOperatorUnshelve.getCount() : 0);
    return new SimpleResult<>(vo);
  }
}
