package org.zxwl.smart.service.rack;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.rack.RackPanelListRequest;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.rack.RackPanelVO;

@Validated
public interface RackPanelService {

  ListResult<RackPanelVO> getRackPanelList(@NotNull @Valid RackPanelListRequest request);
}
