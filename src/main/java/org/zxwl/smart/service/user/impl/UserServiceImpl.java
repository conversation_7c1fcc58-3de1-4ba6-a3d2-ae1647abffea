package org.zxwl.smart.service.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.UserCache;
import org.zxwl.smart.cache.model.JwtToken;
import org.zxwl.smart.cache.model.JwtTokenSubject;
import org.zxwl.smart.cache.model.UserDetail;
import org.zxwl.smart.common.convert.user.UserConvert;
import org.zxwl.smart.common.utils.JwtTokenUtil;
import org.zxwl.smart.common.utils.PwdUtil;
import org.zxwl.smart.common.utils.ResponseUtil;
import org.zxwl.smart.constant.consist.AuthorizeConstant;
import org.zxwl.smart.domain.request.user.UserLoginRequest;
import org.zxwl.smart.domain.request.user.UserOrganizationSwitchRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.UserLoginVO;
import org.zxwl.smart.mybatis.entity.cabinet.CabinetGroup;
import org.zxwl.smart.mybatis.entity.cabinet.HrCabinetGroup;
import org.zxwl.smart.mybatis.entity.rack.RackGroup;
import org.zxwl.smart.mybatis.entity.user.Organization;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.HrCabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationUserMapper;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.service.user.UserService;

@Slf4j
@Service
@AllArgsConstructor
public class UserServiceImpl implements UserService {

  private final UserCache userCache;
  private final UserMapper userMapper;
  private final CabinetGroupMapper cabinetGroupMapper;
  private final HrCabinetGroupMapper hrCabinetGroupMapper;
  private final RackGroupMapper rackGroupMapper;
  private final OrganizationMapper organizationMapper;
  private final OrganizationUserMapper organizationUserMapper;

  @Override
  public JwtToken refreshJwtTokenCache(Long userId, Long organizationId, String terminal) {
    JwtTokenSubject subject = new JwtTokenSubject(userId, organizationId, terminal);
    JwtToken jwtToken = JwtTokenUtil.createJwtToken(subject);
    userCache.cacheJwtToken(userId, organizationId, terminal, jwtToken);
    return jwtToken;
  }

  @Override
  public void refreshUserCacheIfNeed(Long userId, Date expiredDate) {
    if (Objects.isNull(userId)) {
      return;
    }
    UserDetail userDetail = userCache.getUserDetail(userId);
    if (Objects.isNull(userDetail)) {
      // 从数据库查询需要信息，放入缓存
      User user = userMapper.selectById(userId);
      userDetail = new UserDetail();
      userDetail.setUserId(user.getId());
      userDetail.setAdminFlag(user.getAdminFlag());
      userDetail.setExpiredDate(expiredDate);
      userCache.cacheUserDetail(userId, userDetail);
    }
    // 将必要信息放入线程缓存
    AccountLocal.setAdminFlag(userDetail.getAdminFlag());
  }

  @Override
  public BaseResult switchUserOrganization(
      UserOrganizationSwitchRequest request, HttpServletResponse response) {
    boolean adminFlag = AccountLocal.getAdminFlag();
    String terminal = AccountLocal.getTerminal();
    Long userId = AccountLocal.getUserId();
    Long organizationId = request.getOrganizationId();
    if (Objects.equals(organizationId, AccountLocal.getOrganizationId())) {
      // 机构没变直接返回成功
      return BaseResult.success();
    }
    Organization organization = organizationMapper.selectById(organizationId);
    if (Objects.isNull(organization)) {
      return BaseResult.failed("机构不存在");
    }
    if (!adminFlag
        && organizationUserMapper.existByOrganizationIdAndUserId(userId, organizationId) == 0) {
      return BaseResult.failed("当前用户不在该机构下，不可切换到该机构");
    }
    // 生成 jwt token，放入缓存并写到响应头里面去
    JwtToken jwtToken = refreshJwtTokenCache(userId, organizationId, terminal);
    String token = jwtToken.getToken();
    ResponseUtil.addHeader(response, AuthorizeConstant.HEADER_AUTHORIZE_KEY, token, true);
    return BaseResult.success();
  }

  @Override
  public SimpleResult<UserLoginVO> login(UserLoginRequest request, HttpServletResponse response) {
    String terminal = AccountLocal.getTerminal();
    Long id = request.getId();
    Integer cabinGroupNo = request.getCabinGroupNo();
    Integer hrCabinetGroupNo = request.getHrCabinetGroupNo();
    Integer rackGroupNo = request.getRackGroupNo();
    Long organizationId = request.getOrganizationId();
    String userAccount = request.getUserAccount();
    String userPwd = request.getUserPwd();
    String idCard = request.getIdCard();
    // 用户信息判断
    User user;
    if (Objects.nonNull(id)) {
      // 人脸登录/指纹登录
      user = userMapper.selectById(id);
      if (Objects.isNull(user)) {
        return new SimpleResult<>("用户不存在");
      }
    } else if (StrUtil.isNotBlank(idCard)) {
      // idCard登录
      user = userMapper.selectByUserAccountOrIdCard(null, idCard);
      if (Objects.isNull(user)) {
        return new SimpleResult<>("用户不存在");
      }
    } else if (StrUtil.isNotBlank(userAccount)) {
      // 账户登录
      if (StrUtil.isBlank(userPwd)) {
        return new SimpleResult<>("密码不能为空");
      }
      user = userMapper.selectByUserAccountOrIdCard(userAccount, null);
      if (Objects.isNull(user)) {
        return new SimpleResult<>("用户不存在");
      }
      if (StrUtil.isNotBlank(userPwd) && !PwdUtil.checkpw(userPwd, user.getUserPwd())) {
        return new SimpleResult<>("密码错误");
      }
    } else {
      return new SimpleResult<>("账号不能为空");
    }
    // 智能载具信息验证
    if (Objects.nonNull(cabinGroupNo)) {
      // 智能柜登录
      CabinetGroup cabinetGroup = cabinetGroupMapper.selectByGroupNo(cabinGroupNo);
      if (Objects.isNull(cabinetGroup)) {
        return new SimpleResult<>("柜组不存在，组号为" + cabinGroupNo);
      }
      organizationId = cabinetGroup.getOrganizationId();
    } else if (Objects.nonNull(hrCabinetGroupNo)) {
      // 人事柜登录
      HrCabinetGroup hrCabinetGroup = hrCabinetGroupMapper.selectByGroupNo(hrCabinetGroupNo);
      if (Objects.isNull(hrCabinetGroup)) {
        return new SimpleResult<>("人事柜组不存在，组号为" + hrCabinetGroupNo);
      }
      organizationId = hrCabinetGroup.getOrganizationId();
    } else if (Objects.nonNull(rackGroupNo)) {
      // 密集架登录
      RackGroup rackGroup = rackGroupMapper.selectByGroupNo(rackGroupNo);
      if (Objects.isNull(rackGroup)) {
        return new SimpleResult<>("密集架组不存在，组号为" + rackGroupNo);
      }
      organizationId = rackGroup.getOrganizationId();
    }
    // 机构信息验证
    if (Objects.isNull(organizationId)) {
      Long userId = user.getAdminFlag() ? null : user.getId();
      List<Organization> organizationList = organizationMapper.selectListByUserId(userId);
      if (CollUtil.isEmpty(organizationList)) {
        return new SimpleResult<>("该用户没有关联任何机构");
      }
      organizationId = organizationList.get(0).getId();
    } else {
      // 验证机构和登录人的关系
      Organization organization = organizationMapper.selectById(organizationId);
      if (Objects.isNull(organization)) {
        return new SimpleResult<>("机构不存在");
      }
      // 非管理员才需要验证，管理员不需要
      if (!user.getAdminFlag()
          && organizationUserMapper.existByOrganizationIdAndUserId(organizationId, user.getId())
              == 0) {
        return new SimpleResult<>("该用户不在所选机构或者智能载具所属机构下");
      }
    }
    // 生成 jwt token，放入缓存并写到响应头里面去
    Long userId = user.getId();
    JwtToken jwtToken = refreshJwtTokenCache(userId, organizationId, terminal);
    String token = jwtToken.getToken();
    ResponseUtil.addHeader(response, AuthorizeConstant.HEADER_AUTHORIZE_KEY, token, true);
    UserLoginVO vo = UserConvert.getUserLoginVO(user);
    vo.setOrganizationId(organizationId);
    return new SimpleResult<>(vo);
  }

  @Override
  public BaseResult logout() {
    Long organizationId = AccountLocal.getOrganizationId();
    String terminal = AccountLocal.getTerminal();
    Long userId = AccountLocal.getUserId();
    userCache.cleanJwtToken(userId, organizationId, terminal);
    return BaseResult.success();
  }
}
