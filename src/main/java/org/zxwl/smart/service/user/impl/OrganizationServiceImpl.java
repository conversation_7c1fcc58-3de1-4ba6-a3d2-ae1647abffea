package org.zxwl.smart.service.user.impl;

import cn.hutool.core.util.StrUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.common.convert.user.OrganizationConvert;
import org.zxwl.smart.common.utils.ColUtil;
import org.zxwl.smart.constant.consist.UserConstant;
import org.zxwl.smart.domain.request.user.OrganizationCreateRequest;
import org.zxwl.smart.domain.request.user.OrganizationDeleteRequest;
import org.zxwl.smart.domain.request.user.OrganizationListRequest;
import org.zxwl.smart.domain.request.user.OrganizationUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.OrganizationVO;
import org.zxwl.smart.mybatis.entity.user.Organization;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveMapper;
import org.zxwl.smart.mybatis.mapper.archive.ArchiveTypeMapper;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationUserMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.user.OrganizationService;

@Service
@AllArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

  private final OrganizationMapper organizationMapper;
  private final OrganizationUserMapper organizationUserMapper;
  private final CabinetGroupMapper cabinetGroupMapper;
  private final RackGroupMapper rackGroupMapper;
  private final ArchiveTypeMapper archiveTypeMapper;
  private final WarehouseMapper warehouseMapper;
  private final ArchiveMapper archiveMapper;

  @Override
  public int generateDefaultOrganizationIfNeed() {
    if (organizationMapper.count() == 0) {
      Organization organization = new Organization();
      organization.setOrganizationNo(UserConstant.DEFAULT_ORGANIZATION_NO);
      organization.setOrganizationName(UserConstant.DEFAULT_ORGANIZATION_NAME);
      organization.setLevel(UserConstant.ROOT_ORGANIZATION_LEVEL);
      return organizationMapper.insert(organization);
    }
    return 0;
  }

  @Override
  public ListResult<OrganizationVO> getOrganizationList(OrganizationListRequest request) {
    boolean adminFlag = AccountLocal.getAdminFlag();
    Long userId = AccountLocal.getUserId();
    List<Organization> organizationList = organizationMapper.selectList();
    Set<Long> inOrganizationIdSet = organizationUserMapper.selectOrganizationIdSetByUserId(userId);
    List<OrganizationVO> voList = OrganizationConvert.getOrganizationVOList(organizationList);
    Map<Long, List<OrganizationVO>> map = ColUtil.groupingBy(voList, OrganizationVO::getParentId);
    for (OrganizationVO vo : voList) {
      vo.setInOrganization(adminFlag || inOrganizationIdSet.contains(vo.getId()));
      vo.setChildren(map.get(vo.getId()));
    }
    List<OrganizationVO> rootVoList =
        voList.stream().filter(v -> Objects.isNull(v.getParentId())).collect(Collectors.toList());
    return new ListResult<>(rootVoList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createOrganization(OrganizationCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long parentId = request.getParentId();
    String organizationNo = request.getOrganizationNo();
    String organizationName = request.getOrganizationName();
    Organization parent = null;
    if (Objects.nonNull(parentId)) {
      parent = organizationMapper.selectById(parentId);
      if (Objects.isNull(parent)) {
        return new SimpleResult<>("上级机构不存在");
      }
      if (parent.getLevel() >= UserConstant.MAX_ORGANIZATION_LEVEL) {
        return new SimpleResult<>("机构不能超过" + UserConstant.MAX_ORGANIZATION_LEVEL + "级");
      }
    }
    if (organizationMapper.existByOrganizationNo(organizationNo) > 0) {
      return new SimpleResult<>("机构编号已存在");
    }
    if (organizationMapper.existByOrganizationName(organizationName) > 0) {
      return new SimpleResult<>("机构名称已存在");
    }
    if (organizationMapper.count() > UserConstant.MAX_ORGANIZATION_COUNT) {
      return new SimpleResult<>("机构已达到" + UserConstant.MAX_ORGANIZATION_COUNT + "个最大数量");
    }
    Organization organization = OrganizationConvert.getOrganization(request);
    organization.setLevel(
        Objects.nonNull(parent) ? parent.getLevel() + 1 : UserConstant.ROOT_ORGANIZATION_LEVEL);
    organization.setCreatedId(userId);
    organization.setUpdatedId(userId);
    organizationMapper.insert(organization);
    return new SimpleResult<>(new BaseCreateVO(organization.getId()));
  }

  @Override
  public BaseResult updateOrganization(OrganizationUpdateRequest request) {
    boolean adminFlag = AccountLocal.getAdminFlag();
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    String organizationNo = request.getOrganizationNo();
    String organizationName = request.getOrganizationName();
    Organization orgOrganization = organizationMapper.selectById(id);
    if (Objects.isNull(orgOrganization)) {
      return BaseResult.failed("机构不存在");
    }
    if (!adminFlag && !Objects.equals(organizationId, id)) {
      return BaseResult.failed("当前用户不在该机构下，不可编辑该机构");
    }
    if (!StrUtil.equals(orgOrganization.getOrganizationNo(), organizationNo)) {
      if (organizationMapper.existByOrganizationNo(organizationNo) > 0) {
        return BaseResult.failed("机构编号已存在");
      }
    }
    if (!StrUtil.equals(orgOrganization.getOrganizationName(), organizationName)) {
      if (organizationMapper.existByOrganizationName(organizationName) > 0) {
        return BaseResult.failed("机构名称已存在");
      }
    }
    Organization organization = OrganizationConvert.getOrganization(request);
    organization.setUpdatedId(userId);
    organizationMapper.updateById(organization);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteOrganization(OrganizationDeleteRequest request) {
    boolean adminFlag = AccountLocal.getAdminFlag();
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    Organization orgOrganization = organizationMapper.selectById(id);
    if (Objects.isNull(orgOrganization)) {
      return BaseResult.failed("机构不存在");
    }
    if (Objects.equals(organizationId, id)) {
      return BaseResult.failed("不可删除当前所在机构");
    }
    if (!adminFlag && organizationUserMapper.existByOrganizationIdAndUserId(id, userId) == 0) {
      return BaseResult.failed("当前用户不在该机构下，不可删除该机构");
    }
    if (organizationMapper.exitByParentId(id) > 0) {
      return BaseResult.failed("该机构存在子机构，不能直接删除");
    }
    if (organizationMapper.count() == 1) {
      return BaseResult.failed("不能删除最后一个机构");
    }
    if (cabinetGroupMapper.existByOrganizationId(id) > 0) {
      return BaseResult.failed("该机构下存在智能柜组，不能直接删除");
    }
    if (rackGroupMapper.existByOrganizationId(id) > 0) {
      return BaseResult.failed("该机构下存在密集架组，不能直接删除");
    }
    if (archiveTypeMapper.existByOrganizationId(id) > 0) {
      return BaseResult.failed("该机构下存在档案门类，不能直接删除");
    }
    if (warehouseMapper.existByOrganizationId(id) > 0) {
      return BaseResult.failed("该机构下存在库房，不能直接删除");
    }
    if (archiveMapper.existByOrganizationId(id) > 0) {
      return BaseResult.failed("该机构下存在档案，不能直接删除");
    }
    organizationMapper.deleteById(id, System.currentTimeMillis());
    return BaseResult.success();
  }
}
