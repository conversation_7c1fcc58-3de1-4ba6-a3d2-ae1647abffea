package org.zxwl.smart.service.user;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.user.EmployeeCreateRequest;
import org.zxwl.smart.domain.request.user.EmployeeDeleteRequest;
import org.zxwl.smart.domain.request.user.EmployeeListRequest;
import org.zxwl.smart.domain.request.user.EmployeeUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.EmployeeVO;

@Validated
public interface EmployeeService {

  int generateDefaultEmployeeIfNeed();

  PageResult<EmployeeVO> getEmployeeList(@NotNull @Valid EmployeeListRequest request);

  SimpleResult<BaseCreateVO> createEmployee(@NotNull @Valid EmployeeCreateRequest request);

  BaseResult updateEmployee(@NotNull @Valid EmployeeUpdateRequest request);

  BaseResult deleteEmployee(@NotNull @Valid EmployeeDeleteRequest request);
}
