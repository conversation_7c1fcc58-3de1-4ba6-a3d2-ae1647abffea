package org.zxwl.smart.service.user;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.cache.model.JwtToken;
import org.zxwl.smart.domain.request.user.UserLoginRequest;
import org.zxwl.smart.domain.request.user.UserOrganizationSwitchRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.UserLoginVO;

@Validated
public interface UserService {

  /**
   * 刷新 jwt token 相关缓存
   *
   * @param userId 用户id
   * @param organizationId 机构id
   * @param terminal 终端标识
   * @return 新生成的 jwt token
   */
  JwtToken refreshJwtTokenCache(Long userId, Long organizationId, String terminal);

  /**
   * 在有必要的情况下刷新用户相关缓存（用户 jwtToken、userDetails）
   *
   * @param userId 用户id
   * @param expiredDate 缓存过期时间
   */
  void refreshUserCacheIfNeed(Long userId, Date expiredDate);

  BaseResult switchUserOrganization(
      @NotNull @Valid UserOrganizationSwitchRequest request, HttpServletResponse response);

  SimpleResult<UserLoginVO> login(
      @NotNull @Valid UserLoginRequest request, HttpServletResponse response);

  BaseResult logout();
}
