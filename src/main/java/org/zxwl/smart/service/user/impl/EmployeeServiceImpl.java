package org.zxwl.smart.service.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.UserCache;
import org.zxwl.smart.common.convert.user.UserConvert;
import org.zxwl.smart.common.utils.OSSUtil;
import org.zxwl.smart.common.utils.PwdUtil;
import org.zxwl.smart.constant.consist.GlobalConstant;
import org.zxwl.smart.constant.consist.UserConstant;
import org.zxwl.smart.constant.enums.user.UserSexEnum;
import org.zxwl.smart.constant.enums.user.UserStatusEnum;
import org.zxwl.smart.domain.request.user.EmployeeCreateRequest;
import org.zxwl.smart.domain.request.user.EmployeeDeleteRequest;
import org.zxwl.smart.domain.request.user.EmployeeListRequest;
import org.zxwl.smart.domain.request.user.EmployeeUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.EmployeeVO;
import org.zxwl.smart.mybatis.entity.sys.Role;
import org.zxwl.smart.mybatis.entity.sys.UserRole;
import org.zxwl.smart.mybatis.entity.user.Organization;
import org.zxwl.smart.mybatis.entity.user.OrganizationUser;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.mapper.sys.RoleMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationUserMapper;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.mybatis.mapper.user.UserRoleMapper;
import org.zxwl.smart.service.user.EmployeeService;

@Slf4j
@Service
@AllArgsConstructor
public class EmployeeServiceImpl implements EmployeeService {

  private final UserMapper userMapper;
  private final UserRoleMapper userRoleMapper;
  private final RoleMapper roleMapper;
  private final OrganizationMapper organizationMapper;
  private final OrganizationUserMapper organizationUserMapper;
  private final UserCache userCache;

  @Override
  public int generateDefaultEmployeeIfNeed() {
    if (userMapper.count() <= 0) {
      User user = new User();
      user.setAdminFlag(true);
      user.setUserStatus(UserStatusEnum.NORMAL.getValue());
      user.setUserSex(UserSexEnum.MALE.getValue());
      user.setUserName(UserConstant.ADMIN_DEFAULT_USER_NAME);
      user.setUserAccount(UserConstant.ADMIN_DEFAULT_USER_ACCOUNT);
      user.setUserPwd(PwdUtil.encrypt(UserConstant.ADMIN_DEFAULT_USER_PWD));
      return userMapper.insert(user);
    }
    return 0;
  }

  @Override
  public PageResult<EmployeeVO> getEmployeeList(EmployeeListRequest request) {
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    String userNameLike = request.getUserNameLike();
    Date updatedAfter = request.getUpdatedAfter();
    PageHelper.startPage(pageNum, pageSize);
    List<User> userList = userMapper.selectList(userNameLike, updatedAfter);
    List<EmployeeVO> voList = new ArrayList<>();
    for (User user : userList) {
      EmployeeVO vo = UserConvert.getEmployeeVO(user);
      // todo：信息补充
      vo.setFaceId(OSSUtil.getFileUrl(user.getFaceId()));
      Set<Long> organizationIdSet =
          organizationUserMapper.selectOrganizationIdSetByUserId(user.getId());
      if (CollUtil.isNotEmpty(organizationIdSet)) {
        for (Long organizationId : organizationIdSet) {
          Organization organization = organizationMapper.selectById(organizationId);
          if (Objects.nonNull(organization)) {
            vo.setOrganizationName(organization.getOrganizationName());
          }
          List<Long> roleIdList =
              userRoleMapper.selectRoleIdListByUserIdAndOrgId(user.getId(), organizationId);
          if (CollUtil.isNotEmpty(roleIdList)) {
            List<Role> roleList = roleMapper.selectByIdList(roleIdList);
            if (CollUtil.isNotEmpty(roleList)) {
              vo.setRoleIdList(roleIdList);
              vo.setRoleNameList(roleList.stream().map(Role::getRoleName).toList());
            }
          }
        }
      }
      voList.add(vo);
    }
    return new PageResult<>(voList, userList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createEmployee(EmployeeCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    String userAccount = request.getUserAccount();
    String userPwd = request.getUserPwd();
    // todo: 检查机构、部门、角色是否存在
    if (userMapper.existByUserAccount(userAccount) > 0) {
      return new SimpleResult<>("用户账号已存在");
    }
    if (userMapper.existByIdCard(request.getIdCard()) > 0) {
      return new SimpleResult<>("卡号已存在");
    }
    User user = UserConvert.getUser(request);
    user.setAdminFlag(false);
    user.setUserPwd(PwdUtil.encrypt(userPwd));
    user.setFaceId(OSSUtil.getFilePath(request.getFaceId()));
    user.setCreatedId(userId);
    user.setUpdatedId(userId);
    userMapper.insert(user);
    return new SimpleResult<>(new BaseCreateVO(user.getId()));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public BaseResult updateEmployee(EmployeeUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    String userAccount = request.getUserAccount();
    String userPwd = request.getUserPwd();
    List<Long> roleIdList = request.getRoleIdList();
    List<Long> organizationIdList = request.getOrganizationIdList();
    // todo: 检查部门是否存在
    User orgUser = userMapper.selectById(id);
    if (Objects.isNull(orgUser)) {
      return BaseResult.failed("用户不存在");
    }
    if (!StrUtil.equals(orgUser.getUserAccount(), userAccount)) {
      if (userMapper.existByUserAccount(userAccount) > 0) {
        return BaseResult.failed("用户账号已存在");
      }
    }
    if (!StrUtil.equals(orgUser.getIdCard(), request.getIdCard())) {
      if (userMapper.existByIdCard(request.getIdCard()) > 0) {
        return BaseResult.failed("卡号已存在");
      }
    }
    // 批量验证角色是否存在
    if (CollUtil.isNotEmpty(roleIdList)) {
      List<Role> roleList = roleMapper.selectByIdList(roleIdList);
      if (roleList.size() != roleIdList.size()) {
        return BaseResult.failed("部分角色不存在");
      }
    }
    if (CollUtil.isNotEmpty(organizationIdList)) {
      List<Organization> organizationList = organizationMapper.selectByIdList(organizationIdList);
      if (organizationList.size() != organizationIdList.size()) {
        return BaseResult.failed("部分机构不存在");
      }
    }
    assignOrganization(orgUser.getId(), organizationIdList);
    assignRole(orgUser.getId(), organizationId, roleIdList);
    User user = UserConvert.getUser(request);
    if (!StrUtil.equals(userPwd, orgUser.getUserPwd())) {
      user.setUserPwd(PwdUtil.encrypt(userPwd));
    } else {
      user.setUserPwd(userPwd);
    }
    user.setFaceId(OSSUtil.getFilePath(request.getFaceId()));
    user.setUpdatedId(userId);
    userMapper.updateById(user);
    // 清除对应用户权限缓存
    userCache.clearUserPermissionCache(orgUser.getId(), organizationId);
    // 如果用户的机构发生了变化，还需要清除对应机构的权限缓存
    if (Objects.nonNull(organizationIdList)) {
      for (Long orgId : organizationIdList) {
        userCache.clearUserPermissionCache(orgUser.getId(), orgId);
      }
    }
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteEmployee(EmployeeDeleteRequest request) {
    Long id = request.getId();
    User orgUser = userMapper.selectById(id);
    if (Objects.isNull(orgUser)) {
      return BaseResult.failed("用户不存在");
    }
    if (orgUser.getAdminFlag()) {
      return BaseResult.failed("管理员账户不能删除");
    }
    userMapper.deleteById(id, System.currentTimeMillis());
    return BaseResult.success();
  }

  private void assignRole(Long userId, Long organizationId, List<Long> roleIdList) {
    // 删除原有的用户-角色关联
    userRoleMapper.deleteByUserIdAndOrgId(userId, organizationId);
    if (Objects.nonNull(roleIdList)) {
      // 分批处理，避免SQL过长，创建新的用户-角色关联
      for (int i = 0; i < roleIdList.size(); i += GlobalConstant.BATCH_SQL_MAX_SIZE) {
        int endIndex = Math.min(i + GlobalConstant.BATCH_SQL_MAX_SIZE, roleIdList.size());
        List<Long> batch = roleIdList.subList(i, endIndex);
        List<UserRole> userRoleList =
            batch.stream()
                .map(
                    roleId -> {
                      UserRole userRole = new UserRole();
                      userRole.setUserId(userId);
                      userRole.setRoleId(roleId);
                      userRole.setOrganizationId(organizationId);
                      userRole.setCreatedId(AccountLocal.getUserId());
                      return userRole;
                    })
                .toList();
        userRoleMapper.insertBatch(userRoleList);
      }
    }
  }

  private void assignOrganization(Long userId, List<Long> organizationIdList) {
    // 删除原有的用户-机构关联
    organizationUserMapper.deleteByUserId(userId);
    if (CollUtil.isNotEmpty(organizationIdList)) {
      // 批量新增用户-机构关联
      List<OrganizationUser> organizationUserList =
          organizationIdList.stream()
              .map(
                  organizationId -> {
                    OrganizationUser organizationUser = new OrganizationUser();
                    organizationUser.setUserId(userId);
                    organizationUser.setOrganizationId(organizationId);
                    return organizationUser;
                  })
              .toList();
      organizationUserMapper.insertBatch(organizationUserList);
    }
  }
}
