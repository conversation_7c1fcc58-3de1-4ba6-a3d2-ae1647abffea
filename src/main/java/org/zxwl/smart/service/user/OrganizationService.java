package org.zxwl.smart.service.user;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.user.OrganizationCreateRequest;
import org.zxwl.smart.domain.request.user.OrganizationDeleteRequest;
import org.zxwl.smart.domain.request.user.OrganizationListRequest;
import org.zxwl.smart.domain.request.user.OrganizationUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.OrganizationVO;

@Validated
public interface OrganizationService {

  int generateDefaultOrganizationIfNeed();

  ListResult<OrganizationVO> getOrganizationList(@NotNull @Valid OrganizationListRequest request);

  SimpleResult<BaseCreateVO> createOrganization(@NotNull @Valid OrganizationCreateRequest request);

  BaseResult updateOrganization(@NotNull @Valid OrganizationUpdateRequest request);

  BaseResult deleteOrganization(@NotNull @Valid OrganizationDeleteRequest request);
}
