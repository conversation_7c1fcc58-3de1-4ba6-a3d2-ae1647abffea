package org.zxwl.smart.service.role;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.role.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.role.MenuPermissionVO;
import org.zxwl.smart.domain.response.role.RoleVO;

@Validated
public interface RoleService {

  /** 获取角色列表 */
  ListResult<RoleVO> getRoleList(@NotNull @Valid RoleListRequest request);

  /** 获取角色菜单权限列表 */
  ListResult<MenuPermissionVO> getRolePermissionList(
      @NotNull @Valid RoleMenuPermissionListRequest request);

  /** 创建角色 */
  BaseResult createRole(@NotNull @Valid RoleCreateRequest request);

  /** 更新角色 */
  BaseResult updateRole(@NotNull @Valid RoleUpdateRequest request);

  /** 删除角色 */
  BaseResult deleteRole(@NotNull @Valid RoleDeleteRequest request);
}
