package org.zxwl.smart.service.role.impl;

import cn.hutool.core.collection.CollUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.UserCache;
import org.zxwl.smart.common.convert.permission.PermissionConvert;
import org.zxwl.smart.common.convert.role.RoleConvert;
import org.zxwl.smart.common.convert.sys.MenuConvert;
import org.zxwl.smart.constant.consist.GlobalConstant;
import org.zxwl.smart.domain.request.role.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.permission.PermissionVO;
import org.zxwl.smart.domain.response.role.MenuPermissionVO;
import org.zxwl.smart.domain.response.role.RoleVO;
import org.zxwl.smart.domain.response.sys.MenuVO;
import org.zxwl.smart.mybatis.entity.sys.*;
import org.zxwl.smart.mybatis.mapper.sys.*;
import org.zxwl.smart.mybatis.mapper.user.UserRoleMapper;
import org.zxwl.smart.service.role.RoleService;

@Slf4j
@Service
@AllArgsConstructor
public class RoleServiceImpl implements RoleService {

  private final RoleMapper roleMapper;
  private final SysMenuMapper sysMenuMapper;
  private final UserRoleMapper userRoleMapper;
  private final MenuRoleMapper menuRoleMapper;
  private final PermissionMapper permissionMapper;
  private final RolePermissionMapper rolePermissionMapper;
  private final UserCache userCache;

  @Override
  public ListResult<RoleVO> getRoleList(RoleListRequest request) {
    String roleNameLike = request.getRoleNameLike();
    // 获取角色列表
    List<Role> roleList = roleMapper.selectList(roleNameLike);
    // 转换为VO
    List<RoleVO> voList = roleList.stream().map(RoleConvert::getRoleVO).toList();
    return new ListResult<>(voList);
  }

  @Override
  public ListResult<MenuPermissionVO> getRolePermissionList(RoleMenuPermissionListRequest request) {
    Long roleId = request.getRoleId();
    // 获取角色关联的菜单id列表
    List<Long> menuIdList = menuRoleMapper.selectMenuIdListByRoleId(roleId);
    // 获取角色关联的权限id列表
    List<Long> permissionIdList = rolePermissionMapper.selectPermissionIdListByRoleId(roleId);
    // 构建菜单树
    List<MenuVO> menuTree = buildMenuTree(menuIdList);
    // 构建权限树
    List<PermissionVO> permissionTree = buildPermissionTree(permissionIdList);
    // 构建MenuPermissionVO
    List<MenuPermissionVO> menuPermissionVOList = new ArrayList<>();
    MenuPermissionVO vo = new MenuPermissionVO();
    vo.setMenuList(menuTree);
    vo.setPermissionList(permissionTree);
    menuPermissionVOList.add(vo);
    return new ListResult<>(menuPermissionVOList);
  }

  @Override
  public BaseResult createRole(RoleCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    String roleName = request.getRoleName();
    // 检查角色名称是否已存在
    if (roleMapper.selectByRoleName(roleName) > 0) {
      return BaseResult.failed("角色名称已存在");
    }
    // 创建角色
    Role role = RoleConvert.getRole(request);
    role.setCreatedId(userId);
    role.setUpdatedId(userId);
    roleMapper.insert(role);
    return BaseResult.success();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public BaseResult updateRole(RoleUpdateRequest request) {
    Long roleId = request.getId();
    Long userId = AccountLocal.getUserId();
    Long organizationId = AccountLocal.getOrganizationId();
    List<Long> menuIdList = request.getMenuIdList();
    List<Long> permissionIdList = request.getPermissionIdList();
    if (Objects.isNull(roleMapper.selectById(roleId))) {
      return BaseResult.failed("角色不存在");
    }
    if (Objects.nonNull(permissionIdList)) {
      assignPermission(roleId, permissionIdList);
    }
    if (Objects.nonNull(menuIdList)) {
      assignRoleMenu(roleId, menuIdList);
    }
    Role role = RoleConvert.getRole(request);
    role.setUpdatedId(userId);
    roleMapper.update(role);
    // 清除更新的角色对应用户的权限缓存
    List<UserRole> userRoleList = userRoleMapper.selectByRoleId(roleId);
    for (UserRole userRole : userRoleList) {
      userCache.clearUserPermissionCache(userRole.getUserId(), organizationId);
    }
    return BaseResult.success();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public BaseResult deleteRole(RoleDeleteRequest request) {

    List<Long> idList = request.getIdList();
    // 检查角色是否存在
    List<Role> roleList = roleMapper.selectByIdList(idList);
    if (Objects.isNull(roleList)) {
      return BaseResult.failed("该角色不存在");
    }
    for (Role role : roleList) {
      Long id = role.getId();
      // 检查是否是系统角色
      if (role.getSystemFlag()) {
        return BaseResult.failed("系统角色不允许删除");
      }
      // 检查角色是否已分配给用户
      List<UserRole> userRoleList = userRoleMapper.selectByRoleId(id);
      if (Objects.nonNull(userRoleList)) {
        return BaseResult.failed("角色已分配给用户，不允许删除");
      }
    }
    // 删除角色-权限关联
    rolePermissionMapper.deleteByRoleIdList(idList);
    // 删除角色
    roleMapper.deleteByIdList(idList);
    return BaseResult.success();
  }

  private List<MenuVO> buildMenuTree(List<Long> menuIdList) {
    if (CollUtil.isEmpty(menuIdList)) {
      return new ArrayList<>();
    }
    // 查询菜单详情
    List<SysMenu> menuList = sysMenuMapper.selectListByIdList(menuIdList);
    if (Objects.isNull(menuList)) {
      return new ArrayList<>();
    }
    // 转换为MenuVO
    List<MenuVO> menuVOList =
        menuList.stream().map(MenuConvert::getMenuVO).collect(Collectors.toList());
    Map<Long, MenuVO> menuMap =
        menuVOList.stream().collect(Collectors.toMap(MenuVO::getId, menu -> menu));
    List<MenuVO> rootMenuList = new ArrayList<>();
    for (MenuVO menuVO : menuVOList) {
      if (Objects.isNull(menuVO.getParentId()) || menuVO.getParentId() == 0) {
        rootMenuList.add(menuVO);
      } else {
        MenuVO parentMenu = menuMap.get(menuVO.getParentId());
        if (Objects.nonNull(parentMenu)) {
          if (Objects.isNull(parentMenu.getChildren())) {
            parentMenu.setChildren(new ArrayList<>());
          }
          parentMenu.getChildren().add(menuVO);
        } else {
          rootMenuList.add(menuVO);
        }
      }
    }
    sortMenuChildren(rootMenuList);
    return rootMenuList;
  }

  private void sortMenuChildren(List<MenuVO> rootMenuList) {
    if (CollUtil.isEmpty(rootMenuList)) {
      return;
    }
    for (MenuVO menuVO : rootMenuList) {
      if (CollUtil.isNotEmpty(menuVO.getChildren())) {
        menuVO
            .getChildren()
            .sort(
                Comparator.comparing(MenuVO::getOrdinal, Comparator.nullsLast(Integer::compareTo)));
        sortMenuChildren(menuVO.getChildren());
      }
    }
  }

  private List<PermissionVO> buildPermissionTree(List<Long> permissionIdList) {
    if (CollUtil.isEmpty(permissionIdList)) {
      return new ArrayList<>();
    }
    // 查询权限详情
    List<Permission> permissionList = permissionMapper.selectListByIdList(permissionIdList);
    if (Objects.isNull(permissionList)) {
      return new ArrayList<>();
    }
    // 转换为PermissionVO
    List<PermissionVO> permissionVOList =
        permissionList.stream()
            .map(PermissionConvert::getPermissionVO)
            .collect(Collectors.toList());
    permissionVOList.sort(
        Comparator.comparing(PermissionVO::getOrdinal, Comparator.nullsLast(Integer::compareTo)));
    Map<String, PermissionVO> permissionMap =
        permissionVOList.stream()
            .collect(Collectors.toMap(PermissionVO::getPermissionCode, permission -> permission));
    // 构建权限树
    List<PermissionVO> rootPermissionList = new ArrayList<>();
    for (PermissionVO permissionVO : permissionVOList) {
      String parentName = permissionVO.getParentName();
      if (Objects.isNull(parentName) || parentName.trim().isEmpty()) {
        // 根权限
        rootPermissionList.add(permissionVO);
      } else {
        // 子权限，查找父权限
        PermissionVO parentPermission = findParentByName(permissionMap, parentName);
        if (Objects.nonNull(parentPermission)) {
          if (Objects.isNull(parentPermission.getChildren())) {
            parentPermission.setChildren(new ArrayList<>());
          }
          parentPermission.getChildren().add(permissionVO);
        } else {
          rootPermissionList.add(permissionVO);
        }
      }
    }
    sortPermissionChildren(rootPermissionList);
    return rootPermissionList;
  }

  private PermissionVO findParentByName(
      Map<String, PermissionVO> permissionMap, String parentName) {
    return permissionMap.values().stream()
        .filter(permission -> Objects.equals(permission.getPermissionName(), parentName))
        .findFirst()
        .orElse(null);
  }

  private void sortPermissionChildren(List<PermissionVO> rootPermissionList) {
    if (CollUtil.isEmpty(rootPermissionList)) {
      return;
    }
    for (PermissionVO permissionVO : rootPermissionList) {
      if (CollUtil.isNotEmpty(permissionVO.getChildren())) {
        permissionVO
            .getChildren()
            .sort(
                Comparator.comparing(
                    PermissionVO::getOrdinal, Comparator.nullsLast(Integer::compareTo)));
        sortPermissionChildren(permissionVO.getChildren());
      }
    }
  }

  private void assignPermission(Long roleId, List<Long> permissionIdList) {
    // 校验传过来的permissionId是否合法
    BaseResult result = validatePermissionIdList(permissionIdList);
    if (Objects.nonNull(result)) {
      throw new RuntimeException("权限id不合法");
    }
    // 删除原有的角色-权限关联
    rolePermissionMapper.deleteByRoleId(roleId);
    // 分批处理，避免SQL过长
    for (int i = 0; i < permissionIdList.size(); i += GlobalConstant.BATCH_SQL_MAX_SIZE) {
      int endIndex = Math.min(i + GlobalConstant.BATCH_SQL_MAX_SIZE, permissionIdList.size());
      List<Long> batch = permissionIdList.subList(i, endIndex);
      List<RolePermission> rolePermissionList =
          batch.stream()
              .map(
                  permissionId -> {
                    RolePermission rolePermission = new RolePermission();
                    rolePermission.setRoleId(roleId);
                    rolePermission.setPermissionId(permissionId);
                    rolePermission.setCreatedId(AccountLocal.getUserId());
                    return rolePermission;
                  })
              .toList();
      rolePermissionMapper.insertBatch(rolePermissionList);
    }
  }

  private void assignRoleMenu(Long roleId, List<Long> menuIdList) {
    // 校验传过来的menuId是否合法
    BaseResult result = validateMenuIdList(menuIdList);
    if (Objects.nonNull(result)) {
      throw new RuntimeException("菜单id不合法");
    }
    // 删除原有的角色-菜单关联
    menuRoleMapper.deleteByRoleId(roleId);
    // 创建新的角色-菜单关联
    List<MenuRole> menuRoleList =
        menuIdList.stream()
            .map(
                menuId -> {
                  MenuRole menuRole = new MenuRole();
                  menuRole.setRoleId(roleId);
                  menuRole.setMenuId(menuId);
                  return menuRole;
                })
            .toList();
    menuRoleMapper.insertBatch(menuRoleList);
  }

  private BaseResult validatePermissionIdList(List<Long> permissionIdList) {
    if (CollUtil.isEmpty(permissionIdList)) {
      // 空列表不需要校验
      return null;
    }
    // 查询数据库中实际存在的权限id
    List<Long> existingIdList = permissionMapper.selectByIdList(permissionIdList);
    // 检查是否有不存在的权限id
    List<Long> invalidIdList =
        permissionIdList.stream().filter(id -> !existingIdList.contains(id)).toList();
    if (!invalidIdList.isEmpty()) {
      return BaseResult.failed("权限ID不存在: " + invalidIdList);
    }
    // 校验通过
    return null;
  }

  private BaseResult validateMenuIdList(List<Long> menuIdList) {
    if (CollUtil.isEmpty(menuIdList)) {
      return null;
    }
    List<Long> existingIdList = sysMenuMapper.selectByIdList(menuIdList);
    List<Long> invalidIdList =
        menuIdList.stream().filter(id -> !existingIdList.contains(id)).toList();
    if (!invalidIdList.isEmpty()) {
      return BaseResult.failed("菜单ID不存在: " + invalidIdList);
    }
    return null;
  }
}
