package org.zxwl.smart.service.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.config.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.config.ConfigVO;

@Validated
public interface ConfigService {

  ListResult<ConfigVO> getConfigList(@NotNull @Valid ConfigListRequest request);

  BaseResult createConfig(@NotNull @Valid ConfigInsertRequest request);

  BaseResult updateConfig(@NotNull @Valid ConfigUpdateRequest request);

  BaseResult updateServerConfig(@NotNull @Valid ServerConfigUpdateRequest request);

  BaseResult deleteConfigList(ConfigListDeleteRequest request);
}
