package org.zxwl.smart.service.config.impl;

import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.ServerConfigCache;
import org.zxwl.smart.common.convert.config.ConfigConvert;
import org.zxwl.smart.domain.request.config.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.config.ConfigVO;
import org.zxwl.smart.mybatis.entity.sys.SysConfiguration;
import org.zxwl.smart.mybatis.ext.config.ConfigListWrapper;
import org.zxwl.smart.mybatis.mapper.sys.SysConfigurationMapper;
import org.zxwl.smart.service.config.ConfigService;

@Service
@AllArgsConstructor
public class ConfigServiceImpl implements ConfigService {

  private final SysConfigurationMapper sysConfigurationMapper;
  private final TransactionTemplate transactionTemplate;

  @Override
  public ListResult<ConfigVO> getConfigList(ConfigListRequest request) {
    ConfigListWrapper wrapper = ConfigConvert.getConfigListWrapper(request);
    List<SysConfiguration> sysConfigurationList = sysConfigurationMapper.selectList(wrapper);
    List<ConfigVO> voList = sysConfigurationList.stream().map(ConfigConvert::getConfigVO).toList();
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createConfig(ConfigInsertRequest request) {
    Long userId = AccountLocal.getUserId();
    SysConfiguration sysConfiguration = ConfigConvert.getSysConfiguration(request);
    sysConfiguration.setCreatedId(userId);
    sysConfiguration.setUpdatedId(userId);
    transactionTemplate.execute(
        status -> {
          sysConfigurationMapper.insert(sysConfiguration);
          sysConfigurationMapper.updateOtherConfigsToHistory(
              sysConfiguration.getId(),
              sysConfiguration.getConfigType(),
              sysConfiguration.getTerminalCode(),
              userId);
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(sysConfiguration.getId()));
  }

  @Override
  public BaseResult updateConfig(ConfigUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    if (sysConfigurationMapper.existById(id) == 0) {
      return new SimpleResult<>("配置不存在");
    }
    SysConfiguration sysConfiguration = ConfigConvert.getSysConfiguration(request);
    sysConfiguration.setUpdatedId(userId);
    transactionTemplate.execute(
        status -> {
          sysConfigurationMapper.updateById(sysConfiguration);
          sysConfigurationMapper.updateOtherConfigsToHistory(
              sysConfiguration.getId(),
              sysConfiguration.getConfigType(),
              sysConfiguration.getTerminalCode(),
              userId);
          return true;
        });
    return new SimpleResult<>(new BaseCreateVO(sysConfiguration.getId()));
  }

  @Override
  public BaseResult updateServerConfig(ServerConfigUpdateRequest request) {
    boolean hkLogEnabled = request.isHkLogEnabled();
    ServerConfigCache.setHkLogEnabled(hkLogEnabled);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteConfigList(ConfigListDeleteRequest request) {
    List<Long> idList = request.getIdList();
          sysConfigurationMapper.deleteByIdList(idList, System.currentTimeMillis());
    return BaseResult.success();
  }
}
