package org.zxwl.smart.service.file.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.zxwl.smart.common.properties.FileUploadProperties;
import org.zxwl.smart.common.utils.OSSUtil;
import org.zxwl.smart.domain.request.file.FileDeleteRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.file.FileUploadVO;
import org.zxwl.smart.service.file.FileService;

@Slf4j
@Service
@AllArgsConstructor
public class FileServiceImpl implements FileService {

  private final FileUploadProperties properties;

  @Override
  public SimpleResult<FileUploadVO> uploadFile(MultipartFile multipartFile, String fileType) {
    if (multipartFile == null || multipartFile.isEmpty()) {
      return new SimpleResult<>("上传文件不能为空");
    }
    String originalFilename = multipartFile.getOriginalFilename();
    if (StrUtil.isBlank(originalFilename)) {
      return new SimpleResult<>("文件名不能为空");
    }
    try {
      String extension = FileUtil.extName(originalFilename);
      String targetDir = StrUtil.format("{}/{}", properties.getRootPath(), fileType);
      String filePath;
      if (StrUtil.equalsIgnoreCase("zip", extension)) {
        // zip文件上传需要先解压
        filePath = OSSUtil.uploadZip(multipartFile.getInputStream(), targetDir);
      } else {
        // 普通文件直接保存
        filePath = OSSUtil.upload(multipartFile.getInputStream(), targetDir, extension);
      }
      FileUploadVO vo = new FileUploadVO();
      vo.setFilePath(filePath);
      return new SimpleResult<>(vo);
    } catch (Exception e) {
      log.error("文件上传失败", e);
      return new SimpleResult<>("文件上传失败：" + e.getMessage());
    }
  }

  @Override
  public BaseResult deleteFile(FileDeleteRequest request) {
    String filePath = request.getFilePath();
    OSSUtil.deleteFile(filePath);
    return BaseResult.success();
  }
}
