package org.zxwl.smart.service.file;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import org.zxwl.smart.constant.enums.file.FileTypeEnum;
import org.zxwl.smart.domain.annotation.EnumValue;
import org.zxwl.smart.domain.request.file.FileDeleteRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.file.FileUploadVO;

@Validated
public interface FileService {

  SimpleResult<FileUploadVO> uploadFile(
      MultipartFile multipartFile,
      @NotBlank(message = "文件类型不能为空") @EnumValue(enumClass = FileTypeEnum.class, message = "文件类型非法")
          String fileType);

  BaseResult deleteFile(@NotNull @Valid FileDeleteRequest request);
}
