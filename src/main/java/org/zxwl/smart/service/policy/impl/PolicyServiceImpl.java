package org.zxwl.smart.service.policy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.client.hk.enums.*;
import org.zxwl.smart.common.convert.policy.PolicyConvert;
import org.zxwl.smart.common.lang.Pair;
import org.zxwl.smart.common.lang.Triple;
import org.zxwl.smart.common.utils.ColUtil;
import org.zxwl.smart.common.utils.JSONUtil;
import org.zxwl.smart.constant.consist.PolicyConstant;
import org.zxwl.smart.constant.enums.policy.PolicyActionTypeEnum;
import org.zxwl.smart.constant.enums.policy.PolicyConditionOperatorEnum;
import org.zxwl.smart.constant.enums.policy.PolicyRepeatCycleEnum;
import org.zxwl.smart.constant.enums.policy.PolicyTypeEnum;
import org.zxwl.smart.domain.request.policy.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.policy.PolicyActionVO;
import org.zxwl.smart.domain.response.policy.PolicyListVO;
import org.zxwl.smart.domain.response.policy.PolicyVO;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;
import org.zxwl.smart.mybatis.entity.policy.Policy;
import org.zxwl.smart.mybatis.entity.warehouse.Area;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.ext.policy.PolicyAction;
import org.zxwl.smart.mybatis.ext.policy.PolicyCondition;
import org.zxwl.smart.mybatis.ext.policy.PolicyPeriod;
import org.zxwl.smart.mybatis.mapper.device.EnvDeviceMapper;
import org.zxwl.smart.mybatis.mapper.policy.PolicyMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.AreaMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.policy.PolicyService;

@Service
@AllArgsConstructor
public class PolicyServiceImpl implements PolicyService {

  private final PolicyMapper policyMapper;
  private final WarehouseMapper warehouseMapper;
  private final AreaMapper areaMapper;
  private final EnvDeviceMapper envDeviceMapper;

  public void updatePolicyIfNeed(Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Set<Long> deviceIdSet = deviceList.stream().map(EnvDevice::getId).collect(Collectors.toSet());
    List<Policy> orgPolicyList = policyMapper.selectListByAreaId(areaId);
    // 生成新的策略
    Set<String> deviceTypeSet =
        deviceList.stream().map(EnvDevice::getDeviceType).collect(Collectors.toSet());
    List<Policy> policyList = new ArrayList<>();
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.WSDCGQ.getValue())) {
      // 生成高温调控策略
      policyList.add(createHighTempRegulationPolicy(warehouseId, areaId, deviceList));
      // 生成低温调控策略
      policyList.add(createLowTempRegulationPolicy(warehouseId, areaId, deviceList));
      // 生成高湿调控策略
      policyList.add(createHighHumRegulationPolicy(warehouseId, areaId, deviceList));
      // 生成低湿调控策略
      policyList.add(createLowHumRegulationPolicy(warehouseId, areaId, deviceList));
      // 生成高温告警策略
      policyList.add(createHighTempAlarmPolicy(warehouseId, areaId, deviceList));
      // 生成低温告警策略
      policyList.add(createLowTempAlarmPolicy(warehouseId, areaId, deviceList));
      // 生成高湿告警策略
      policyList.add(createHighHumAlarmPolicy(warehouseId, areaId, deviceList));
      // 生成低湿告警策略
      policyList.add(createLowHumAlarmPolicy(warehouseId, areaId, deviceList));
    }
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.YCY.getValue())) {
      // 生成空气质量调控策略
      policyList.add(createAirQualityRegulationPolicy(warehouseId, areaId, deviceList));
      // 生成空气质量告警策略
      policyList.add(createAirQualityAlarmPolicy(warehouseId, areaId, deviceList));
    }
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.HWTCQ.getValue())) {
      // 生成红外防盗策略
      policyList.add(createInfraredSecurityPolicy(warehouseId, areaId, deviceList));
    }
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.LSTCQ.getValue())
        || deviceTypeSet.contains(EnvDeviceTypeEnum.ZNQSQ.getValue())) {
      // 生成漏水策略
      policyList.add(createWaterLeakagePolicy(warehouseId, areaId, deviceList));
    }
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.YGTCQ.getValue())) {
      // 生成防火策略
      policyList.add(createFirePreventionPolicy(warehouseId, areaId, deviceList));
    }
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.ZXKQJKJJJ.getValue())) {
      // 生成防虫害策略
      policyList.add(createPestControlPolicy(warehouseId, areaId, deviceList));
    }
    if (deviceTypeSet.contains(EnvDeviceTypeEnum.ZNQSQ.getValue())) {
      // 生成防鼠策略
      policyList.add(createRodentPreventionPolicy(warehouseId, areaId, deviceList));
    }
    Triple<List<Policy>, List<Policy>, List<Pair<Policy, Policy>>> diff =
        ColUtil.diff(
            orgPolicyList,
            policyList,
            Policy::getPolicyType,
            (p, p2) -> StrUtil.equals(p.getPolicyAction(), p2.getPolicyAction()));
    List<Policy> addPolicyList = diff.getLeft();
    List<Policy> deletePolicyList = diff.getMiddle();
    // 新增策略
    if (CollUtil.isNotEmpty(addPolicyList)) {
      policyMapper.insertList(addPolicyList);
    }
    // 删除策略
    if (CollUtil.isNotEmpty(deletePolicyList)) {
      List<Long> deleteIdList = deletePolicyList.stream().map(Policy::getId).toList();
      policyMapper.deleteByIdList(deleteIdList, System.currentTimeMillis());
    }
    // 修改策略
    List<Policy> updatePolicyList = new ArrayList<>();
    for (Pair<Policy, Policy> pair : diff.getRight()) {
      List<PolicyAction> orgPolicyActionList =
          JSONUtil.stringToObj(pair.getLeft().getPolicyAction(), new TypeReference<>() {});
      List<PolicyAction> newPolicyActionList =
          JSONUtil.stringToObj(pair.getRight().getPolicyAction(), new TypeReference<>() {});
      if (Objects.isNull(orgPolicyActionList) || Objects.isNull(newPolicyActionList)) continue;
      Iterator<PolicyAction> orgIterator = orgPolicyActionList.iterator();
      // 删除已剔除的设备
      while (orgIterator.hasNext()) {
        PolicyAction orgPolicyAction = orgIterator.next();
        Long deviceId = orgPolicyAction.getDeviceId();
        if (Objects.isNull(deviceId)) continue;
        if (!deviceIdSet.contains(deviceId)) {
          orgIterator.remove();
        }
      }
      // 新增新加入的设备
      for (PolicyAction newPolicyAction : newPolicyActionList) {
        Long deviceId = newPolicyAction.getDeviceId();
        if (Objects.isNull(deviceId)) continue;
        if (!CollUtil.contains(orgPolicyActionList, a -> deviceId.equals(a.getDeviceId()))) {
          orgPolicyActionList.add(newPolicyAction);
        }
      }
      pair.getLeft().setPolicyAction(JSONUtil.objToString(orgPolicyActionList));
      updatePolicyList.add(pair.getLeft());
    }
    if (CollUtil.isNotEmpty(updatePolicyList)) {
      policyMapper.updateList(updatePolicyList);
    }
  }

  @Override
  public PageResult<PolicyListVO> getPolicyList(PolicyListRequest request) {
    Long warehouseId = request.getWarehouseId();
    Long areaId = request.getAreaId();
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    PageHelper.startPage(pageNum, pageSize);
    List<Policy> list = policyMapper.selectList(warehouseId, areaId);
    List<PolicyListVO> voList = getPolicyListVOList(list);
    return new PageResult<>(voList, list);
  }

  @Override
  public SimpleResult<PolicyVO> getPolicy(PolicyRequest request) {
    Long id = request.getId();
    Policy policy = policyMapper.selectById(id);
    if (Objects.isNull(policy)) {
      return new SimpleResult<>("策略不存在");
    }
    PolicyVO vo = getPolicyVO(policy);
    return new SimpleResult<>(vo);
  }

  @Override
  public BaseResult updatePolicy(PolicyUpdateRequest request) {
    Long id = request.getId();
    List<PolicyConditionRequest> conditionRequestList = request.getConditionList();
    List<PolicyPeriodRequest> periodRequestList = request.getPeriodList();
    List<PolicyActionRequest> actionRequestList = request.getActionList();
    Policy orgPolicy = policyMapper.selectById(id);
    if (Objects.isNull(orgPolicy)) {
      return new SimpleResult<>("策略不存在");
    }
    List<PolicyCondition> policyConditionList =
        PolicyConvert.getPolicyConditionList(conditionRequestList);
    List<PolicyPeriod> policyPeriodList = PolicyConvert.getPolicyPeriodList(periodRequestList);
    List<PolicyAction> policyActionList = PolicyConvert.getPolicyActionList(actionRequestList);
    Policy policy = new Policy();
    policy.setId(id);
    policy.setPolicyName(request.getPolicyName());
    policy.setPolicyDescription(request.getPolicyDescription());
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    policyMapper.updateById(policy);
    return BaseResult.success();
  }

  @Override
  public BaseResult updatePolicyEnable(PolicyEnableRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Boolean enabled = request.getEnabled();
    Policy orgPolicy = policyMapper.selectById(id);
    if (Objects.isNull(orgPolicy)) {
      return new SimpleResult<>("策略不存在");
    }
    policyMapper.updateEnabledById(id, enabled, userId);
    return BaseResult.success();
  }

  private List<PolicyListVO> getPolicyListVOList(List<Policy> list) {
    List<PolicyListVO> voList = PolicyConvert.getPolicyListVOList(list);
    if (CollUtil.isEmpty(voList)) {
      return voList;
    }
    // 补充库房和区域名称
    List<Long> warehouseIdList = voList.stream().map(PolicyListVO::getWarehouseId).toList();
    List<Long> areaIdList = voList.stream().map(PolicyListVO::getAreaId).toList();
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    List<Area> areaList = areaMapper.selectNameList(areaIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    Map<Long, Area> areaMap = areaList.stream().collect(Collectors.toMap(Area::getId, v -> v));
    for (PolicyListVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      Area area = areaMap.get(vo.getAreaId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
      if (Objects.nonNull(area)) {
        vo.setAreaName(area.getAreaName());
      }
    }
    return voList;
  }

  private PolicyVO getPolicyVO(Policy policy) {
    Long warehouseId = policy.getWarehouseId();
    Long areaId = policy.getAreaId();
    Warehouse warehouse = warehouseMapper.selectById(warehouseId);
    Area area = areaMapper.selectById(areaId);
    PolicyVO vo = PolicyConvert.getPolicyVO(policy);
    if (Objects.nonNull(warehouse)) {
      vo.setWarehouseName(warehouse.getWarehouseName());
    }
    if (Objects.nonNull(area)) {
      vo.setAreaName(area.getAreaName());
    }
    // 策略条件、执行周期、动作
    List<PolicyCondition> policyConditionList =
        JSONUtil.stringToObj(policy.getPolicyCondition(), new TypeReference<>() {});
    List<PolicyPeriod> policyPeriodList =
        JSONUtil.stringToObj(policy.getPolicyPeriod(), new TypeReference<>() {});
    List<PolicyAction> policyActionList =
        JSONUtil.stringToObj(policy.getPolicyAction(), new TypeReference<>() {});
    vo.setConditionList(PolicyConvert.getPolicyConditionVOList(policyConditionList));
    vo.setPeriodList(PolicyConvert.getPolicyPeriodVOList(policyPeriodList));
    vo.setActionList(PolicyConvert.getPolicyActionVOList(policyActionList));
    // 设备名字补充完善
    if (CollUtil.isNotEmpty(vo.getConditionList())) {
      List<Long> deviceIdList =
          vo.getActionList().stream()
              .map(PolicyActionVO::getDeviceId)
              .filter(Objects::nonNull)
              .toList();
      if (CollUtil.isNotEmpty(deviceIdList)) {
        List<EnvDevice> deviceList = envDeviceMapper.selectListByIdList(deviceIdList, true);
        Map<Long, EnvDevice> deviceMap =
            deviceList.stream().collect(Collectors.toMap(EnvDevice::getId, v -> v));
        for (PolicyActionVO actionVO : vo.getActionList()) {
          Long deviceId = actionVO.getDeviceId();
          if (Objects.isNull(deviceId)) {
            continue;
          }
          EnvDevice device = deviceMap.get(deviceId);
          if (Objects.nonNull(device)) {
            actionVO.setDeviceName(device.getDeviceName());
          }
        }
      }
    }
    return vo;
  }

  private Policy createHighTempRegulationPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.HIGH_TEMP_REGULATION.getValue());
    policy.setPolicyName(PolicyTypeEnum.HIGH_TEMP_REGULATION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.HIGH_TEMP_REGULATION.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.TEMPERATURE.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition.setRangeValue(PolicyConstant.HIGH_TEMP_REGULATION_RANGE);
    policyCondition.setFieldValue(PolicyConstant.HIGH_TEMP_REGULATION_TEMP);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.KTKZQ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(List.of(KTKZQControlFieldEnum.COOLING.getName()));
        policyAction.setEndControlFieldNameList(List.of(KTKZQControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createLowTempRegulationPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.LOW_TEMP_REGULATION.getValue());
    policy.setPolicyName(PolicyTypeEnum.LOW_TEMP_REGULATION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.LOW_TEMP_REGULATION.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.TEMPERATURE.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.LESS_THAN.getValue());
    policyCondition.setRangeValue(PolicyConstant.LOW_TEMP_REGULATION_RANGE);
    policyCondition.setFieldValue(PolicyConstant.LOW_TEMP_REGULATION_TEMP);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.KTKZQ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(List.of(KTKZQControlFieldEnum.HEATING.getName()));
        policyAction.setEndControlFieldNameList(List.of(KTKZQControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createHighHumRegulationPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.HIGH_HUM_REGULATION.getValue());
    policy.setPolicyName(PolicyTypeEnum.HIGH_HUM_REGULATION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.HIGH_HUM_REGULATION.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.HUMIDITY.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition.setRangeValue(PolicyConstant.HIGH_HUM_REGULATION_RANGE);
    policyCondition.setFieldValue(PolicyConstant.HIGH_HUM_REGULATION_HUM);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policy.setPolicyPeriod(JSONUtil.objToString(List.of(policyPeriod)));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.ZXHSJ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(
            List.of(
                ZXHSJControlFieldEnum.POWER_ON.getName(),
                ZXHSJControlFieldEnum.MODE_CHU_SHI.getName()));
        policyAction.setEndControlFieldNameList(List.of(ZXHSJControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createLowHumRegulationPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.LOW_HUM_REGULATION.getValue());
    policy.setPolicyName(PolicyTypeEnum.LOW_HUM_REGULATION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.LOW_HUM_REGULATION.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.HUMIDITY.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.LESS_THAN.getValue());
    policyCondition.setRangeValue(PolicyConstant.LOW_HUM_REGULATION_RANGE);
    policyCondition.setFieldValue(PolicyConstant.LOW_HUM_REGULATION_HUM);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.ZXHSJ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(
            List.of(
                ZXHSJControlFieldEnum.POWER_ON.getName(),
                ZXHSJControlFieldEnum.MODE_JIA_SHI.getName()));
        policyAction.setEndControlFieldNameList(List.of(ZXHSJControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createHighTempAlarmPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.HIGH_TEMP_ALARM.getValue());
    policy.setPolicyName(PolicyTypeEnum.HIGH_TEMP_ALARM.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.HIGH_TEMP_ALARM.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.TEMPERATURE.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition.setFieldValue(PolicyConstant.HIGH_TEMP_ALARM_TEMP);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createLowTempAlarmPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.LOW_TEMP_ALARM.getValue());
    policy.setPolicyName(PolicyTypeEnum.LOW_TEMP_ALARM.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.LOW_TEMP_ALARM.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.TEMPERATURE.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.LESS_THAN.getValue());
    policyCondition.setFieldValue(PolicyConstant.LOW_TEMP_ALARM_TEMP);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createHighHumAlarmPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.HIGH_HUM_ALARM.getValue());
    policy.setPolicyName(PolicyTypeEnum.HIGH_HUM_ALARM.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.HIGH_HUM_ALARM.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.HUMIDITY.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition.setFieldValue(PolicyConstant.HIGH_HUM_ALARM_HUM);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createLowHumAlarmPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.LOW_HUM_ALARM.getValue());
    policy.setPolicyName(PolicyTypeEnum.LOW_HUM_ALARM.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.LOW_HUM_ALARM.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(WSDCGQFieldEnum.HUMIDITY.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.LESS_THAN.getValue());
    policyCondition.setFieldValue(PolicyConstant.LOW_HUM_ALARM_HUM);
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createAirQualityRegulationPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.AIR_QUALITY_REGULATION.getValue());
    policy.setPolicyName(PolicyTypeEnum.AIR_QUALITY_REGULATION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.AIR_QUALITY_REGULATION.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition1 = new PolicyCondition();
    policyCondition1.setFieldName(YCYFieldEnum.PM25.getName());
    policyCondition1.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition1.setRangeValue(PolicyConstant.PM25_REGULATION_RANGE);
    policyCondition1.setFieldValue(PolicyConstant.PM25_REGULATION_VALUE);
    policyConditionList.add(policyCondition1);

    PolicyCondition policyCondition2 = new PolicyCondition();
    policyCondition2.setFieldName(YCYFieldEnum.PM10.getName());
    policyCondition2.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition2.setRangeValue(PolicyConstant.PM10_REGULATION_RANGE);
    policyCondition2.setFieldValue(PolicyConstant.PM10_REGULATION_VALUE);
    policyConditionList.add(policyCondition2);

    PolicyCondition policyCondition3 = new PolicyCondition();
    policyCondition3.setFieldName(YCYFieldEnum.TVOC.getName());
    policyCondition3.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition3.setRangeValue(PolicyConstant.TVOC_REGULATION_RANGE);
    policyCondition3.setFieldValue(PolicyConstant.TVOC_REGULATION_VALUE);
    policyConditionList.add(policyCondition3);

    PolicyCondition policyCondition4 = new PolicyCondition();
    policyCondition4.setFieldName(YCYFieldEnum.CO2.getName());
    policyCondition4.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition4.setRangeValue(PolicyConstant.CO2_REGULATION_RANGE);
    policyCondition4.setFieldValue(PolicyConstant.CO2_REGULATION_VALUE);
    policyConditionList.add(policyCondition4);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.ZXKQJKJJJ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(
            List.of(
                ZXKQJKJJJControlFieldEnum.POWER_ON.getName(),
                ZXKQJKJJJControlFieldEnum.MIDDLE_SPEED.getName()));
        policyAction.setEndControlFieldNameList(
            List.of(ZXKQJKJJJControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createAirQualityAlarmPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.AIR_QUALITY_ALARM.getValue());
    policy.setPolicyName(PolicyTypeEnum.AIR_QUALITY_ALARM.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.AIR_QUALITY_ALARM.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition1 = new PolicyCondition();
    policyCondition1.setFieldName(YCYFieldEnum.PM25.getName());
    policyCondition1.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition1.setFieldValue(PolicyConstant.PM25_ALARM_VALUE);
    policyConditionList.add(policyCondition1);

    PolicyCondition policyCondition2 = new PolicyCondition();
    policyCondition2.setFieldName(YCYFieldEnum.PM10.getName());
    policyCondition2.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition2.setFieldValue(PolicyConstant.PM10_ALARM_VALUE);
    policyConditionList.add(policyCondition2);

    PolicyCondition policyCondition3 = new PolicyCondition();
    policyCondition3.setFieldName(YCYFieldEnum.TVOC.getName());
    policyCondition3.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition3.setFieldValue(PolicyConstant.TVOC_ALARM_VALUE);
    policyConditionList.add(policyCondition3);

    PolicyCondition policyCondition4 = new PolicyCondition();
    policyCondition4.setFieldName(YCYFieldEnum.CO2.getName());
    policyCondition4.setOperator(PolicyConditionOperatorEnum.GREATER_THAN.getValue());
    policyCondition4.setFieldValue(PolicyConstant.CO2_ALARM_VALUE);
    policyConditionList.add(policyCondition4);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createInfraredSecurityPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.INFRARED_SECURITY.getValue());
    policy.setPolicyName(PolicyTypeEnum.INFRARED_SECURITY.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.INFRARED_SECURITY.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(BJFieldEnum.ALARM.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.EQUAL_TO.getValue());
    policyCondition.setFieldValue(EnvAlertValueMapperEnum.AUTO.getValue().toString());
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createWaterLeakagePolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.WATER_LEAKAGE.getValue());
    policy.setPolicyName(PolicyTypeEnum.WATER_LEAKAGE.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.WATER_LEAKAGE.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(BJFieldEnum.ALARM.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.EQUAL_TO.getValue());
    policyCondition.setFieldValue(EnvAlertValueMapperEnum.AUTO.getValue().toString());
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createFirePreventionPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.FIRE_PREVENTION.getValue());
    policy.setPolicyName(PolicyTypeEnum.FIRE_PREVENTION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.FIRE_PREVENTION.getDescription());

    List<PolicyCondition> policyConditionList = new ArrayList<>();
    PolicyCondition policyCondition = new PolicyCondition();
    policyCondition.setFieldName(BJFieldEnum.ALARM.getName());
    policyCondition.setOperator(PolicyConditionOperatorEnum.EQUAL_TO.getValue());
    policyCondition.setFieldValue(EnvAlertValueMapperEnum.AUTO.getValue().toString());
    policyConditionList.add(policyCondition);
    policy.setPolicyCondition(JSONUtil.objToString(policyConditionList));

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    PolicyAction policyAction = new PolicyAction();
    policyAction.setType(PolicyActionTypeEnum.SYSTEM_MESSAGE.getValue());
    policyActionList.add(policyAction);
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createPestControlPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.PEST_CONTROL.getValue());
    policy.setPolicyName(PolicyTypeEnum.PEST_CONTROL.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.PEST_CONTROL.getDescription());

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.ZXKQJKJJJ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(
            List.of(
                ZXKQJKJJJControlFieldEnum.POWER_ON.getName(),
                ZXKQJKJJJControlFieldEnum.MIDDLE_SPEED.getName()));
        policyAction.setEndControlFieldNameList(
            List.of(ZXKQJKJJJControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }

  private Policy createRodentPreventionPolicy(
      Long warehouseId, Long areaId, List<EnvDevice> deviceList) {
    Policy policy = new Policy();
    policy.setWarehouseId(warehouseId);
    policy.setAreaId(areaId);
    policy.setSystem(true);
    policy.setEnabled(true);
    policy.setPolicyType(PolicyTypeEnum.RODENT_PREVENTION.getValue());
    policy.setPolicyName(PolicyTypeEnum.RODENT_PREVENTION.getValue());
    policy.setPolicyDescription(PolicyTypeEnum.RODENT_PREVENTION.getDescription());

    List<PolicyPeriod> policyPeriodList = new ArrayList<>();
    PolicyPeriod policyPeriod = new PolicyPeriod();
    policyPeriod.setRepeatCycle(PolicyRepeatCycleEnum.DAILY.getValue());
    policyPeriod.setStartTime(PolicyConstant.DAILY_START_TIME);
    policyPeriod.setEndTime(PolicyConstant.DAILY_END_TIME);
    policyPeriodList.add(policyPeriod);
    policy.setPolicyPeriod(JSONUtil.objToString(policyPeriodList));

    List<PolicyAction> policyActionList = new ArrayList<>();
    for (EnvDevice envDevice : deviceList) {
      if (EnvDeviceTypeEnum.ZNQSQ.equalsValue(envDevice.getDeviceType())) {
        PolicyAction policyAction = new PolicyAction();
        policyAction.setType(PolicyActionTypeEnum.DEVICE_CONTROL.getValue());
        policyAction.setDeviceId(envDevice.getId());
        policyAction.setControlFieldNameList(List.of(ZNQSQControlFieldEnum.POWER_ON.getName()));
        policyAction.setEndControlFieldNameList(List.of(ZNQSQControlFieldEnum.POWER_OFF.getName()));
        policyActionList.add(policyAction);
      }
    }
    policy.setPolicyAction(JSONUtil.objToString(policyActionList));
    return policy;
  }
}
