package org.zxwl.smart.service.policy;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.policy.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.policy.PolicyListVO;
import org.zxwl.smart.domain.response.policy.PolicyVO;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;

@Validated
public interface PolicyService {

  void updatePolicyIfNeed(Long warehouseId, Long areaId, List<EnvDevice> deviceList);

  PageResult<PolicyListVO> getPolicyList(@NotNull @Valid PolicyListRequest request);

  SimpleResult<PolicyVO> getPolicy(@NotNull @Valid PolicyRequest request);

  BaseResult updatePolicy(@NotNull @Valid PolicyUpdateRequest request);

  BaseResult updatePolicyEnable(@NotNull @Valid PolicyEnableRequest request);
}
