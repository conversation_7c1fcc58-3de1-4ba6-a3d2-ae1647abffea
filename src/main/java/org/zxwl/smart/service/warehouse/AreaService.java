package org.zxwl.smart.service.warehouse;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.warehouse.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.warehouse.AreaListVO;
import org.zxwl.smart.domain.response.warehouse.AreaVO;

@Validated
public interface AreaService {

  ListResult<AreaListVO> getAreaList(@NotNull @Valid AreaListRequest request);

  SimpleResult<AreaVO> getArea(@NotNull @Valid AreaRequest request);

  BaseResult createArea(@NotNull @Valid AreaCreateRequest request);

  BaseResult updateArea(@NotNull @Valid AreaUpdateRequest request);

  BaseResult deleteArea(@NotNull @Valid AreaDeleteRequest request);
}
