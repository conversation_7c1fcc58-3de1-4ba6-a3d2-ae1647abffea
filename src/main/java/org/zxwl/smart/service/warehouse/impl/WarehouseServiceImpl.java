package org.zxwl.smart.service.warehouse.impl;

import cn.hutool.core.collection.CollUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.cache.model.WarehouseCacheModel;
import org.zxwl.smart.common.convert.warehouse.WarehouseConvert;
import org.zxwl.smart.constant.consist.WarehouseConstant;
import org.zxwl.smart.domain.request.warehouse.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.warehouse.WarehouseBuildingVO;
import org.zxwl.smart.domain.response.warehouse.WarehouseFloorVO;
import org.zxwl.smart.domain.response.warehouse.WarehouseVO;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.entity.warehouse.WarehouseBuilding;
import org.zxwl.smart.mybatis.entity.warehouse.WarehouseFloor;
import org.zxwl.smart.mybatis.mapper.cabinet.CabinetGroupMapper;
import org.zxwl.smart.mybatis.mapper.rack.RackGroupMapper;
import org.zxwl.smart.mybatis.mapper.user.OrganizationMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseBuildingMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseFloorMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.warehouse.WarehouseService;

@Slf4j
@Service
@AllArgsConstructor
public class WarehouseServiceImpl implements WarehouseService {

  private final OrganizationMapper organizationMapper;
  private final WarehouseBuildingMapper buildingMapper;
  private final WarehouseFloorMapper floorMapper;
  private final WarehouseMapper warehouseMapper;
  private final CabinetGroupMapper cabinetGroupMapper;
  private final RackGroupMapper rackGroupMapper;
  private final EnvDataCache envDataCache;
  private final TransactionTemplate transactionTemplate;

  @Override
  public int generateDefaultWarehouseIfNeed() {
    if (buildingMapper.count() <= 0) {
      WarehouseBuilding building = new WarehouseBuilding();
      building.setBuildingNo(WarehouseConstant.DEFAULT_BUILDING_NO);
      building.setBuildingName(WarehouseConstant.DEFAULT_BUILDING_NAME);
      WarehouseFloor floor = new WarehouseFloor();
      floor.setFloorNo(WarehouseConstant.DEFAULT_FLOOR_NO);
      floor.setFloorName(WarehouseConstant.DEFAULT_FLOOR_NAME);
      Warehouse warehouse = new Warehouse();
      warehouse.setOrganizationId(organizationMapper.selectFirst().getId());
      warehouse.setWarehouseNo(WarehouseConstant.DEFAULT_WAREHOUSE_NO);
      warehouse.setWarehouseName(WarehouseConstant.DEFAULT_WAREHOUSE_NAME);
      transactionTemplate.execute(
          status -> {
            buildingMapper.insert(building);
            floor.setWarehouseBuildingId(building.getId());
            floorMapper.insert(floor);
            warehouse.setWarehouseFloorId(floor.getId());
            warehouseMapper.insert(warehouse);
            log.info("创建默认建筑、默认楼层、默认库房");
            return true;
          });
      return 1;
    }
    return 0;
  }

  @Override
  public ListResult<WarehouseBuildingVO> getWarehouseBuildingList(BuildingListRequest request) {
    List<WarehouseBuilding> list = buildingMapper.selectList();
    List<WarehouseBuildingVO> voList = WarehouseConvert.getWarehouseBuildingVOList(list);
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createWarehouseBuilding(BuildingCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Integer buildingNo = request.getBuildingNo();
    int exist = buildingMapper.existByIdOrBuildingNo(null, buildingNo);
    if (exist > 0) {
      return new SimpleResult<>("库房建筑编号已存在");
    }
    Integer buildingMaxCount = WarehouseConstant.BUILDING_MAX_COUNT;
    if (buildingMapper.count() >= buildingMaxCount) {
      return new SimpleResult<>("库房建筑数量已达" + buildingMaxCount + "条上限");
    }
    WarehouseBuilding building = WarehouseConvert.getWarehouseBuilding(request);
    building.setCreatedId(userId);
    building.setUpdatedId(userId);
    buildingMapper.insert(building);
    return new SimpleResult<>(new BaseCreateVO(building.getId()));
  }

  @Override
  public BaseResult updateWarehouseBuilding(BuildingUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Integer buildingNo = request.getBuildingNo();
    WarehouseBuilding orgBuilding = buildingMapper.selectById(id);
    if (Objects.isNull(orgBuilding)) {
      return BaseResult.failed("库房建筑不存在");
    }
    if (!buildingNo.equals(orgBuilding.getBuildingNo())) {
      int exist = buildingMapper.existByIdOrBuildingNo(null, buildingNo);
      if (exist > 0) {
        return BaseResult.failed("库房建筑编号已存在");
      }
    }
    WarehouseBuilding building = WarehouseConvert.getWarehouseBuilding(request);
    building.setUpdatedId(userId);
    buildingMapper.updateById(building);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteWarehouseBuilding(BuildingDeleteRequest request) {
    Long id = request.getId();
    long deleteAt = System.currentTimeMillis();
    int exist = floorMapper.existByBuildingId(id);
    if (exist > 0) {
      return BaseResult.failed("该库房建筑下存在楼层，不能直接删除");
    }
    buildingMapper.deleteById(id, deleteAt);
    return BaseResult.success();
  }

  @Override
  public ListResult<WarehouseFloorVO> getWarehouseFloorList(FloorListRequest request) {
    Long buildingId = request.getBuildingId();
    List<WarehouseFloor> floorList = floorMapper.selectList(buildingId);
    List<WarehouseFloorVO> voList = getWarehouseFloorVOList(floorList);
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<BaseCreateVO> createWarehouseFloor(FloorCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long buildingId = request.getBuildingId();
    Integer floorNo = request.getFloorNo();
    if (buildingMapper.existByIdOrBuildingNo(buildingId, null) == 0) {
      return new SimpleResult<>("库房建筑不存在");
    }
    if (floorMapper.existByBuildingIdAndFloorNo(buildingId, floorNo) > 0) {
      return new SimpleResult<>("该库房建筑下楼层编号已存在");
    }
    Integer floorMaxCount = WarehouseConstant.FLOOR_MAX_COUNT;
    if (floorMapper.count() > floorMaxCount) {
      return new SimpleResult<>("楼层数量已达" + floorMaxCount + "条上限");
    }
    WarehouseFloor floor = WarehouseConvert.getWarehouseFloor(request);
    floor.setCreatedId(userId);
    floor.setUpdatedId(userId);
    floorMapper.insert(floor);
    return new SimpleResult<>(new BaseCreateVO(floor.getId()));
  }

  @Override
  public BaseResult updateWarehouseFloor(FloorUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Integer floorNo = request.getFloorNo();
    WarehouseFloor orgFloor = floorMapper.selectById(id);
    if (Objects.isNull(orgFloor)) {
      return BaseResult.failed("楼层不存在");
    }
    if (!floorNo.equals(orgFloor.getFloorNo())) {
      int exist =
          floorMapper.existByBuildingIdAndFloorNo(orgFloor.getWarehouseBuildingId(), floorNo);
      if (exist > 0) {
        return BaseResult.failed("该库房建筑下楼层编号已存在");
      }
    }
    WarehouseFloor floor = WarehouseConvert.getWarehouseFloor(request);
    floor.setUpdatedId(userId);
    floorMapper.updateById(floor);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteWarehouseFloor(FloorDeleteRequest request) {
    Long id = request.getId();
    int exist = warehouseMapper.existByFloorId(id);
    if (exist > 0) {
      return BaseResult.failed("该楼层下存在库房，不能直接删除");
    }
    long deleteAt = System.currentTimeMillis();
    floorMapper.deleteById(id, deleteAt);
    return BaseResult.success();
  }

  @Override
  public ListResult<WarehouseVO> getWarehouseList(WarehouseListRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    List<Long> idList = request.getIdList();
    Long floorId = request.getFloorId();
    List<Warehouse> warehouseList = warehouseMapper.selectList(organizationId, idList, floorId);
    List<WarehouseVO> voList = getWarehouseVOList(warehouseList);
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<WarehouseVO> getWarehouse(WarehouseGetRequest request) {
    Long id = request.getId();
    Warehouse warehouse = warehouseMapper.selectById(id);
    WarehouseVO vo = getWarehouseVO(warehouse);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<BaseCreateVO> createWarehouse(WarehouseCreateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long floorId = request.getFloorId();
    Integer warehouseNo = request.getWarehouseNo();
    if (floorMapper.existById(floorId) == 0) {
      return new SimpleResult<>("库房楼层不存在");
    }
    if (warehouseMapper.existByFloorIdAndWarehouseNo(floorId, warehouseNo) > 0) {
      return new SimpleResult<>("该库房楼层下库房编号已存在");
    }
    Integer warehouseMaxCount = WarehouseConstant.WAREHOUSE_MAX_COUNT;
    if (warehouseMapper.count() > warehouseMaxCount) {
      return new SimpleResult<>("库房数量已达" + warehouseMaxCount + "条上限");
    }
    Warehouse warehouse = WarehouseConvert.getWarehouse(request);
    warehouse.setOrganizationId(organizationId);
    warehouse.setCreatedId(userId);
    warehouse.setUpdatedId(userId);
    warehouseMapper.insert(warehouse);
    // 更新缓存
    WarehouseCacheModel warehouseCacheModel = new WarehouseCacheModel();
    warehouseCacheModel.setWarehouseId(warehouse.getId());
    envDataCache.putWarehouse(warehouseCacheModel);
    return new SimpleResult<>(new BaseCreateVO(warehouse.getId()));
  }

  @Override
  public BaseResult updateWarehouse(WarehouseUpdateRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Integer warehouseNo = request.getWarehouseNo();
    Warehouse orgWarehouse = warehouseMapper.selectById(id);
    if (Objects.isNull(orgWarehouse)) {
      return BaseResult.failed("库房不存在");
    }
    if (!Objects.equals(orgWarehouse.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该库房不属于当前机构");
    }
    if (!warehouseNo.equals(orgWarehouse.getWarehouseNo())) {
      int exist =
          warehouseMapper.existByFloorIdAndWarehouseNo(
              orgWarehouse.getWarehouseFloorId(), warehouseNo);
      if (exist > 0) {
        return BaseResult.failed("该库房楼层下库房编号已存在");
      }
    }
    Warehouse warehouse = WarehouseConvert.getWarehouse(request);
    warehouse.setUpdatedId(userId);
    warehouseMapper.updateById(warehouse);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteWarehouse(WarehouseDeleteRequest request) {
    Long organizationId = AccountLocal.getOrganizationId();
    Long id = request.getId();
    Warehouse orgWarehouse = warehouseMapper.selectById(id);
    if (Objects.isNull(orgWarehouse)) {
      return BaseResult.failed("库房不存在");
    }
    if (!Objects.equals(orgWarehouse.getOrganizationId(), organizationId)) {
      return BaseResult.failed("该库房不属于当前机构");
    }
    if (cabinetGroupMapper.existByWarehouseId(id) > 0) {
      return BaseResult.failed("该库房下存在智能柜组，不能直接删除");
    }
    if (rackGroupMapper.existByWarehouseId(id) > 0) {
      return BaseResult.failed("该库房下存在密集架组，不能直接删除");
    }
    // todo：判断库房下关联环控设备
    long deleteAt = System.currentTimeMillis();
    warehouseMapper.deleteById(id, deleteAt);
    // 删除缓存
    envDataCache.removeWarehouse(id);
    return BaseResult.success();
  }

  private List<WarehouseFloorVO> getWarehouseFloorVOList(List<WarehouseFloor> floorList) {
    List<WarehouseFloorVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(floorList)) {
      return voList;
    }
    List<Long> buildingIdList =
        floorList.stream()
            .map(WarehouseFloor::getWarehouseBuildingId)
            .distinct()
            .collect(Collectors.toList());
    List<WarehouseBuilding> buildingList = buildingMapper.selectNoAndNameList(buildingIdList);
    Map<Long, WarehouseBuilding> buildingMap =
        buildingList.stream().collect(Collectors.toMap(WarehouseBuilding::getId, v -> v));
    for (WarehouseFloor floor : floorList) {
      WarehouseBuilding building = buildingMap.get(floor.getWarehouseBuildingId());
      WarehouseFloorVO vo = WarehouseConvert.getWarehouseFloorVO(floor);
      vo.setBuildingNo(building.getBuildingNo());
      vo.setBuildingName(building.getBuildingName());
      voList.add(vo);
    }
    return voList;
  }

  private List<WarehouseVO> getWarehouseVOList(List<Warehouse> warehouseList) {
    List<WarehouseVO> voList = new ArrayList<>();
    if (CollUtil.isEmpty(warehouseList)) {
      return voList;
    }
    List<Long> floorIdList =
        warehouseList.stream()
            .map(Warehouse::getWarehouseFloorId)
            .distinct()
            .collect(Collectors.toList());
    List<WarehouseFloor> floorList = floorMapper.selectNoAndNameList(floorIdList);
    Map<Long, WarehouseFloor> floorMap =
        floorList.stream().collect(Collectors.toMap(WarehouseFloor::getId, v -> v));
    List<Long> buildingIdList =
        floorList.stream().map(WarehouseFloor::getWarehouseBuildingId).collect(Collectors.toList());
    List<WarehouseBuilding> buildingList = buildingMapper.selectNoAndNameList(buildingIdList);
    Map<Long, WarehouseBuilding> buildingMap =
        buildingList.stream().collect(Collectors.toMap(WarehouseBuilding::getId, v -> v));
    for (Warehouse warehouse : warehouseList) {
      WarehouseFloor floor = floorMap.get(warehouse.getWarehouseFloorId());
      WarehouseBuilding building = buildingMap.get(floor.getWarehouseBuildingId());
      WarehouseVO vo = WarehouseConvert.getWarehouseVO(warehouse);
      vo.setFloorNo(floor.getFloorNo());
      vo.setFloorName(floor.getFloorName());
      vo.setBuildingId(building.getId());
      vo.setBuildingNo(building.getBuildingNo());
      vo.setBuildingName(building.getBuildingName());
      voList.add(vo);
    }
    return voList;
  }

  private WarehouseVO getWarehouseVO(Warehouse warehouse) {
    if (Objects.isNull(warehouse)) {
      return null;
    }
    Long warehouseFloorId = warehouse.getWarehouseFloorId();
    WarehouseFloor floor = floorMapper.selectById(warehouseFloorId);
    WarehouseBuilding building = buildingMapper.selectById(floor.getWarehouseBuildingId());
    WarehouseVO vo = WarehouseConvert.getWarehouseVO(warehouse);
    vo.setFloorNo(floor.getFloorNo());
    vo.setFloorName(floor.getFloorName());
    vo.setBuildingId(building.getId());
    vo.setBuildingNo(building.getBuildingNo());
    vo.setBuildingName(building.getBuildingName());
    return vo;
  }
}
