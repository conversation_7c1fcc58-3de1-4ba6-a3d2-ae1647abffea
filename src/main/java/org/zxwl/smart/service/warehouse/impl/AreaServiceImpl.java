package org.zxwl.smart.service.warehouse.impl;

import cn.hutool.core.collection.CollUtil;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.EnvDataCache;
import org.zxwl.smart.cache.model.AreaCacheModel;
import org.zxwl.smart.common.convert.env.AreaConvert;
import org.zxwl.smart.domain.request.warehouse.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.warehouse.AreaListVO;
import org.zxwl.smart.domain.response.warehouse.AreaVO;
import org.zxwl.smart.mybatis.entity.device.EnvDevice;
import org.zxwl.smart.mybatis.entity.warehouse.Area;
import org.zxwl.smart.mybatis.entity.warehouse.AreaDevice;
import org.zxwl.smart.mybatis.entity.warehouse.Warehouse;
import org.zxwl.smart.mybatis.mapper.device.EnvDeviceMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.AreaDeviceMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.AreaMapper;
import org.zxwl.smart.mybatis.mapper.warehouse.WarehouseMapper;
import org.zxwl.smart.service.policy.PolicyService;
import org.zxwl.smart.service.warehouse.AreaService;

@Service
@AllArgsConstructor
public class AreaServiceImpl implements AreaService {

  private final WarehouseMapper warehouseMapper;
  private final AreaMapper areaMapper;
  private final AreaDeviceMapper areaDeviceMapper;
  private final EnvDeviceMapper envDeviceMapper;
  private final TransactionTemplate transactionTemplate;
  private final EnvDataCache envDataCache;
  private final PolicyService policyService;

  @Override
  public ListResult<AreaListVO> getAreaList(AreaListRequest request) {
    Long warehouseId = request.getWarehouseId();
    List<Area> areaList = areaMapper.selectList(warehouseId);
    List<AreaDevice> areaDeviceList = Collections.emptyList();
    if (CollUtil.isNotEmpty(areaList)) {
      List<Long> areaIdList = areaList.stream().map(Area::getId).toList();
      areaDeviceList = areaDeviceMapper.selectListByAreaIdList(areaIdList);
    }
    List<AreaListVO> voList = getEnvDeviceAreaVOList(areaList, areaDeviceList);
    return new ListResult<>(voList);
  }

  @Override
  public SimpleResult<AreaVO> getArea(AreaRequest request) {
    Long id = request.getId();
    Area area = areaMapper.selectById(id);
    if (Objects.isNull(area)) {
      return new SimpleResult<>("区域不存在");
    }
    List<EnvDevice> deviceList = areaDeviceMapper.selectEnvDeviceListByAreaId(id);
    AreaVO vo = AreaConvert.getAreaVO(area);
    // 缓存数据完善
    AreaCacheModel areaCacheModel = envDataCache.getArea(id);
    if (Objects.nonNull(areaCacheModel)) {
      vo.setTemperature(areaCacheModel.getTemperature());
      vo.setHumidity(areaCacheModel.getHumidity());
      vo.setCo2(areaCacheModel.getCo2());
      vo.setTvoc(areaCacheModel.getTvoc());
      vo.setPm10(areaCacheModel.getPm10());
      vo.setPm25(areaCacheModel.getPm25());
    }
    // 库房名字完善
    Warehouse warehouse = warehouseMapper.selectById(area.getWarehouseId());
    if (Objects.nonNull(warehouse)) {
      vo.setWarehouseName(warehouse.getWarehouseName());
    }
    // 设备数据完善
    List<AreaVO.DeviceVO> deviceVOList = new ArrayList<>();
    for (EnvDevice device : deviceList) {
      AreaVO.DeviceVO deviceVO = new AreaVO.DeviceVO();
      deviceVO.setId(device.getId());
      deviceVO.setDeviceName(device.getDeviceName());
      deviceVOList.add(deviceVO);
    }
    vo.setDeviceList(deviceVOList);
    return new SimpleResult<>(vo);
  }

  @Override
  public BaseResult createArea(AreaCreateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long warehouseId = request.getWarehouseId();
    if (warehouseMapper.existById(warehouseId) == 0) {
      return new SimpleResult<>("库房不存在");
    }
    List<AreaCreateRequest.Device> deviceRequestList = request.getDeviceList();
    List<Long> deviceIdList =
        deviceRequestList.stream().map(AreaCreateRequest.Device::getId).distinct().toList();
    List<EnvDevice> deviceList = envDeviceMapper.selectListByIdList(deviceIdList, false);
    Map<Long, EnvDevice> deviceMap =
        deviceList.stream().collect(Collectors.toMap(EnvDevice::getId, v -> v));
    for (AreaCreateRequest.Device deviceRequest : deviceRequestList) {
      EnvDevice device = deviceMap.get(deviceRequest.getId());
      if (Objects.isNull(device)) {
        return new SimpleResult<>("设备 " + deviceRequest.getDeviceName() + " 不存在");
      }
      if (!device.getWarehouseId().equals(warehouseId)) {
        return new SimpleResult<>("设备 " + deviceRequest.getDeviceName() + " 不属于当前区域库房");
      }
    }
    // 转DO
    Area area = AreaConvert.getArea(request);
    area.setCreatedId(userId);
    area.setUpdatedId(userId);
    List<AreaDevice> relationList = new ArrayList<>();
    for (Long deviceId : deviceIdList) {
      AreaDevice relation = new AreaDevice();
      relation.setEnvDeviceId(deviceId);
      relationList.add(relation);
    }
    // 入库
    transactionTemplate.execute(
        status -> {
          areaMapper.insert(area);
          relationList.forEach(relation -> relation.setAreaId(area.getId()));
          areaDeviceMapper.insertList(relationList);
          policyService.updatePolicyIfNeed(warehouseId, area.getId(), deviceList);
          return true;
        });
    // 加入缓存
    AreaCacheModel cacheModel = new AreaCacheModel();
    cacheModel.setId(area.getId());
    cacheModel.setWarehouseId(warehouseId);
    if (CollUtil.isNotEmpty(deviceIdList)) {
      cacheModel.setDeviceIdSet(new HashSet<>(deviceIdList));
    }
    envDataCache.putArea(cacheModel);
    return BaseResult.success();
  }

  @Override
  public BaseResult updateArea(AreaUpdateRequest request) {
    Long userId = AccountLocal.getUserId();
    Long id = request.getId();
    Area orgArea = areaMapper.selectById(id);
    if (Objects.isNull(orgArea)) {
      return new SimpleResult<>("区域不存在");
    }
    List<AreaUpdateRequest.Device> deviceRequestList = request.getDeviceList();
    List<Long> deviceIdList =
        deviceRequestList.stream().map(AreaUpdateRequest.Device::getId).distinct().toList();
    List<EnvDevice> deviceList = envDeviceMapper.selectListByIdList(deviceIdList, false);
    Map<Long, EnvDevice> deviceMap =
        deviceList.stream().collect(Collectors.toMap(EnvDevice::getId, v -> v));
    for (AreaUpdateRequest.Device deviceRequest : deviceRequestList) {
      EnvDevice device = deviceMap.get(deviceRequest.getId());
      if (Objects.isNull(device)) {
        return new SimpleResult<>("设备 " + deviceRequest.getDeviceName() + " 不存在");
      }
      if (!device.getWarehouseId().equals(orgArea.getWarehouseId())) {
        return new SimpleResult<>("设备 " + deviceRequest.getDeviceName() + " 不属于当前区域库房");
      }
    }
    // 转DO
    Area area = AreaConvert.getArea(request);
    area.setUpdatedId(userId);
    List<AreaDevice> relationList = new ArrayList<>();
    for (Long deviceId : deviceIdList) {
      AreaDevice relation = new AreaDevice();
      relation.setEnvDeviceId(deviceId);
      relationList.add(relation);
    }
    // 入库
    transactionTemplate.execute(
        status -> {
          areaMapper.updateById(area);
          relationList.forEach(relation -> relation.setAreaId(area.getId()));
          areaDeviceMapper.deleteByAreaId(id);
          areaDeviceMapper.insertList(relationList);
          policyService.updatePolicyIfNeed(orgArea.getWarehouseId(), id, deviceList);
          return true;
        });
    // 更新缓存
    AreaCacheModel cacheModel = envDataCache.getArea(id);
    if (Objects.isNull(cacheModel)) {
      cacheModel = new AreaCacheModel();
      cacheModel.setId(id);
      cacheModel.setWarehouseId(orgArea.getWarehouseId());
    }
    cacheModel.setDeviceIdSet(new HashSet<>(deviceIdList));
    envDataCache.putArea(cacheModel);
    return BaseResult.success();
  }

  @Override
  public BaseResult deleteArea(AreaDeleteRequest request) {
    Long id = request.getId();
    Area area = areaMapper.selectById(id);
    if (Objects.isNull(area)) {
      return new SimpleResult<>("区域不存在");
    }
    transactionTemplate.execute(
        status -> {
          areaMapper.deleteById(id, System.currentTimeMillis());
          areaDeviceMapper.deleteByAreaId(id);
          return true;
        });
    // 删除缓存
    envDataCache.removeArea(id);
    return BaseResult.success();
  }

  private List<AreaListVO> getEnvDeviceAreaVOList(
      List<Area> areaList, List<AreaDevice> areaDeviceList) {
    if (CollUtil.isEmpty(areaList)) {
      return new ArrayList<>();
    }
    List<AreaListVO> voList = AreaConvert.getAreaListVOList(areaList);
    // 从缓存中读取数据
    for (AreaListVO vo : voList) {
      AreaCacheModel areaCacheModel = envDataCache.getArea(vo.getId());
      vo.setTemperature(areaCacheModel.getTemperature());
      vo.setHumidity(areaCacheModel.getHumidity());
      vo.setCo2(areaCacheModel.getCo2());
      vo.setTvoc(areaCacheModel.getTvoc());
      vo.setPm10(areaCacheModel.getPm10());
      vo.setPm25(areaCacheModel.getPm25());
    }
    // 完善库房名称
    List<Long> warehouseIdList = areaList.stream().map(Area::getWarehouseId).toList();
    List<Warehouse> warehouseList = warehouseMapper.selectNoAndNameList(warehouseIdList, true);
    Map<Long, Warehouse> warehouseMap =
        warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
    for (AreaListVO vo : voList) {
      Warehouse warehouse = warehouseMap.get(vo.getWarehouseId());
      if (Objects.nonNull(warehouse)) {
        vo.setWarehouseName(warehouse.getWarehouseName());
      }
    }
    // 设备id集合补充
    Map<Long, List<Long>> areaDeviceMap =
        areaDeviceList.stream()
            .collect(
                Collectors.groupingBy(
                    AreaDevice::getAreaId,
                    Collectors.mapping(AreaDevice::getEnvDeviceId, Collectors.toList())));
    for (AreaListVO vo : voList) {
      vo.setDeviceIdList(areaDeviceMap.get(vo.getId()));
    }
    return voList;
  }
}
