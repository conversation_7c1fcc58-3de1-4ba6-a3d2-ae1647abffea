package org.zxwl.smart.service.warehouse;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.warehouse.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.warehouse.WarehouseBuildingVO;
import org.zxwl.smart.domain.response.warehouse.WarehouseFloorVO;
import org.zxwl.smart.domain.response.warehouse.WarehouseVO;

@Validated
public interface WarehouseService {

  int generateDefaultWarehouseIfNeed();

  ListResult<WarehouseBuildingVO> getWarehouseBuildingList(
      @NotNull @Valid BuildingListRequest request);

  SimpleResult<BaseCreateVO> createWarehouseBuilding(@NotNull @Valid BuildingCreateRequest request);

  BaseResult updateWarehouseBuilding(@NotNull @Valid BuildingUpdateRequest request);

  BaseResult deleteWarehouseBuilding(@NotNull @Valid BuildingDeleteRequest request);

  ListResult<WarehouseFloorVO> getWarehouseFloorList(@NotNull @Valid FloorListRequest request);

  SimpleResult<BaseCreateVO> createWarehouseFloor(@NotNull @Valid FloorCreateRequest request);

  BaseResult updateWarehouseFloor(@NotNull @Valid FloorUpdateRequest request);

  BaseResult deleteWarehouseFloor(@NotNull @Valid FloorDeleteRequest request);

  ListResult<WarehouseVO> getWarehouseList(@NotNull @Valid WarehouseListRequest request);

  SimpleResult<WarehouseVO> getWarehouse(@NotNull @Valid WarehouseGetRequest request);

  SimpleResult<BaseCreateVO> createWarehouse(@NotNull @Valid WarehouseCreateRequest request);

  BaseResult updateWarehouse(@NotNull @Valid WarehouseUpdateRequest request);

  BaseResult deleteWarehouse(@NotNull @Valid WarehouseDeleteRequest request);
}
