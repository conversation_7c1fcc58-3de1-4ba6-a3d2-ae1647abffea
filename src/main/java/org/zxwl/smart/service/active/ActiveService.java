package org.zxwl.smart.service.active;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.zxwl.smart.domain.request.active.ActiveByActiveCodeRequest;
import org.zxwl.smart.domain.request.active.GenerateActiveCodeRequest;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.active.ActiveCodeVO;
import org.zxwl.smart.domain.response.active.CurrentDeviceCodeVO;

@Validated
public interface ActiveService {
  SimpleResult<CurrentDeviceCodeVO> getCurrentDeviceCode();

  SimpleResult<ActiveCodeVO> generateActiveCode(@NotNull @Valid GenerateActiveCodeRequest request);

  BaseResult activeByActiveCode(@NotNull @Valid ActiveByActiveCodeRequest request);
}
