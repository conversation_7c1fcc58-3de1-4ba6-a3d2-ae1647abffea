package org.zxwl.smart.service.active.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.cache.ActiveCache;
import org.zxwl.smart.cache.model.ActiveCodeModel;
import org.zxwl.smart.common.convert.active.ActiveConvert;
import org.zxwl.smart.common.properties.ProjectProperties;
import org.zxwl.smart.common.utils.ActiveUtil;
import org.zxwl.smart.common.utils.OSSUtil;
import org.zxwl.smart.domain.request.active.ActiveByActiveCodeRequest;
import org.zxwl.smart.domain.request.active.GenerateActiveCodeRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.active.ActiveCodeVO;
import org.zxwl.smart.domain.response.active.CurrentDeviceCodeVO;
import org.zxwl.smart.mybatis.entity.user.User;
import org.zxwl.smart.mybatis.mapper.user.UserMapper;
import org.zxwl.smart.service.active.ActiveService;

@Slf4j
@Service
@AllArgsConstructor
public class ActiveServiceImpl implements ActiveService {

  private final UserMapper userMapper;
  private final ProjectProperties projectProperties;

  @Override
  public SimpleResult<CurrentDeviceCodeVO> getCurrentDeviceCode() {
    ActiveCodeModel.Server server = ActiveCache.getServer();
    String activeDeviceModelJson = JSONUtil.parseObj(server).toString();
    if (StrUtil.isEmpty(server.getSystemSN())
        && StrUtil.isEmpty(server.getProcessorId())
        && StrUtil.isEmpty(server.getDiskSN())) {
      return new SimpleResult<>("当前服务器“系统序列号”、“cpu序列号”、“第一个硬盘序列号”均为空，无法生成设备码，请联系管理员");
    }
    RSA rsa = SecureUtil.rsa(null, projectProperties.getDeviceCodeRsaPublicKey());
    String result = rsa.encryptBase64(activeDeviceModelJson, KeyType.PublicKey);
    CurrentDeviceCodeVO vo = new CurrentDeviceCodeVO();
    vo.setDeviceCode(result);
    return new SimpleResult<>(vo);
  }

  @Override
  public SimpleResult<ActiveCodeVO> generateActiveCode(GenerateActiveCodeRequest request) {
    ActiveCodeModel activeCodeModel = ActiveConvert.getGenerateActiveCodeModel(request);
    // 激活策略
    activeCodeModel.setPolicy(new ActiveCodeModel.Policy());
    List<Date> expireTimeRange = request.getExpireTimeRange();
    if (Objects.nonNull(expireTimeRange)) {
      activeCodeModel.getPolicy().setStartTime(expireTimeRange.get(0).getTime());
      activeCodeModel.getPolicy().setExpireTime(expireTimeRange.get(1).getTime());
    }
    // 服务器信息
    activeCodeModel.setServer(new ActiveCodeModel.Server());
    String deviceCode = request.getDeviceCode();
    RSA rsa = SecureUtil.rsa(projectProperties.getDeviceCodeRsaPrivateKey(), null);
    String activeDeviceModelJson = rsa.decryptStr(deviceCode, KeyType.PrivateKey);
    if (StrUtil.isNotEmpty(activeDeviceModelJson)) {
      ActiveCodeModel.Server server = JSONUtil.toBean(activeDeviceModelJson, ActiveCodeModel.Server.class);
      activeCodeModel.getServer().setSystemSN(server.getSystemSN());
      activeCodeModel.getServer().setProcessorId(server.getProcessorId());
      activeCodeModel.getServer().setDiskSN(server.getDiskSN());
    }
    // 元数据
    activeCodeModel.setMetadata(new ActiveCodeModel.Metadata());
    Long userId = AccountLocal.getUserId();
    User user = userMapper.selectById(userId);
    if (ObjectUtil.isNotNull(user)) {
      activeCodeModel.getMetadata().setOperator(user.getUserName());
    }
    activeCodeModel.getMetadata().setGenTime(System.currentTimeMillis());

    // 加密为激活码并返回
    String jsonStr = JSONUtil.toJsonStr(activeCodeModel);
    String result =
        SecureUtil.rsa(null, projectProperties.getActiveCodeRsaPublicKey())
            .encryptBase64(jsonStr, KeyType.PublicKey);
    ActiveCodeVO vo = new ActiveCodeVO();
    vo.setActiveCode(result);
    return new SimpleResult<>(vo);
  }

  @Override
  public BaseResult activeByActiveCode(ActiveByActiveCodeRequest request) {
    // 激活码转bo
    String activeCode = request.getActiveCode();
    RSA rsa = SecureUtil.rsa(projectProperties.getActiveCodeRsaPrivateKey(), null);
    String decryptStr = rsa.decryptStr(activeCode, KeyType.PrivateKey);
    ActiveCodeModel activeCodeModel = JSONUtil.toBean(decryptStr, ActiveCodeModel.class);
    if (ObjectUtil.isNull(activeCodeModel)) {
      return BaseResult.failed("激活码解析失败");
    }

    // 验证服务器信息
    ActiveCodeModel.Server server = activeCodeModel.getServer();
    if (!ActiveUtil.checkServerInfo(server)) {
      return BaseResult.failed("激活码中的服务器信息与当前服务器不匹配");
    }
    // 缓存激活信息
    ActiveCache.setActiveCodeModel(activeCodeModel);
    // 保存激活码到项目目录
    try (InputStream inputStream =
        new ByteArrayInputStream(activeCode.getBytes(StandardCharsets.UTF_8))) {
      OSSUtil.saveFile(
          inputStream,
          projectProperties.getActiveCodeFileName(),
          projectProperties.getActiveCodeFilePath());
      return BaseResult.success();
    } catch (Exception e) {
      log.error("文件上传失败", e);
      return new SimpleResult<>("文件上传失败：" + e.getMessage());
    }
  }
}
