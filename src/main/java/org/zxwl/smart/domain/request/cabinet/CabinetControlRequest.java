package org.zxwl.smart.domain.request.cabinet;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.zxwl.smart.constant.enums.cabinet.CabinetControlCmdEnum;
import org.zxwl.smart.domain.annotation.EnumValue;
import org.zxwl.smart.domain.request.BaseRequest;

@Data
public class CabinetControlRequest extends BaseRequest {

  @Schema(description = "柜组id")
  @NotNull(message = "柜组id不能为空")
  private Long cabinetGroupId;

  @Schema(description = "柜子id，不传则默认盘点整个柜组")
  private Long cabinetId;

  @Schema(description = "控制指令")
  @EnumValue(enumClass = CabinetControlCmdEnum.class, message = "控制指令非法")
  private String cmd;
}
