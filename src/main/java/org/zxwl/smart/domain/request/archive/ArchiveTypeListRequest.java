package org.zxwl.smart.domain.request.archive;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.zxwl.smart.constant.enums.archive.ArchiveTypeTypeEnum;
import org.zxwl.smart.domain.annotation.EnumValue;
import org.zxwl.smart.domain.request.BaseRequest;

@Data
public class ArchiveTypeListRequest extends BaseRequest {

  @Schema(description = "门类类型，archive档案级，box盒级")
  @NotBlank(message = "门类类型不能为空")
  @EnumValue(enumClass = ArchiveTypeTypeEnum.class, message = "门类类型非法")
  private String type;
}
