package org.zxwl.smart.domain.request.archive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.zxwl.smart.domain.request.PageRequest;

@Data
public class OperatorRecordShelveListRequest extends PageRequest {

  @Schema(description = "档案名称精确查询")
  private String archiveName;

  @Schema(description = "档案名称模糊查询")
  private String archiveNameLike;

  @Schema(description = "档案编号精确查询")
  private String archiveNo;

  @Schema(description = "档案编号模糊查询")
  private String archiveNoLike;
}
