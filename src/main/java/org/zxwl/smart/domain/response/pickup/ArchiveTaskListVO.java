package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ArchiveTaskListVO {

  @Schema(description = "任务id")
  private Long id;

  @Schema(description = "档案id")
  private Long archiveId;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "装盒状态，1已装盒，0未装盒")
  private Boolean boxStatus;

  @Schema(description = "名字")
  private String archiveName;

  @Schema(description = "编号")
  private String archiveNo;

  @Schema(description = "位置")
  private String location;

  @Schema(description = "任务类型，in存入，out取出")
  private String taskType;

  @Schema(description = "状态，pending待执行，executing执行中，pending_pickup待取出，completed完成")
  private String status;

  @Schema(description = "执行方式")
  private String executionType;

  @Schema(description = "创建时间")
  private Date createAt;

  @Schema(description = "载具类型，archive_cabinet-档案柜，hr_cabinet-人事柜，rack-密集架")
  private String storageType;
}
