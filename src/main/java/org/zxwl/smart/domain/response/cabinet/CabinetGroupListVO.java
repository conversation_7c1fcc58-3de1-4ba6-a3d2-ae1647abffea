package org.zxwl.smart.domain.response.cabinet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CabinetGroupListVO {

  @Schema(description = "柜组id")
  private Long id;

  @Schema(description = "所属库房id")
  private Long warehouseId;

  @Schema(description = "所属库房编号")
  private Integer warehouseNo;

  @Schema(description = "所属库房名称")
  private String warehouseName;

  @Schema(description = "柜组编号")
  private Integer groupNo;

  @Schema(description = "柜组名")
  private String groupName;

  @Schema(description = "主柜编号")
  private Integer mainCabinetNo;

  @Schema(description = "副柜数量")
  private Integer subCabinetCount;

  @Schema(description = "主柜格数")
  private Integer mainCabinetGridCount;

  @Schema(description = "副柜格数")
  private Integer subCabinetGridCount;

  @Schema(description = "格口容量")
  private Integer cabinetGridCapacity;

  @Schema(description = "通信ip")
  private String controlIp;

  @Schema(description = "通信端口")
  private Integer controlPort;

  @Schema(description = "容量")
  private Integer capacity;

  @Schema(description = "已用容量")
  private Integer usedCapacity;

  @Schema(description = "备注")
  private String remark;

  @Schema(description = "地图配置信息")
  private String visualConfig;
}
