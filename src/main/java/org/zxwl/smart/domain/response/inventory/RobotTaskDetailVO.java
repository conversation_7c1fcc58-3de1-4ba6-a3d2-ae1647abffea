package org.zxwl.smart.domain.response.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RobotTaskDetailVO {

  @Schema(description = "主键id")
  private Long id;

  @Schema(description = "密集架组id")
  private Long rackGroupId;

  @Schema(description = "密集架组编号")
  private Long rackGroupNo;

  @Schema(description = "密集架列编号")
  private Long rackColumNo;

  @Schema(description = "密集架面编号")
  private Long rackPanelNo;
}
