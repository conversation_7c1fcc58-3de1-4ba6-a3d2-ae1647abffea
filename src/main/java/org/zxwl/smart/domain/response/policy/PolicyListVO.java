package org.zxwl.smart.domain.response.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
public class PolicyListVO {

  @Schema(description = "策略id")
  private Long id;

  @Schema(description = "所属库房id")
  private Long warehouseId;

  @Schema(description = "所属库房名称")
  private String warehouseName;

  @Schema(description = "所属区域id")
  private Long areaId;

  @Schema(description = "所属区域名称")
  private String areaName;

  @Schema(description = "是否是系统自动生成，0否，1是")
  private Boolean system;

  @Schema(description = "是否启用，0否，1是")
  private Boolean enabled;

  @Schema(description = "策略类型")
  private String policyType;

  @Schema(description = "策略名称")
  private String policyName;

  @Schema(description = "策略描述")
  private String policyDescription;

  @Schema(description = "创建时间")
  private Date createdAt;
}
