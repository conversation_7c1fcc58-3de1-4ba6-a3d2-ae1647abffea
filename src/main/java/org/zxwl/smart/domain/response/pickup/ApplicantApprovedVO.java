package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
public class ApplicantApprovedVO {

  @Schema(description = "审批人id")
  private Long approverId;

  @Schema(description = "审批人姓名")
  private String approverName;

  @Schema(description = "审批时间")
  private Date approvalAt;

  @Schema(description = "审批意见")
  private String approvalComment;
}
