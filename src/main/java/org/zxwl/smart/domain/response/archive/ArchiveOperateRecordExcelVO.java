package org.zxwl.smart.domain.response.archive;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.date.DateStringConverter;
import java.util.Date;
import lombok.Data;

@Data
public class ArchiveOperateRecordExcelVO {

  @ExcelProperty("档案编号")
  private String archiveNo;

  @ExcelProperty("档案名称")
  private String archiveName;

  @ExcelProperty("档案位置")
  private String location;

  @ExcelIgnore private Long operatorId;

  @ExcelProperty("操作人")
  private String operatorName;

  @ExcelProperty("操作类型")
  private String operatorType;

  @ExcelProperty(value = "操作时间", converter = DateStringConverter.class)
  @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
  private Date createdAt;
}
