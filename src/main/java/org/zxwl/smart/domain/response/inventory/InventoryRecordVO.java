package org.zxwl.smart.domain.response.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class InventoryRecordVO {

  @Schema(description = "盘点记录id")
  private Long id;

  @Schema(description = "机构id")
  private Long organizationId;

  @Schema(description = "盘点任务id")
  private Long inventoryTaskId;

  @Schema(description = "库房id")
  private Long warehouseId;

  @Schema(description = "盘点类型，handheld-手持机，inventory_car-盘点车，cabinet-智能柜，rack-密集架")
  private String inventorType;

  @Schema(description = "在架数")
  private Integer onShelfCount;

  @Schema(description = "不在架数")
  private Integer offShelfCount;

  @Schema(description = "错架数")
  private Integer misplacedCount;

  @Schema(description = "在借数")
  private Integer borrowCount;

  @Schema(description = "未注册数")
  private Integer unbindCount;

  @Schema(description = "密集架组号")
  private Integer rackGroupNo;

  @Schema(description = "密集架列号")
  private Integer rackColumnNo;

  @Schema(description = "密集架面号")
  private Integer rackPanelNo;

  @Schema(description = "密集架节号")
  private Integer rackSectionNo;

  @Schema(description = "密集架层号")
  private Integer rackLayerNo;

  @Schema(description = "档案所在人事柜组号")
  private Integer hrCabinetGroupNo;

  @Schema(description = "档案所在人事柜号")
  private Integer hrCabinetNo;

  @Schema(description = "档案所在人事柜层号")
  private Integer hrCabinetLayerNo;

  @Schema(description = "档案所在柜组号")
  private Integer cabinetGroupNo;

  @Schema(description = "档案所在柜号")
  private Integer cabinetNo;

  @Schema(description = "档案所在柜格号")
  private Integer cabinetGridNo;

  @Schema(description = "备注")
  private String remark;

  @Schema(description = "创建时间")
  private Date createdAt;

  @Schema(description = "位置信息")
  private String location;

  @Schema(description = "盘点详情列表")
  private List<DetailVO> detailList;

  @Schema(name = "InventoryRecordDetailVO")
  @Data
  public static class DetailVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "密集架组号")
    private Integer rackGroupNo;

    @Schema(description = "密集架列号")
    private Integer rackColumnNo;

    @Schema(description = "密集架面号")
    private Integer rackPanelNo;

    @Schema(description = "密集架节号")
    private Integer rackSectionNo;

    @Schema(description = "密集架层号")
    private Integer rackLayerNo;

    @Schema(description = "密集架格号")
    private Integer rackGridNo;

    @Schema(description = "档案所在人事柜组号")
    private Integer hrCabinetGroupNo;

    @Schema(description = "档案所在人事柜号")
    private Integer hrCabinetNo;

    @Schema(description = "档案所在人事柜层号")
    private Integer hrCabinetLayerNo;

    @Schema(description = "档案所在人事柜格号")
    private Integer hrCabinetGridNo;

    @Schema(description = "档案所在柜组号")
    private Integer cabinetGroupNo;

    @Schema(description = "档案所在柜号")
    private Integer cabinetNo;

    @Schema(description = "档案所在柜格号")
    private Integer cabinetGridNo;

    @Schema(description = "档案id")
    private Long archiveId;

    @Schema(description = "档案编号")
    private String archiveNo;

    @Schema(description = "档案名称")
    private String archiveName;

    @Schema(description = "标签tid")
    private String tid;

    @Schema(description = "结果状态，on_shelf-在架，off_shelf-不在架，misplaced-错架，borrow-外借，unbind-未注册")
    private String status;

    @Schema(description = "位置")
    private String location;
  }
}
