package org.zxwl.smart.domain.response;

import lombok.Getter;

@Getter
public enum ResultCodeEnum {
  SUCCESS(1000, "操作成功"),
  FAILED(1001, "操作失败"),
  UNAUTHORIZED(1002, "认证失败或登录过期，请重新登录"),
  FORBIDDEN(1003, "权限不足"),
  PARAM_FAILED(1004, "参数校验失败"),
  SERVER_ERROR(5000, "服务端错误");

  private final int code;
  private final String msg;

  ResultCodeEnum(int code, String msg) {
    this.code = code;
    this.msg = msg;
  }
}
