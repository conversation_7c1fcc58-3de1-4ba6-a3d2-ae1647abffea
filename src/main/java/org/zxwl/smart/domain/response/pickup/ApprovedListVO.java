package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ApprovedListVO {

  @Schema(description = "申请记录id")
  private Long id;

  @Schema(description = "档案id")
  private Long archiveId;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "类型，box档案盒，archive档案，volume卷内档案")
  private String type;

  @Schema(description = "名字")
  private String archiveName;

  @Schema(description = "编号")
  private String archiveNo;

  @Schema(description = "位置")
  private String location;

  @Schema(description = "申请人姓名")
  private String applicantName;

  @Schema(description = "经办人id")
  private Long operatorId;

  @Schema(description = "经办人姓名")
  private String operatorName;

  @Schema(description = "申请备注")
  private String remark;

  @Schema(description = "申请创建时间")
  private Date applicantCreatedAt;

  @Schema(description = "审批状态，approved审批通过，reject审批拒绝")
  private String approvalStatus;

  @Schema(description = "审批时间")
  private Date approvalAt;

  @Schema(description = "审批意见")
  private String approvalComment;
}
