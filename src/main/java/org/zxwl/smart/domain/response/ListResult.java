package org.zxwl.smart.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

@Schema(description = "集合响应结果")
@Getter
public class ListResult<T> extends BaseResult {

  @Schema(description = "响应数据")
  private List<T> list;

  public ListResult(List<T> list) {
    super(ResultCodeEnum.SUCCESS);
    this.list = Objects.nonNull(list) ? list : Collections.emptyList();
  }

  public ListResult(String msg) {
    super(ResultCodeEnum.FAILED.getCode(), msg);
    this.list = Collections.emptyList();
  }

  public ListResult(BaseResult baseResult) {
    super(baseResult.getCode(), baseResult.getMsg());
    this.list = Collections.emptyList();
  }
}
