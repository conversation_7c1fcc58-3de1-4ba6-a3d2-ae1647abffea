package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ApplicantVO {

  @Schema(description = "申请记录id")
  private Long id;

  @Schema(description = "档案id")
  private Long archiveId;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "装盒状态，1已装盒，0未装盒")
  private Boolean boxStatus;

  @Schema(description = "名字")
  private String archiveName;

  @Schema(description = "编号")
  private String archiveNo;

  @Schema(description = "位置")
  private String location;

  @Schema(description = "状态，pending待审批，approved审批通过，reject审批拒绝，revoke已撤销")
  private String status;

  @Schema(description = "借阅目的，internal内部查阅，external外部查阅，transfer移交，outbound出库")
  private String borrowIntent;

  @Schema(description = "申请人姓名")
  private String applicantName;

  @Schema(description = "申请人手机号")
  private String applicantPhone;

  @Schema(description = "申请人邮箱")
  private String applicantEmail;

  @Schema(description = "借阅天数")
  private Integer borrowDays;

  @Schema(description = "借阅时间")
  private Date borrowAt;

  @Schema(description = "经办人id")
  private Long operatorId;

  @Schema(description = "经办人姓名")
  private String operatorName;

  @Schema(description = "备注")
  private String remark;

  @Schema(description = "应归还时间")
  private Date needBackAt;

  @Schema(description = "创建时间")
  private Date createdAt;

  @Schema(description = "审批记录")
  private ApplicantApprovedVO approved;
}
