package org.zxwl.smart.domain.response.archive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ArchiveDeletedListVO {

  @Schema(description = "档案id")
  private Long id;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "类型，box档案盒，archive档案，volume卷内档案")
  private String type;

  @Schema(description = "名字")
  private String archiveName;

  @Schema(description = "编号")
  private String archiveNo;

  @Schema(description = "删除时间")
  private Date deleteTime;
}
