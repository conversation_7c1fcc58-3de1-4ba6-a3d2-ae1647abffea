package org.zxwl.smart.domain.response.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class PolicyActionVO {

  @Schema(description = "类型，设备控制，系统消息通知，短信通知")
  private String type;

  @Schema(description = "设备id")
  private Long deviceId;

  @Schema(description = "设备名称")
  private String deviceName;

  @Schema(description = "控制字段名")
  private List<String> controlFieldNameList;

  @Schema(description = "结束控制字段名")
  private List<String> endControlFieldNameList;

  @Schema(description = "短信通知或电话通知手机号")
  private String phone;
}
