package org.zxwl.smart.domain.response.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WarehouseVO {

  @Schema(description = "库房id")
  private Long id;

  @Schema(description = "所属建筑id")
  private Long buildingId;

  @Schema(description = "所属建筑编号")
  private Integer buildingNo;

  @Schema(description = "所属建筑名")
  private String buildingName;

  @Schema(description = "所属楼层id")
  private Long floorId;

  @Schema(description = "所属楼层编号")
  private Integer floorNo;

  @Schema(description = "所属楼层名")
  private String floorName;

  @Schema(description = "库房编号")
  private Integer warehouseNo;

  @Schema(description = "库房名")
  private String warehouseName;

  @Schema(description = "地图配置信息")
  private String visualConfig;
}
