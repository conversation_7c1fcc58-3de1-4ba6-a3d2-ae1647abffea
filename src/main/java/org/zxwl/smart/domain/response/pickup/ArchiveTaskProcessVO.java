package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ArchiveTaskProcessVO {
    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "任务完成状态，uncompleted未完成，completed已完成")
    private String completionStatus;

    @Schema(description = "任务执行过程名称")
    private String processName;

    @Schema(description = "任务执行过程时间")
    private Date processTime;

    @Schema(description = "任务执行过程描述")
    private String processDescription;
}