package org.zxwl.smart.domain.response.archive;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ArchivePendingStorageListVO {

  @Schema(description = "档案id")
  private Long id;

  @Schema(description = "所属库房id")
  private Long warehouseId;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "上层id（盒id或档案id）")
  private Long parentId;

  @Schema(description = "类型，box档案盒，archive档案，volume卷内档案")
  private String type;

  @Schema(description = "装盒状态，1已装盒，0未装盒")
  private Boolean boxStatus;

  @Schema(description = "在库状态，in在库，out不在库")
  private String stockStatus;

  @Schema(description = "在架状态，pending_in待上架，in在架，pending_out待下架，out不在架")
  private String shelvingStatus;

  @Schema(description = "名字")
  private String archiveName;

  @Schema(description = "编号")
  private String archiveNo;

  @Schema(description = "标签TID")
  private String tid;

  @Schema(description = "档案所在密集架组号")
  private Integer rackGroupNo;

  @Schema(description = "档案所在密集架列号")
  private Integer rackColumnNo;

  @Schema(description = "档案所在密集架面号")
  private Integer rackPanelNo;

  @Schema(description = "档案所在密集架节号")
  private Integer rackSectionNo;

  @Schema(description = "档案所在密集架层号")
  private Integer rackLayerNo;

  @Schema(description = "档案所在密集架格号")
  private Integer rackGridNo;

  @Schema(description = "档案所在人事柜组号")
  private Integer hrCabinetGroupNo;

  @Schema(description = "档案所在人事柜号")
  private Integer hrCabinetNo;

  @Schema(description = "档案所在人事柜层号")
  private Integer hrCabinetLayerNo;

  @Schema(description = "档案所在人事柜格号")
  private Integer hrCabinetGridNo;

  @Schema(description = "档案所在柜组号")
  private Integer cabinetGroupNo;

  @Schema(description = "档案所在柜号")
  private Integer cabinetNo;

  @Schema(description = "档案所在柜格号")
  private Integer cabinetGridNo;

  @Schema(description = "载具类型，archive_cabinet-档案柜，hr_cabinet-人事柜，rack-密集架")
  private String storageType;

  @Schema(description = "位置")
  private String location;

  @Schema(description = "创建时间")
  private Date createdAt;

  @Schema(description = "子档案数量")
  private Integer subArchiveCount;

  @Schema(description = "字段列表")
  private List<ArchiveFieldValueVO> fieldValueList;

  @Schema(description = "取出申请人id")
  private Long pickupCreatedId;

  @Schema(description = "取出申请人姓名")
  private String pickupCreatedName;

  @Schema(description = "取出经办人id")
  private Long pickupOperatorId;

  @Schema(description = "取出经办人姓名")
  private String pickupOperatorName;

  @Schema(description = "取出时间")
  private Date pickupTime;

  @Schema(description = "取档申请id")
  private Long applicantId;
}
