package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ApprovedVO {

  @Schema(description = "申请记录id")
  private Long id;

  @Schema(description = "档案id")
  private Long archiveId;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "类型，box档案盒，archive档案，volume卷内档案")
  private String type;

  @Schema(description = "名字")
  private String archiveName;

  @Schema(description = "编号")
  private String archiveNo;

  @Schema(description = "位置")
  private String location;

  @Schema(description = "借阅目的，internal内部查阅，external外部查阅，transfer移交，outbound出库")
  private String borrowIntent;

  @Schema(description = "申请人姓名")
  private String applicantName;

  @Schema(description = "申请人手机号")
  private String applicantPhone;

  @Schema(description = "申请人邮箱")
  private String applicantEmail;

  @Schema(description = "借阅天数")
  private Integer borrowDays;

  @Schema(description = "借阅时间")
  private Date borrowAt;

  @Schema(description = "经办人id")
  private Long operatorId;

  @Schema(description = "经办人姓名")
  private String operatorName;

  @Schema(description = "备注")
  private String remark;

  @Schema(description = "应归还时间")
  private Date needBackAt;

  @Schema(description = "审批状态，approved审批通过，reject审批拒绝")
  private String approvalStatus;

  @Schema(description = "审批时间")
  private Date approvalAt;

  @Schema(description = "审批意见")
  private String approvalComment;
}
