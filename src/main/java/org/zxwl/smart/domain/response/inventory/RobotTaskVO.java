package org.zxwl.smart.domain.response.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RobotTaskVO {

  @Schema(description = "主键id")
  private Long id;

  @Schema(description = "盘点库房id")
  private Long warehouseId;

  @Schema(description = "任务名字")
  private String taskName;

  @Schema(description = "任务状态，unstarted未开启，started已开始，finished完成")
  private String taskStatus;

  @Schema(description = "盘点机器人id")
  private Long robotId;

  @Schema(description = "盘点机器人名字")
  private Long robotName;

  @Schema(description = "任务周期，single单次，weekly每周，monthly每月，yearly每年")
  private String taskCycle;

  @Schema(description = "任务执行周期天数")
  private Integer taskCycleDay;

  @Schema(description = "任务执行时间，时间格式 HH:mm:ss")
  @JsonFormat(pattern = "HH:mm:ss")
  private Date taskTime;

  @Schema(description = "任务详情列表")
  private List<RobotTaskDetailVO> taskDetailList;
}
