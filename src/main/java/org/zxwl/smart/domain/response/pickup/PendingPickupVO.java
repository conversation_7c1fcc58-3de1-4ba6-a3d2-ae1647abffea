package org.zxwl.smart.domain.response.pickup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PendingPickupVO {

  @Schema(description = "记录id")
  private Long id;

  @Schema(description = "档案id")
  private Long archiveId;

  @Schema(description = "档案门类id")
  private Long archiveTypeId;

  @Schema(description = "档案门类名")
  private String archiveTypeName;

  @Schema(description = "类型，box档案盒，archive档案，volume卷内档案")
  private String type;

  @Schema(description = "文件名字")
  private String archiveName;

  @Schema(description = "文件编号")
  private String archiveNo;

  @Schema(description = "位置")
  private String location;
}
