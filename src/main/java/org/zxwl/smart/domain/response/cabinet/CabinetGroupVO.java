package org.zxwl.smart.domain.response.cabinet;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class CabinetGroupVO {

  @Schema(description = "主键id")
  private Long id;

  @Schema(description = "所属库房id")
  private Long warehouseId;

  @Schema(description = "所属库房编号")
  private Integer warehouseNo;

  @Schema(description = "所属库房名称")
  private String warehouseName;

  @Schema(description = "柜组编号")
  private Integer groupNo;

  @Schema(description = "柜组名")
  private String groupName;

  @Schema(description = "主柜编号")
  private Integer mainCabinetNo;

  @Schema(description = "副柜数量")
  private Integer subCabinetCount;

  @Schema(description = "主柜格数")
  private Integer mainCabinetGridCount;

  @Schema(description = "副柜格数")
  private Integer subCabinetGridCount;

  @Schema(description = "格口容量")
  private Integer cabinetGridCapacity;

  @Schema(description = "通信ip")
  private String controlIp;

  @Schema(description = "通信端口")
  private Integer controlPort;

  @Schema(description = "容量")
  private Integer capacity;

  @Schema(description = "已用容量")
  private Integer usedCapacity;

  @Schema(description = "备注")
  private String remark;

  @Schema(description = "地图配置信息")
  private String visualConfig;

  @Schema(description = "柜子信息列表")
  private List<CabinetVO> cabinetList;

  @Data
  public static class CabinetVO {

    @Schema(description = "柜子id")
    private Long id;

    @Schema(description = "柜子编号")
    private Integer cabinetNo;

    @Schema(description = "是否是主柜 0不是，1是")
    private Boolean mainCabinet;

    @Schema(description = "柜子总格数")
    private Integer cabinetGridNum;

    @Schema(description = "容量")
    private Integer capacity;

    @Schema(description = "已用容量")
    private Integer usedCapacity;

    @Schema(description = "格口信息列表")
    private List<GridVO> cabinetGridList;
  }

  @Data
  @Schema(name = "CabinetGridVO")
  public static class GridVO {

    @Schema(description = "格口id")
    private Long id;

    @Schema(description = "格子容量")
    private Integer capacity;

    @Schema(description = "柜子已用容量")
    private Integer usedCapacity;

    @Schema(description = "格口状态，normal正常，open打开，lock锁定")
    private String gridStatus;

    @Schema(description = "格子编号")
    private Integer gridNo;

    @Schema(description = "格口档案列表")
    private List<ArchiveVO> archiveList;
  }

  @Data
  @Schema(name = "CabinetGridArchiveVO")
  public static class ArchiveVO {

    @Schema(description = "档案id")
    private Long archiveId;

    @Schema(description = "在架状态，pending_in待上架，in在架，pending_out待下架，out不在架")
    private String shelvingStatus;

    @Schema(description = "档案名称")
    private String archiveName;

    @Schema(description = "档案编号")
    private String archiveNo;

    @Schema(description = "标签TID")
    private String tid;
  }
}
