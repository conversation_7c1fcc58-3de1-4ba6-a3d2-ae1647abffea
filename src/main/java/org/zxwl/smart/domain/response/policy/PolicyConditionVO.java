package org.zxwl.smart.domain.response.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PolicyConditionVO {

  @Schema(description = "监听的扩展字段名")
  private String fieldName;

  @Schema(description = "监听的扩展字段操作符，=、>、>=、<、<=")
  private String operator;

  @Schema(description = "监听的扩展字段值")
  private String fieldValue;

  @Schema(description = "浮动区间")
  private String rangeValue;
}
