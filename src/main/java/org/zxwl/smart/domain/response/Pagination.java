package org.zxwl.smart.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "分页信息")
@Data
public class Pagination {

  @Schema(description = "当前页码", example = "1")
  private int pageNum;

  @Schema(description = "每页数量", example = "10")
  private int pageSize;

  @Schema(description = "总记录数", example = "100")
  private long total;

  @Schema(description = "总页数", example = "10")
  private int pages;
}
