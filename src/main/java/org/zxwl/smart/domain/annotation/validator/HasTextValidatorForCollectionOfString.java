package org.zxwl.smart.domain.annotation.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.zxwl.smart.domain.annotation.HasText;

import java.util.Collection;
import java.util.Objects;

public class HasTextValidatorForCollectionOfString
    implements ConstraintValidator<HasText, Collection<String>> {

  private boolean skipNull;

  @Override
  public void initialize(HasText constraintAnnotation) {
    skipNull = constraintAnnotation.skipNull();
  }

  @Override
  public boolean isValid(Collection<String> valueList, ConstraintValidatorContext context) {
    if (Objects.isNull(valueList) || valueList.isEmpty()) {
      return true;
    }
    for (String value : valueList) {
      if (Objects.isNull(value) && !skipNull) {
        return false;
      }
      if (value.isEmpty()) {
        return false;
      }
      boolean isWhitespace = true;
      int strLen = value.length();
      for (int i = 0; i < strLen; i++) {
        if (!Character.isWhitespace(value.charAt(i))) {
          isWhitespace = false;
          break;
        }
      }
      if (isWhitespace) {
        return false;
      }
    }
    return true;
  }
}
