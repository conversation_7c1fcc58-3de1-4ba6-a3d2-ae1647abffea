package org.zxwl.smart.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.zxwl.smart.domain.excel.DoorControlUserImportTemplate;
import org.zxwl.smart.domain.request.env.DoorControlUserCreateRequest;
import org.zxwl.smart.domain.response.env.DoorControlUserImportResultVO;

/** 门禁用户导入Excel监听器 */
@Slf4j
@Data
@AllArgsConstructor
public class DoorControlUserImportListener
    extends AnalysisEventListener<DoorControlUserImportTemplate> {

  private static final int BATCH_SIZE = 100;

  private final List<DoorControlUserCreateRequest> successList = new ArrayList<>();

  private final List<DoorControlUserImportResultVO.FailRecord> failList = new ArrayList<>();

  private final Map<DoorControlUserCreateRequest, Integer> rowIndexMap = new HashMap<>();

  private final String deviceNo;

  @Override
  public void invoke(DoorControlUserImportTemplate data, AnalysisContext context) {
    int rowIndex = context.readRowHolder().getRowIndex() + 1;

    try {
      // 数据校验
      if (StringUtils.isBlank(data.getUserName())) {
        addFailRecord(rowIndex, "人员名称不能为空", data);
        return;
      }

      if (StringUtils.isBlank(data.getIdCard())) {
        addFailRecord(rowIndex, "人员工号不能为空", data);
        return;
      }
      if (StringUtils.isBlank(data.getUserPwd())) {
        addFailRecord(rowIndex, "用户密码不能为空", data);
        return;
      }

      // 转换为创建请求对象
      DoorControlUserCreateRequest request = new DoorControlUserCreateRequest();
      request.setUserName(data.getUserName());
      request.setUserPwd(data.getUserPwd());
      request.setCard(data.getCard());
      request.setIdCard(data.getIdCard());
      request.setDeviceNo(deviceNo);

      // 添加到成功列表
      successList.add(request);

      rowIndexMap.put(request, rowIndex);

    } catch (Exception e) {
      log.error("处理Excel行数据异常，行号：{}", rowIndex, e);
      addFailRecord(rowIndex, "数据处理异常：" + e.getMessage(), data);
    }
  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext context) {
    log.info("Excel解析完成，成功：{}条，失败：{}条", successList.size(), failList.size());
  }

  private void addFailRecord(int rowIndex, String reason, DoorControlUserImportTemplate data) {
    DoorControlUserImportResultVO.FailRecord failRecord =
        new DoorControlUserImportResultVO.FailRecord();
    failRecord.setRowIndex(rowIndex);
    failRecord.setReason(reason);
    failRecord.setUserName(data.getUserName());
    failRecord.setIdCard(data.getIdCard());
    failList.add(failRecord);
  }

  public int getRowIndex(DoorControlUserCreateRequest request) {
    return rowIndexMap.getOrDefault(request, 0);
  }
}
