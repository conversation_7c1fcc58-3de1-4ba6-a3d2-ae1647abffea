package org.zxwl.smart.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.zxwl.smart.cache.UserCache;
import org.zxwl.smart.cache.model.JwtToken;
import org.zxwl.smart.cache.model.JwtTokenSubject;
import org.zxwl.smart.common.utils.JwtTokenUtil;
import org.zxwl.smart.constant.consist.AuthorizeConstant;
import org.zxwl.smart.service.user.UserService;

@Slf4j
@Component
@AllArgsConstructor
public class WSJwtInterceptor implements HandshakeInterceptor {

  private final UserCache userCache;
  private final UserService userService;

  @Override
  public boolean beforeHandshake(
      ServerHttpRequest request,
      ServerHttpResponse response,
      WebSocketHandler wsHandler,
      Map<String, Object> attributes)
      throws Exception {
    if (request instanceof ServletServerHttpRequest) {
      HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();
      String uri = servletRequest.getRequestURI();
      String terminal = servletRequest.getParameter(AuthorizeConstant.HEADER_TERMINAL_KEY);
      String jwtTokenKey = AuthorizeConstant.HEADER_AUTHORIZE_KEY;
      String token = servletRequest.getParameter(jwtTokenKey);
      JwtTokenSubject subject = JwtTokenUtil.parseAccessToken(token, JwtTokenSubject.class);
      if (Objects.isNull(subject)
          || Objects.isNull(subject.getUserId())
          || Objects.isNull(subject.getOrganizationId())) {
        String format = "解析 jwt token 失败，uri：{}，token：{}，subject：{}";
        log.error(format, uri, token, subject);
        return false;
      }
      Long organizationId = subject.getOrganizationId();
      Long userId = subject.getUserId();
      // 2、检查是否有登录Token缓存
      JwtToken jwtToken = userCache.getJwtToken(userId, organizationId, terminal);
      if (Objects.isNull(jwtToken)) {
        String format = "解析 jwt token 失败：没有对应的缓存，uri：{}，subject：{}";
        log.error(format, uri, subject);
        return false;
      }
      // 5、缓存JwtToken中携带信息
      attributes.put("userId", userId);
      attributes.put("organizationId", organizationId);
      // AccountLocal.setUserId(userId);
      // AccountLocal.setOrganizationId(organizationId);
      // 6、根据Token携带字段，刷新缓存信息并绑定到当前线程
      userService.refreshUserCacheIfNeed(userId, null);
      return true;
    }
    return false;
  }

  @Override
  public void afterHandshake(
      ServerHttpRequest request,
      ServerHttpResponse response,
      WebSocketHandler wsHandler,
      Exception exception) {}
}
