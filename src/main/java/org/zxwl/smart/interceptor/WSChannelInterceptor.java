package org.zxwl.smart.interceptor;

import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;
import org.zxwl.smart.cache.AccountLocal;

@Slf4j
@Component
public class WSChannelInterceptor implements ChannelInterceptor {

  @Override
  public Message<?> preSend(Message<?> message, MessageChannel channel) {
    StompHeaderAccessor accessor =
        MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
    if (Objects.nonNull(accessor)) {
      Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
      if (sessionAttributes != null) {
        AccountLocal.setUserId((Long) sessionAttributes.get("userId"));
        AccountLocal.setOrganizationId((Long) sessionAttributes.get("organizationId"));
      }
      // 获取 message headers
      log.info("Message Headers: {} {}", Thread.currentThread().getName(), accessor.toMap());
    }
    return message;
  }
}
