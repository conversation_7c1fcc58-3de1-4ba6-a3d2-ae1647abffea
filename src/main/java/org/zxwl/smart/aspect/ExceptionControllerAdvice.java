package org.zxwl.smart.aspect;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import org.zxwl.smart.common.exception.ApplicationException;
import org.zxwl.smart.common.utils.RequestUtil;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ResultCodeEnum;

@Slf4j
@RestControllerAdvice
public class ExceptionControllerAdvice {

  /** 统一处理系统异常 */
  @ResponseBody
  @ExceptionHandler(Exception.class)
  public BaseResult handlerException(HttpServletRequest request, Exception e) {
    if (e instanceof ApplicationException) {
      log.warn("业务异常", e);
    } else {
      log.error("系统异常", e);
    }
    return BaseResult.failed(e.getMessage());
  }

  /** 未找到对应的请求 */
  @ResponseBody
  @ExceptionHandler(NoResourceFoundException.class)
  public BaseResult handlerNoResourceFoundException(
      HttpServletRequest request, NoResourceFoundException e) {
    String header = RequestUtil.getRequestHeaderJsonStr(request);
    log.warn(
        "未找到对应的请求 uri = {} header = {}, message = {}",
        request.getRequestURI(),
        header,
        e.getMessage());
    String message = ResultCodeEnum.FAILED.getMsg();
    if (!e.getMessage().isEmpty()) {
      message = e.getMessage();
    }
    return new BaseResult(ResultCodeEnum.FAILED.getCode(), message);
  }

  /** 处理参数转换异常 */
  @ResponseBody
  @ExceptionHandler(HttpMessageConversionException.class)
  public BaseResult handlerMessageConversionException(
      HttpServletRequest request, HttpMessageConversionException e) {
    String uri = request.getRequestURI();
    String header = RequestUtil.getRequestHeaderJsonStr(request);
    log.warn("请求参数转换异常 uri = {}, {}", uri, header, e);
    String message = ResultCodeEnum.PARAM_FAILED.getMsg();
    if (!e.getMessage().isEmpty()) {
      message = e.getMessage();
    }
    return new BaseResult(ResultCodeEnum.PARAM_FAILED.getCode(), message);
  }

  @ResponseBody
  @ExceptionHandler(HttpMessageNotReadableException.class)
  public BaseResult handlerHttpMessageNotReadableException(
      HttpServletRequest request, HttpMessageNotReadableException e) {
    String uri = request.getRequestURI();
    String header = RequestUtil.getRequestHeaderJsonStr(request);
    log.warn("请求体为空：uri = {}, header = {}, message = {}, e", uri, header, e.getMessage(), e);
    return BaseResult.failed("请求体转换异常，具体错误信息：" + e.getMessage());
  }

  /** 处理 spring boot validation controller 参数校验错误异常 */
  @ResponseBody
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public BaseResult handlerMethodArgumentNotValidException(
      HttpServletRequest request, MethodArgumentNotValidException e) {
    String uri = request.getRequestURI();
    String header = RequestUtil.getRequestHeaderJsonStr(request);
    log.warn("请求参数异常：uri = {}, header = {}, message = {}", uri, header, e.getMessage());
    String message = ResultCodeEnum.PARAM_FAILED.getMsg();
    if (!e.getMessage().isEmpty()) {
      message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
    }
    return new BaseResult(ResultCodeEnum.PARAM_FAILED.getCode(), message);
  }

  /** 处理 spring boot validation service 参数校验错误异常 */
  @ResponseBody
  @ExceptionHandler(ConstraintViolationException.class)
  public BaseResult handlerConstraintViolationException(
      HttpServletRequest request, ConstraintViolationException e) {
    String uri = request.getRequestURI();
    String header = RequestUtil.getRequestHeaderJsonStr(request);
    log.warn("请求参数异常：uri = {}, header = {}, message = {}", uri, header, e.getMessage());
    ConstraintViolation<?> constraintViolation =
        CollectionUtils.firstElement(e.getConstraintViolations());
    String message = ResultCodeEnum.PARAM_FAILED.getMsg();
    if (!ObjectUtils.isEmpty(constraintViolation)) {
      message = constraintViolation.getMessage();
    }
    return new BaseResult(ResultCodeEnum.PARAM_FAILED.getCode(), message);
  }
}
