package org.zxwl.smart.controller.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.user.UserLoginRequest;
import org.zxwl.smart.domain.request.user.UserOrganizationSwitchRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.UserLoginVO;
import org.zxwl.smart.service.user.UserService;

@Tag(name = "✅用户")
@ZHPTRestController("/user")
@AllArgsConstructor
public class UserController {

  private final UserService userService;

  @Operation(summary = "切换用户机构")
  @PostMapping("/switchUserOrganization")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  public BaseResult switchUserOrganization(
      @RequestBody UserOrganizationSwitchRequest request, HttpServletResponse response) {
    return userService.switchUserOrganization(request, response);
  }

  @Operation(summary = "登录")
  @PostMapping("/login")
  @WebApi
  @HandheldApi
  @WorkstationApi
  @InventoryCarApi
  @AreaControlApi
  @ArchiveCabinetApi
  @HRCabinetApi
  @SmartRackApi
  @LightRackApi
  @BoxRackApi
  public SimpleResult<UserLoginVO> login(
      @RequestBody UserLoginRequest request, HttpServletResponse response) {
    return userService.login(request, response);
  }

  @Operation(summary = "登出")
  @PostMapping("/logout")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  @HRCabinetApi(requireAuth = true)
  @SmartRackApi(requireAuth = true)
  @LightRackApi(requireAuth = true)
  @BoxRackApi(requireAuth = true)
  public BaseResult logout() {
    return userService.logout();
  }
}
