package org.zxwl.smart.controller.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.HandheldApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.user.OrganizationCreateRequest;
import org.zxwl.smart.domain.request.user.OrganizationDeleteRequest;
import org.zxwl.smart.domain.request.user.OrganizationListRequest;
import org.zxwl.smart.domain.request.user.OrganizationUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.OrganizationVO;
import org.zxwl.smart.service.user.OrganizationService;

@Tag(name = "✅机构")
@ZHPTRestController("/organization")
@AllArgsConstructor
public class OrganizationController {

  private final OrganizationService organizationService;

  @Operation(summary = "获取机构列表")
  @PostMapping("/getOrganizationList")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  public ListResult<OrganizationVO> getOrganizationList(
      @RequestBody OrganizationListRequest request) {
    return organizationService.getOrganizationList(request);
  }

  @Operation(summary = "创建机构")
  @PostMapping("/createOrganization")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createOrganization(
      @RequestBody OrganizationCreateRequest request) {
    return organizationService.createOrganization(request);
  }

  @Operation(summary = "更新机构")
  @PostMapping("/updateOrganization")
  @WebApi(requireAuth = true)
  public BaseResult updateOrganization(@RequestBody OrganizationUpdateRequest request) {
    return organizationService.updateOrganization(request);
  }

  @Operation(summary = "删除机构")
  @PostMapping("/deleteOrganization")
  @WebApi(requireAuth = true)
  public BaseResult deleteOrganization(@RequestBody OrganizationDeleteRequest request) {
    return organizationService.deleteOrganization(request);
  }
}
