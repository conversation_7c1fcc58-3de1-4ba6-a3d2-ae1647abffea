package org.zxwl.smart.controller.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.user.EmployeeCreateRequest;
import org.zxwl.smart.domain.request.user.EmployeeDeleteRequest;
import org.zxwl.smart.domain.request.user.EmployeeListRequest;
import org.zxwl.smart.domain.request.user.EmployeeUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.user.EmployeeVO;
import org.zxwl.smart.service.user.EmployeeService;

@Tag(name = "✅员工")
@ZHPTRestController("/user")
@AllArgsConstructor
public class EmployeeController {

  private final EmployeeService employeeService;

  @Operation(summary = "获取员工列表")
  @PostMapping("/getEmployeeList")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @ArchiveCabinetApi
  @HRCabinetApi
  @SmartRackApi
  @LightRackApi
  @BoxRackApi
  public PageResult<EmployeeVO> getEmployeeList(@RequestBody EmployeeListRequest request) {
    return employeeService.getEmployeeList(request);
  }

  @Operation(summary = "创建员工")
  @PostMapping("/createEmployee")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createEmployee(@RequestBody EmployeeCreateRequest request) {
    return employeeService.createEmployee(request);
  }

  @Operation(summary = "更新员工")
  @PostMapping("/updateEmployee")
  @WebApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  @HRCabinetApi(requireAuth = true)
  public BaseResult updateEmployee(@RequestBody EmployeeUpdateRequest request) {
    return employeeService.updateEmployee(request);
  }

  @Operation(summary = "删除员工")
  @PostMapping("/deleteEmployee")
  @WebApi(requireAuth = true)
  public BaseResult deleteEmployee(@RequestBody EmployeeDeleteRequest request) {
    return employeeService.deleteEmployee(request);
  }
}
