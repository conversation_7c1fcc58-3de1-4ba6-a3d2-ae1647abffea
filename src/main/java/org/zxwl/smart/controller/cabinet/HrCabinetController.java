package org.zxwl.smart.controller.cabinet;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.HRCabinetApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.cabinet.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupListVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupSelectVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetGroupVO;
import org.zxwl.smart.service.cabinet.HrCabinetService;

@Tag(name = "✅人事柜")
@ZHPTRestController("/cabinet")
@AllArgsConstructor
public class HrCabinetController {

  private final HrCabinetService hrCabinetService;

  @Operation(summary = "获取人事柜组列表")
  @PostMapping("/getHrCabinetGroupList")
  @WebApi(requireAuth = true)
  public PageResult<HrCabinetGroupListVO> getHrCabinetGroupList(
      @RequestBody HrCabinetGroupListRequest request) {
    return hrCabinetService.getHrCabinetGroupList(request);
  }

  @Operation(summary = "获取人事柜组列表（下拉框）")
  @PostMapping("/getHrCabinetGroupListForSelect")
  @WebApi(requireAuth = true)
  public ListResult<HrCabinetGroupSelectVO> getHrCabinetGroupListForSelect(
      @RequestBody HrCabinetGroupSelectRequest request) {
    return hrCabinetService.getHrCabinetGroupListForSelect(request);
  }

  @Operation(summary = "获取人事柜组详情")
  @PostMapping("/getHrCabinetGroup")
  @WebApi(requireAuth = true)
  @HRCabinetApi
  public SimpleResult<HrCabinetGroupVO> getHrCabinetGroup(
      @RequestBody HrCabinetGroupGetRequest request) {
    return hrCabinetService.getHrCabinetGroup(request);
  }

  @Operation(summary = "新增人事柜组")
  @PostMapping("/createHrCabinetGroup")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createHrCabinetGroup(
      @RequestBody HrCabinetGroupCreateRequest request) {
    return hrCabinetService.createHrCabinetGroup(request);
  }

  @Operation(summary = "更新人事柜组")
  @PostMapping("/updateHrCabinetGroup")
  @WebApi(requireAuth = true)
  public BaseResult updateHrCabinetGroup(@RequestBody HrCabinetGroupUpdateRequest request) {
    return hrCabinetService.updateHrCabinetGroup(request);
  }

  @Operation(summary = "删除人事柜组")
  @PostMapping("/deleteHrCabinetGroup")
  @WebApi(requireAuth = true)
  public BaseResult deleteHrCabinetGroup(@RequestBody HrCabinetGroupDeleteRequest request) {
    return hrCabinetService.deleteHrCabinetGroup(request);
  }

  @Operation(summary = "控制人事柜组")
  @PostMapping("/controlHrCabinetGroup")
  @WebApi(requireAuth = true)
  public BaseResult controlHrCabinetGroup(@RequestBody HrCabinetGroupControlRequest request) {
    return hrCabinetService.controlHrCabinetGroup(request);
  }
}
