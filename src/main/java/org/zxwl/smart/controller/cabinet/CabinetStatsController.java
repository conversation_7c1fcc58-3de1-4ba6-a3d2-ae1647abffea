package org.zxwl.smart.controller.cabinet;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.ArchiveCabinetApi;
import org.zxwl.smart.common.annotation.HRCabinetApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.cabinet.CabinetOperatorStatRequest;
import org.zxwl.smart.domain.request.cabinet.CabinetStatRequest;
import org.zxwl.smart.domain.request.cabinet.HrCabinetOperatorStatRequest;
import org.zxwl.smart.domain.request.cabinet.HrCabinetStatRequest;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.cabinet.CabinetOperatorStatVO;
import org.zxwl.smart.domain.response.cabinet.CabinetStatVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetOperatorStatVO;
import org.zxwl.smart.domain.response.cabinet.HrCabinetStatVO;
import org.zxwl.smart.service.cabinet.CabinetStatsService;

@Tag(name = "✅柜体统计")
@ZHPTRestController("/cabinet")
@AllArgsConstructor
public class CabinetStatsController {

  private final CabinetStatsService cabinetStatsService;

  @Operation(summary = "获取档案柜体统计数据")
  @PostMapping("/getCabinetStat")
  @WebApi(requireAuth = true)
  @ArchiveCabinetApi
  public SimpleResult<CabinetStatVO> getCabinetStat(@RequestBody CabinetStatRequest request) {
    return cabinetStatsService.getCabinetStat(request);
  }

  @Operation(summary = "获取档案柜体操作统计列表")
  @PostMapping("/getCabinetOperatorStatList")
  @ArchiveCabinetApi
  public ListResult<CabinetOperatorStatVO> getCabinetOperatorStatList(
      @RequestBody CabinetOperatorStatRequest request) {
    return cabinetStatsService.getCabinetOperatorStatList(request);
  }

  @Operation(summary = "获取人事柜体统计数据")
  @PostMapping("/getHrCabinetStat")
  @WebApi(requireAuth = true)
  @HRCabinetApi
  public SimpleResult<HrCabinetStatVO> getHrCabinetStat(@RequestBody HrCabinetStatRequest request) {
    return cabinetStatsService.getHrCabinetStat(request);
  }

  @Operation(summary = "获取人事柜体操作统计列表")
  @PostMapping("/getHrCabinetOperatorStatList")
  @HRCabinetApi
  public ListResult<HrCabinetOperatorStatVO> getHrCabinetOperatorStatList(
      @RequestBody HrCabinetOperatorStatRequest request) {
    return cabinetStatsService.getHrCabinetOperatorStatList(request);
  }
}
