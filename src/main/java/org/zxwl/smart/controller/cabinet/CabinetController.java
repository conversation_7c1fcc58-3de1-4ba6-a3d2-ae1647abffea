package org.zxwl.smart.controller.cabinet;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.ArchiveCabinetApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.cabinet.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.cabinet.*;
import org.zxwl.smart.service.cabinet.CabinetService;

@Tag(name = "✅档案柜")
@ZHPTRestController("/cabinet")
@AllArgsConstructor
public class CabinetController {

  private final CabinetService cabinetService;

  @Operation(summary = "获取档案柜组列表")
  @PostMapping("/getCabinetGroupList")
  @WebApi(requireAuth = true)
  public PageResult<CabinetGroupListVO> getCabinetGroupList(
      @RequestBody CabinetGroupListRequest request) {
    return cabinetService.getCabinetGroupList(request);
  }

  @Operation(summary = "获取档案柜组列表（下拉框）")
  @PostMapping("/getCabinetGroupListForSelect")
  @WebApi(requireAuth = true)
  public ListResult<CabinetGroupSelectVO> getCabinetGroupListForSelect(
      @RequestBody CabinetGroupSelectRequest request) {
    return cabinetService.getCabinetGroupListForSelect(request);
  }

  @Operation(summary = "获取柜组详情")
  @PostMapping("/getCabinetGroup")
  @WebApi(requireAuth = true)
  @ArchiveCabinetApi
  public SimpleResult<CabinetGroupVO> getCabinetGroup(@RequestBody CabinetGroupGetRequest request) {
    return cabinetService.getCabinetGroup(request);
  }

  @Operation(summary = "新增档案柜组")
  @PostMapping("/createCabinetGroup")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createCabinetGroup(
      @RequestBody CabinetGroupCreateRequest request) {
    return cabinetService.createCabinetGroup(request);
  }

  @Operation(summary = "更新档案柜组")
  @PostMapping("/updateCabinetGroup")
  @WebApi(requireAuth = true)
  public BaseResult updateCabinetGroup(@RequestBody CabinetGroupUpdateRequest request) {
    return cabinetService.updateCabinetGroup(request);
  }

  @Operation(summary = "删除档案柜组")
  @PostMapping("/deleteCabinetGroup")
  @WebApi(requireAuth = true)
  public BaseResult deleteCabinetGroup(@RequestBody CabinetGroupDeleteRequest request) {
    return cabinetService.deleteCabinetGroup(request);
  }

  @Operation(summary = "控制柜体")
  @PostMapping("/controlCabinetGroup")
  @WebApi(requireAuth = true)
  public BaseResult controlCabinetGroup(@RequestBody CabinetGroupControlRequest request) {
    return cabinetService.controlCabinetGroup(request);
  }
}
