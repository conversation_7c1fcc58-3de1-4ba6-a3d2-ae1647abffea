package org.zxwl.smart.controller.warehouse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.AreaControlApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.warehouse.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.warehouse.AreaListVO;
import org.zxwl.smart.domain.response.warehouse.AreaVO;
import org.zxwl.smart.service.warehouse.AreaService;

@Tag(name = "✅区域")
@ZHPTRestController("/area")
@AllArgsConstructor
public class AreaController {

  private final AreaService areaService;

  @Operation(summary = "获取区域列表")
  @PostMapping("/getAreaList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public ListResult<AreaListVO> getAreaList(@RequestBody AreaListRequest request) {
    return areaService.getAreaList(request);
  }

  @Operation(summary = "获取区域详情")
  @PostMapping("/getArea")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public SimpleResult<AreaVO> getArea(@RequestBody AreaRequest request) {
    return areaService.getArea(request);
  }

  @Operation(summary = "创建区域")
  @PostMapping("/createArea")
  @WebApi(requireAuth = true)
  public BaseResult createArea(@RequestBody AreaCreateRequest request) {
    return areaService.createArea(request);
  }

  @Operation(summary = "更新区域")
  @PostMapping("/updateArea")
  @WebApi(requireAuth = true)
  public BaseResult updateArea(@RequestBody AreaUpdateRequest request) {
    return areaService.updateArea(request);
  }

  @Operation(summary = "删除区域")
  @PostMapping("/deleteArea")
  @WebApi(requireAuth = true)
  public BaseResult deleteArea(@RequestBody AreaDeleteRequest request) {
    return areaService.deleteArea(request);
  }
}
