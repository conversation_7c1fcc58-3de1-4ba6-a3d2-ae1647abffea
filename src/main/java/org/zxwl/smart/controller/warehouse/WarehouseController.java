package org.zxwl.smart.controller.warehouse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.warehouse.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.warehouse.WarehouseBuildingVO;
import org.zxwl.smart.domain.response.warehouse.WarehouseFloorVO;
import org.zxwl.smart.domain.response.warehouse.WarehouseVO;
import org.zxwl.smart.service.warehouse.WarehouseService;

@Tag(name = "✅库房")
@ZHPTRestController("/warehouse")
@AllArgsConstructor
public class WarehouseController {

  private final WarehouseService warehouseService;

  @Operation(summary = "获取库房建筑列表")
  @PostMapping("/getWarehouseBuildingList")
  @WebApi(requireAuth = true)
  @WorkstationApi
  @HandheldApi
  public ListResult<WarehouseBuildingVO> getWarehouseBuildingList(
      @RequestBody BuildingListRequest request) {
    return warehouseService.getWarehouseBuildingList(request);
  }

  @Operation(summary = "新增库房建筑")
  @PostMapping("/createWarehouseBuilding")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createWarehouseBuilding(
      @RequestBody BuildingCreateRequest request) {
    return warehouseService.createWarehouseBuilding(request);
  }

  @Operation(summary = "更新库房建筑")
  @PostMapping("/updateWarehouseBuilding")
  @WebApi(requireAuth = true)
  public BaseResult updateWarehouseBuilding(@RequestBody BuildingUpdateRequest request) {
    return warehouseService.updateWarehouseBuilding(request);
  }

  @Operation(summary = "删除库房建筑")
  @PostMapping("/deleteWarehouseBuilding")
  @WebApi(requireAuth = true)
  public BaseResult deleteWarehouseBuilding(@RequestBody BuildingDeleteRequest request) {
    return warehouseService.deleteWarehouseBuilding(request);
  }

  @Operation(summary = "获取库房层列表")
  @PostMapping("/getWarehouseFloorList")
  @WebApi(requireAuth = true)
  @WorkstationApi
  @HandheldApi
  public ListResult<WarehouseFloorVO> getWarehouseFloorList(@RequestBody FloorListRequest request) {
    return warehouseService.getWarehouseFloorList(request);
  }

  @Operation(summary = "新增库房层")
  @PostMapping("/createWarehouseFloor")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createWarehouseFloor(@RequestBody FloorCreateRequest request) {
    return warehouseService.createWarehouseFloor(request);
  }

  @Operation(summary = "更新库房层")
  @PostMapping("/updateWarehouseFloor")
  @WebApi(requireAuth = true)
  public BaseResult updateWarehouseFloor(@RequestBody FloorUpdateRequest request) {
    return warehouseService.updateWarehouseFloor(request);
  }

  @Operation(summary = "删除库房层")
  @PostMapping("/deleteWarehouseFloor")
  @WebApi(requireAuth = true)
  public BaseResult deleteWarehouseFloor(@RequestBody FloorDeleteRequest request) {
    return warehouseService.deleteWarehouseFloor(request);
  }

  @Operation(summary = "获取库房列表")
  @PostMapping("/getWarehouseList")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @AreaControlApi
  public ListResult<WarehouseVO> getWarehouseList(@RequestBody WarehouseListRequest request) {
    return warehouseService.getWarehouseList(request);
  }

  @Operation(summary = "获取库房详情")
  @PostMapping("/getWarehouse")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  public SimpleResult<WarehouseVO> getWarehouse(@RequestBody WarehouseGetRequest request) {
    return warehouseService.getWarehouse(request);
  }

  @Operation(summary = "新增库房")
  @PostMapping("/createWarehouse")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createWarehouse(@RequestBody WarehouseCreateRequest request) {
    return warehouseService.createWarehouse(request);
  }

  @Operation(summary = "更新库房")
  @PostMapping("/updateWarehouse")
  @WebApi(requireAuth = true)
  public BaseResult updateWarehouse(@RequestBody WarehouseUpdateRequest request) {
    return warehouseService.updateWarehouse(request);
  }

  @Operation(summary = "删除库房")
  @PostMapping("/deleteWarehouse")
  @WebApi(requireAuth = true)
  public BaseResult deleteWarehouse(@RequestBody WarehouseDeleteRequest request) {
    return warehouseService.deleteWarehouse(request);
  }
}
