package org.zxwl.smart.controller.active;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.active.ActiveByActiveCodeRequest;
import org.zxwl.smart.domain.request.active.GenerateActiveCodeRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.active.ActiveCodeVO;
import org.zxwl.smart.domain.response.active.CurrentDeviceCodeVO;
import org.zxwl.smart.service.active.ActiveService;

@Tag(name = "♿激活码")
@ZHPTRestController("/active")
@AllArgsConstructor
public class ActiveController {

  private final ActiveService activeService;

  @Operation(summary = "获取当前服务器设备码")
  @PostMapping("/getCurrentDeviceCode")
  @WebApi()
  public SimpleResult<CurrentDeviceCodeVO> getCurrentDeviceCode() {
    return activeService.getCurrentDeviceCode();
  }

  @Operation(summary = "生成激活码")
  @PostMapping("/generateActiveCode")
  @WebApi()
  public SimpleResult<ActiveCodeVO> generateActiveCode(
      @RequestBody GenerateActiveCodeRequest request) {
    return activeService.generateActiveCode(request);
  }

  @Operation(summary = "使用激活码进行激活")
  @PostMapping("/activeByActiveCode")
  @WebApi()
  public BaseResult activeByActiveCode(@RequestBody ActiveByActiveCodeRequest request) {
    return activeService.activeByActiveCode(request);
  }
}
