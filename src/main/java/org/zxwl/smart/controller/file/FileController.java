package org.zxwl.smart.controller.file;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.file.FileDeleteRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.file.FileUploadVO;
import org.zxwl.smart.service.file.FileService;

@Tag(name = "✅文件操作")
@ZHPTRestController("/file")
@AllArgsConstructor
public class FileController {

  private final FileService fileService;

  @Operation(summary = "上传文件")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(
      content = @Content(mediaType = "multipart/form-data"))
  @Parameters({
    @Parameter(name = "file", description = "要上传的文件", required = true),
    @Parameter(
        name = "fileType",
        description = "文件类型：map地图配置文件，app安装包，archive电子档案，user用户",
        required = true)
  })
  @PostMapping(value = "/uploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @WebApi(requireAuth = true)
  public SimpleResult<FileUploadVO> uploadFile(
      @RequestPart("file") MultipartFile multipartFile, @RequestParam("fileType") String fileType) {
    return fileService.uploadFile(multipartFile, fileType);
  }

  @Operation(summary = "删除文件")
  @PostMapping("/deleteFile")
  @WebApi(requireAuth = true)
  public BaseResult deleteFile(@RequestBody FileDeleteRequest request) {
    return fileService.deleteFile(request);
  }
}
