package org.zxwl.smart.controller.inventory;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.inventory.*;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.inventory.InventoryRecordListVO;
import org.zxwl.smart.domain.response.inventory.InventoryRecordVO;
import org.zxwl.smart.service.inventory.InventoryService;

@Tag(name = "✅盘点")
@ZHPTRestController("/inventory")
@AllArgsConstructor
public class InventoryController {

  private final InventoryService inventoryService;

  @Operation(summary = "获取盘点记录列表", description = "设备每次盘点上报上来的数据，会生成一条盘点记录")
  @PostMapping("/getInventoryRecordList")
  @WebApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  public PageResult<InventoryRecordListVO> getInventoryRecordList(
      @RequestBody InventoryRecordListRequest request) {
    return inventoryService.getInventoryRecordList(request);
  }

  @Operation(summary = "获取盘点记录详情")
  @PostMapping("/getInventoryRecord")
  @WebApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  public SimpleResult<InventoryRecordVO> getInventoryRecord(
      @RequestBody InventoryRecordRequest request) {
    return inventoryService.getInventoryRecord(request);
  }

  @Operation(summary = "上传/创建盘点记录")
  @PostMapping("/createInventoryRecord")
  @HandheldApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @HRCabinetApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  @SmartRackApi(requireAuth = true)
  @LightRackApi(requireAuth = true)
  @BoxRackApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createInventoryRecord(
      @RequestBody InventoryRecordCreateRequest request) {
    return inventoryService.createInventoryRecord(request);
  }
}
