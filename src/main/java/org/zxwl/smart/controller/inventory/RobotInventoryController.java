package org.zxwl.smart.controller.inventory;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.inventory.RobotRecordListRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskCreateRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskDeleteRequest;
import org.zxwl.smart.domain.request.inventory.RobotTaskListRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.inventory.RobotRecordVO;
import org.zxwl.smart.domain.response.inventory.RobotTaskVO;
import org.zxwl.smart.service.inventory.RobotInventoryRecordService;
import org.zxwl.smart.service.inventory.RobotInventoryTaskService;

@Tag(name = "机器人盘点")
@ZHPTRestController("/robotInventory")
@AllArgsConstructor
public class RobotInventoryController {

  private final RobotInventoryTaskService taskService;
  private final RobotInventoryRecordService recordService;

  @Operation(summary = "获取机器人盘点任务列表")
  @PostMapping("/getRobotTaskList")
  public PageResult<RobotTaskVO> getRobotTaskList(@RequestBody RobotTaskListRequest request) {
    return taskService.getRobotTaskList(request);
  }

  @Operation(summary = "创建机器人盘点任务")
  @PostMapping("/createRobotTask")
  public SimpleResult<BaseCreateVO> createRobotTask(@RequestBody RobotTaskCreateRequest request) {
    return taskService.createRobotTask(request);
  }

  @Operation(summary = "删除机器人盘点任务")
  @PostMapping("/deleteRobotTask")
  public BaseResult deleteRobotTask(@RequestBody RobotTaskDeleteRequest request) {
    return taskService.deleteRobotTask(request);
  }

  @Operation(summary = "获取机器人盘点记录列表")
  @PostMapping("/getRobotRecordList")
  public PageResult<RobotRecordVO> getRobotRecordList(@RequestBody RobotRecordListRequest request) {
    return recordService.getRobotRecordList(request);
  }
}
