package org.zxwl.smart.controller.rack;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.rack.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.rack.*;
import org.zxwl.smart.service.rack.*;

@Tag(name = "✅密集架")
@ZHPTRestController("/rack")
@AllArgsConstructor
public class RackController {

  private final RackGroupService rackGroupService;
  private final RackColumnService rackColumnService;
  private final RackPanelService rackPanelService;
  private final RackLayerService rackLayerService;

  @Operation(summary = "获取密集架厂商列表")
  @PostMapping("/getRackVendorList")
  @WebApi
  public ListResult<RackVendorVO> getRackVendorList() {
    return rackGroupService.getRackVendorList();
  }

  @Operation(summary = "获取密集架类型列表")
  @PostMapping("/getRackTypeList")
  @WebApi
  public ListResult<RackTypeVO> getRackTypeList() {
    return rackGroupService.getRackTypeList();
  }

  @Operation(summary = "获取密集架组列表")
  @PostMapping("/getRackGroupList")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  public PageResult<RackGroupListVO> getRackGroupList(@RequestBody RackGroupListRequest request) {
    return rackGroupService.getRackGroupList(request);
  }

  @Operation(summary = "获取密集架组列表（下拉框）")
  @PostMapping("/getRackGroupListForSelect")
  @WebApi(requireAuth = true)
  public ListResult<RackGroupSelectVO> getRackGroupListForSelect(
      @RequestBody RackGroupListForSelectRequest request) {
    return rackGroupService.getRackGroupListForSelect(request);
  }

  @Operation(summary = "获取密集架组信息")
  @PostMapping("/getRackGroup")
  @WebApi(requireAuth = true)
  @SmartRackApi
  @LightRackApi
  public SimpleResult<RackGroupVO> getRackGroup(@RequestBody RackGroupGetRequest request) {
    return rackGroupService.getRackGroup(request);
  }

  @Operation(summary = "获取盒定位密集架组信息")
  @PostMapping("/getBoxRackGroup")
  @WebApi(requireAuth = true)
  @BoxRackApi
  public SimpleResult<BoxRackGroupVO> getBoxRackGroup(@RequestBody BoxRackGroupGetRequest request) {
    return rackGroupService.getBoxRackGroup(request);
  }

  @Operation(summary = "创建密集架组")
  @PostMapping("/createRackGroup")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createRackGroup(@RequestBody RackGroupCreateRequest request) {
    return rackGroupService.createRackGroup(request);
  }

  @Operation(summary = "更新密集架组")
  @PostMapping("/updateRackGroup")
  @WebApi(requireAuth = true)
  public BaseResult updateRackGroup(@RequestBody RackGroupUpdateRequest request) {
    return rackGroupService.updateRackGroup(request);
  }

  @Operation(summary = "删除密集架组")
  @PostMapping("/deleteRackGroup")
  @WebApi(requireAuth = true)
  public BaseResult deleteRackGroup(@RequestBody RackGroupDeleteRequest request) {
    return rackGroupService.deleteRackGroup(request);
  }

  @Operation(summary = "控制密集架组")
  @PostMapping("/controlRackGroup")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  public BaseResult controlRackGroup(@RequestBody RackGroupControlRequest request) {
    return rackGroupService.controlRackGroup(request);
  }

  @Operation(summary = "新增密集架列")
  @PostMapping("/createRackColumn")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createRackColumn(@RequestBody RackColumnCreateRequest request) {
    return rackColumnService.createRackColumn(request);
  }

  @Operation(summary = "删除密集架列")
  @PostMapping("/deleteRackColumn")
  @WebApi(requireAuth = true)
  public BaseResult deleteRackColumn(@RequestBody RackColumnDeleteRequest request) {
    return rackColumnService.deleteRackColumn(request);
  }

  @Operation(summary = "获取密集架面列表")
  @PostMapping("/getRackPanelList")
  @WebApi(requireAuth = true)
  public ListResult<RackPanelVO> getRackPanelList(@RequestBody RackPanelListRequest request) {
    return rackPanelService.getRackPanelList(request);
  }

  @Operation(summary = "获取密集架层列表")
  @PostMapping("/getRackLayerList")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @SmartRackApi
  @LightRackApi
  @BoxRackApi
  public PageResult<RackLayerListVO> getRackLayerList(@RequestBody RackLayerListRequest request) {
    return rackLayerService.getRackLayerList(request);
  }

  @Operation(summary = "获取密集架层信息")
  @PostMapping("/getRackLayer")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  public SimpleResult<RackLayerVO> getRackLayer(@RequestBody RackLayerRequest request) {
    return rackLayerService.getRackLayer(request);
  }

  @Operation(summary = "绑定密集架层标签")
  @PostMapping("/bindRackLayerTID")
  @WebApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  public BaseResult bindRackLayerTID(@RequestBody RackLayerBindTIDRequest request) {
    return rackLayerService.bindRackLayerTID(request);
  }

  @Operation(summary = "绑定密集架层关联门类")
  @PostMapping("/bindRackLayerArchiveType")
  @WebApi(requireAuth = true)
  public BaseResult bindRackLayerArchiveType(@RequestBody RackLayerBindArchiveTypeRequest request) {
    return rackLayerService.bindRackLayerArchiveType(request);
  }
}
