package org.zxwl.smart.controller.rack;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.rack.RackStatRequest;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.rack.RackStatVO;
import org.zxwl.smart.service.rack.RackStatsService;

@Tag(name = "✅密集架统计")
@ZHPTRestController("/rack")
@AllArgsConstructor
public class RackStatsController {

  private final RackStatsService rackStatsService;

  @Operation(summary = "获取架体统计数据")
  @PostMapping("/getRackStat")
  @WebApi(requireAuth = true)
  @SmartRackApi
  @LightRackApi
  @BoxRackApi
  public SimpleResult<RackStatVO> getRackStat(@RequestBody RackStatRequest request) {
    return rackStatsService.getRackStat(request);
  }
}
