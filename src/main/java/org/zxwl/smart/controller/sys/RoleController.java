package org.zxwl.smart.controller.sys;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.role.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.role.MenuPermissionVO;
import org.zxwl.smart.domain.response.role.RoleVO;
import org.zxwl.smart.service.role.RoleService;

@Tag(name = "角色管理")
@ZHPTRestController("/role")
@AllArgsConstructor
public class RoleController {

  private final RoleService roleService;

  @Operation(summary = "获取角色列表")
  @PostMapping("/roleList")
  @WebApi(requireAuth = true)
  public ListResult<RoleVO> getRoleList(@RequestBody RoleListRequest request) {
    return roleService.getRoleList(request);
  }

  @Operation(summary = "获取角色菜单权限列表")
  @PostMapping("/getRolePermissionList")
  @WebApi(requireAuth = true)
  public ListResult<MenuPermissionVO> getRolePermissionList(
      @RequestBody RoleMenuPermissionListRequest request) {
    return roleService.getRolePermissionList(request);
  }

  @Operation(summary = "创建角色")
  @PostMapping("/createRole")
  @WebApi(requireAuth = true)
  public BaseResult createRole(@RequestBody RoleCreateRequest request) {
    return roleService.createRole(request);
  }

  @Operation(summary = "更新角色")
  @PostMapping("/updateRole")
  @WebApi(requireAuth = true)
  public BaseResult updateRole(@RequestBody RoleUpdateRequest request) {
    return roleService.updateRole(request);
  }

  @Operation(summary = "删除角色")
  @PostMapping("/deleteRole")
  @WebApi(requireAuth = true)
  public BaseResult deleteRole(@RequestBody RoleDeleteRequest request) {
    return roleService.deleteRole(request);
  }
}
