package org.zxwl.smart.controller.sys;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.sys.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.sys.MenuVO;
import org.zxwl.smart.service.sys.SystemService;

@Tag(name = "系统相关")
@ZHPTRestController("/sys")
@AllArgsConstructor
public class SystemController {

  private final SystemService systemService;

  @Operation(summary = "获取菜单列表")
  @PostMapping("/getMenuList")
  @WebApi(requireAuth = true)
  public ListResult<MenuVO> getMenuList() {
    return systemService.getMenuList();
  }

  @Operation(summary = "获取用户菜单列表")
  @PostMapping("/getUserMenuList")
  @WebApi(requireAuth = true)
  public ListResult<MenuVO> getUserMenuList(@RequestBody UserMenuListRequest request) {
    return systemService.getUserMenuList(request);
  }

  @Operation(summary = "创建菜单")
  @PostMapping("/createMenu")
  @WebApi(requireAuth = true)
  public BaseResult createMenu(@RequestBody MenuCreateRequest request) {
    return systemService.createMenu(request);
  }

  @Operation(summary = "更新菜单")
  @PostMapping("/updateMenu")
  @WebApi(requireAuth = true)
  public BaseResult updateMenu(@RequestBody MenuUpdateRequest request) {
    return systemService.updateMenu(request);
  }

  @Operation(summary = "删除菜单")
  @PostMapping("/deleteMenu")
  @WebApi(requireAuth = true)
  public BaseResult deleteMenu(@RequestBody MenuDeleteRequest request) {
    return systemService.deleteMenu(request);
  }
}
