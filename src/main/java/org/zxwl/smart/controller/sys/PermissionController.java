package org.zxwl.smart.controller.sys;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.permission.*;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.permission.PermissionVO;
import org.zxwl.smart.service.permission.PermissionService;

@Tag(name = "权限管理")
@ZHPTRestController("/permission")
@AllArgsConstructor
public class PermissionController {

  private final PermissionService permissionService;

  @Operation(summary = "获取权限列表")
  @PostMapping("/getPermissionList")
  @WebApi(requireAuth = true)
  public ListResult<PermissionVO> getPermissionList(@RequestBody PermissionListRequest request) {
    return permissionService.getPermissionList(request);
  }

  @Operation(summary = "获取用户权限列表")
  @PostMapping("/getUserPermission")
  @WebApi(requireAuth = true)
  public ListResult<PermissionVO> getUserPermission(@RequestBody UserPermissionRequest request) {
    return permissionService.getUserPermission(request);
  }
}
