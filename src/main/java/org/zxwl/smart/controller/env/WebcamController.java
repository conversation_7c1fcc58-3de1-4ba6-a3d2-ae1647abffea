package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.AreaControlApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.env.WebcamVO;
import org.zxwl.smart.service.env.WebcamService;

@Tag(name = "✅监控设备")
@ZHPTRestController("/webcam")
@AllArgsConstructor
public class WebcamController {

  private final WebcamService webcamService;

  @Operation(summary = "获取监控设备列表")
  @PostMapping("/getWebcamList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public PageResult<WebcamVO> getWebcamList(@RequestBody WebcamListRequest request) {
    return webcamService.getWebcamList(request);
  }

  @Operation(summary = "创建监控设备")
  @PostMapping("/createWebcam")
  @WebApi(requireAuth = true)
  public BaseResult createWebcam(@RequestBody WebcamCreateRequest request) {
    return webcamService.createWebcam(request);
  }

  @Operation(summary = "更新监控设备")
  @PostMapping("/updateWebcam")
  @WebApi(requireAuth = true)
  public BaseResult updateWebcam(@RequestBody WebcamUpdateRequest request) {
    return webcamService.updateWebcam(request);
  }

  @Operation(summary = "删除监控设备")
  @PostMapping("/deleteWebcam")
  @WebApi(requireAuth = true)
  public BaseResult deleteWebcam(@RequestBody WebcamDeleteRequest request) {
    return webcamService.deleteWebcam(request);
  }
}
