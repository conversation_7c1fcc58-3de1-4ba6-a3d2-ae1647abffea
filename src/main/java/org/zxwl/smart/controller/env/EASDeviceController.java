package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.EASApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.EASArchiveAlarmVO;
import org.zxwl.smart.domain.response.env.EASGetAlarmRecordVO;
import org.zxwl.smart.domain.response.env.EASPersonNumVO;
import org.zxwl.smart.service.env.EASDeviceService;

@Tag(name = "防盗门设备", description = "Electronic Article Surveillance gates")
@ZHPTRestController("/easDevice")
@AllArgsConstructor
public class EASDeviceController {

  private final EASDeviceService easDeviceService;

  @Operation(summary = "查询档案是否需要报警")
  @PostMapping("/getArchiveAlarm")
  @EASApi
  @WebApi
  public SimpleResult<EASArchiveAlarmVO> getArchiveAlarm(
      @RequestBody EASArchiveAlarmRequest request) {
    return easDeviceService.getArchiveAlarm(request);
  }

  @Operation(summary = "增加门禁进出人数")
  @PostMapping("/addPeopleNum")
  @EASApi
  public BaseResult addPeopleNum(@RequestBody EASAddPeopleNumRequest request) {
    return easDeviceService.addPeopleNum(request);
  }

  @Operation(summary = "查询门禁进出人数")
  @PostMapping("/getPersonNum")
  @EASApi
  public SimpleResult<EASPersonNumVO> getPeopleNum(@RequestBody EASGetPeopleNumRequest request) {
    return easDeviceService.getPeopleNum(request);
  }

  @Operation(summary = "查询门禁进出记录")
  @PostMapping("/getAlarmRecordList")
  @WebApi(requireAuth = true)
  public PageResult<EASGetAlarmRecordVO> getAlarmRecordList(
      @RequestBody EASGetAlarmRecordRequest request) {
    return easDeviceService.getAlarmRecordList(request);
  }

  @Operation(summary = "删除门禁进出记录")
  @PostMapping("/deleteAlarmRecord")
  public BaseResult deleteAlarmRecord(@RequestBody EASDeleteAlarmRecordRequest request) {
    return easDeviceService.deleteAlarmRecord(request);
  }
}
