package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.AreaControlApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.EnvDeviceTypeVO;
import org.zxwl.smart.domain.response.env.EnvDeviceVO;
import org.zxwl.smart.service.env.EnvDeviceService;

@Tag(name = "✅环控设备")
@ZHPTRestController("/envDevice")
@AllArgsConstructor
public class EnvDeviceController {

  private final EnvDeviceService envDeviceService;

  @Operation(summary = "获取设备类型列表")
  @PostMapping("/getDeviceTypeList")
  @WebApi(requireAuth = true)
  public ListResult<EnvDeviceTypeVO> getDeviceTypeList() {
    return envDeviceService.getDeviceTypeList();
  }

  @Operation(summary = "获取设备列表")
  @PostMapping("/getEnvDeviceList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public ListResult<EnvDeviceVO> getEnvDeviceList(@RequestBody EnvDeviceListRequest request) {
    return envDeviceService.getEnvDeviceList(request);
  }

  @Operation(summary = "获取设备详情")
  @PostMapping("/getEnvDevice")
  @WebApi(requireAuth = true)
  public SimpleResult<EnvDeviceVO> getEnvDevice(@RequestBody EnvDeviceRequest request) {
    return envDeviceService.getEnvDevice(request);
  }

  @Operation(summary = "创建环控设备")
  @PostMapping("/createEnvDevice")
  @WebApi(requireAuth = true)
  public BaseResult createEnvDevice(@RequestBody EnvDeviceCreateRequest request) {
    return envDeviceService.createEnvDevice(request);
  }

  @Operation(summary = "更新环控设备")
  @PostMapping("/updateEnvDevice")
  @WebApi(requireAuth = true)
  public BaseResult updateEnvDevice(@RequestBody EnvDeviceUpdateRequest request) {
    return envDeviceService.updateEnvDevice(request);
  }

  @Operation(summary = "删除环控设备")
  @PostMapping("/deleteEnvDevice")
  @WebApi(requireAuth = true)
  public BaseResult deleteEnvDevice(@RequestBody EnvDeviceDeleteRequest request) {
    return envDeviceService.deleteEnvDevice(request);
  }

  @Operation(summary = "控制环控设备")
  @PostMapping("/controlEnvDevice")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public BaseResult controlEnvDevice(@RequestBody EnvDeviceControlRequest request) {
    return envDeviceService.controlEnvDevice(request);
  }

  @Operation(summary = "批量控制环控设备")
  @PostMapping("/controlEnvDeviceList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public BaseResult controlEnvDeviceList(@RequestBody EnvDeviceBatchControlRequest request) {
    return envDeviceService.controlEnvDeviceList(request);
  }
}
