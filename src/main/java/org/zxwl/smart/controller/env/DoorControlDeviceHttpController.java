package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.domain.request.env.DoorControlDeviceHttpUploadDataParams;
import org.zxwl.smart.domain.request.env.DoorControlDeviceInitParams;
import org.zxwl.smart.service.env.DoorControlDeviceHttpService;

@Tag(name = "门禁设备HTTP接口")
@RestController
@RequestMapping("/iclock")
@AllArgsConstructor
public class DoorControlDeviceHttpController {

  private final DoorControlDeviceHttpService doorControlDeviceHttpService;

  @Operation(summary = "初始化设备配置信息")
  @GetMapping("/cdata")
  public String initOptions(@ModelAttribute DoorControlDeviceInitParams params) {
    return doorControlDeviceHttpService.initOptions(params);
  }

  @Operation(summary = "门禁设备上传数据")
  @PostMapping(value = "/cdata")
  public String uploadData(
      DoorControlDeviceHttpUploadDataParams params, HttpServletRequest request) {
    return doorControlDeviceHttpService.uploadData(params, request);
  }

  @Operation(summary = "门禁设备轮询服务器获取命令")
  @GetMapping("/getrequest")
  public String getRequest(@RequestParam(value = "SN") String deviceNo) {
    return doorControlDeviceHttpService.getRequest(deviceNo);
  }

  @Operation(summary = "门禁设备保持心跳")
  @PostMapping("/ping")
  public String ping(@RequestParam(value = "SN") String deviceNo) {
    return doorControlDeviceHttpService.ping(deviceNo);
  }

  @Operation(summary = "门禁设备执行命令后把结果返回给服务器")
  @PostMapping("/devicecmd")
  public String deviceCommand(
      @RequestParam(value = "SN") String deviceNo, HttpServletRequest request) {
    return doorControlDeviceHttpService.deviceCommand(deviceNo, request);
  }
}
