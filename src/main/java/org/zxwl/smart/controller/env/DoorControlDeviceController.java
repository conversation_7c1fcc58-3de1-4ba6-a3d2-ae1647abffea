package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.*;
import org.zxwl.smart.service.env.DoorControlRecordService;
import org.zxwl.smart.service.env.DoorControlUserService;

@Tag(name = "门禁设备")
@ZHPTRestController("/doorDevice")
@AllArgsConstructor
public class DoorControlDeviceController {

  private final DoorControlRecordService doorControlRecordService;
  private final DoorControlUserService doorControlUserService;

  @Operation(summary = "获取门禁用户列表")
  @PostMapping("/getDoorControlUserList")
  @WebApi(requireAuth = true)
  public PageResult<DoorControlUserVO> getDoorControlUserList(
      @RequestBody DoorControlUserListRequest request) {
    return doorControlUserService.getDoorControlUserList(request);
  }

  @Operation(summary = "创建门禁用户")
  @PostMapping("/createDoorControlUser")
  @WebApi(requireAuth = true)
  public BaseResult createDoorControlUser(@RequestBody DoorControlUserCreateRequest request) {
    return doorControlUserService.createDoorControlUser(request);
  }

  @Operation(summary = "更新门禁用户")
  @PostMapping("/updateDoorControlUser")
  @WebApi(requireAuth = true)
  public BaseResult updateDoorControlUser(@RequestBody DoorControlUserUpdateRequest request) {
    return doorControlUserService.updateDoorControlUser(request);
  }

  @Operation(summary = "删除门禁用户")
  @PostMapping("/deleteDoorControlUser")
  @WebApi(requireAuth = true)
  public BaseResult deleteDoorControlUser(@RequestBody DoorControlUserDeleteRequest request) {
    return doorControlUserService.deleteDoorControlUser(request);
  }

  @Operation(summary = "批量导入门禁用户")
  @PostMapping(value = "/importDoorControlUser", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @WebApi(requireAuth = true)
  public SimpleResult<DoorControlUserImportResultVO> importDoorControlUser(
      @RequestPart("file") MultipartFile multipartFile, @RequestParam("deviceNo") String deviceNo) {
    return doorControlUserService.importDoorControlUser(multipartFile, deviceNo);
  }

  @Operation(summary = "从其他门禁设备导入用户")
  @PostMapping("/copyDoorControlUser")
  @WebApi(requireAuth = true)
  public SimpleResult<DoorControlUserCopyResultVO> copyDoorControlUser(
      @RequestBody DoorControlUserCopyRequest request) {
    return doorControlUserService.copyDoorControlUser(request);
  }

  @Operation(summary = "获取门禁记录列表")
  @PostMapping("/getDoorControlRecordList")
  @WebApi(requireAuth = true)
  public PageResult<DoorControlRecordVO> getDoorControlRecordList(
      @RequestBody DoorControlRecordListRequest request) {
    return doorControlRecordService.getDoorControlRecordList(request);
  }

  @Operation(summary = "删除门禁日志记录")
  @PostMapping("/deleteDoorControlRecord")
  @WebApi(requireAuth = true)
  public BaseResult deleteDoorControlRecord(@RequestBody DoorControlRecordDeleteRequest request) {
    return doorControlRecordService.deleteDoorControlRecord(request);
  }

  @Operation(summary = "导出门禁日志记录表")
  @PostMapping("/getDoorControlRecordExcel")
  @WebApi(requireAuth = true)
  public void getDoorControlRecordExcel(
      @RequestBody DoorControlRecordListRequest request, HttpServletResponse response) {
    doorControlRecordService.getDoorControlRecordExcel(request, response);
  }

  @Operation(summary = "获取所有门禁通行数量统计")
  @PostMapping("/getDoorControlRecordCount")
  @WebApi(requireAuth = true)
  public SimpleResult<DoorControlRecordCountVO> getDoorControlRecordCount(
      @RequestBody DoorControlRecordCountRequest request) {
    return doorControlRecordService.getDoorControlRecordCount(request);
  }

  @Operation(summary = "获取单个门禁通行数量统计")
  @PostMapping("/getSingleDoorControlRecordCount")
  @WebApi(requireAuth = true)
  public SimpleResult<DoorControlRecordCountVO> getSingleDoorControlRecordCount(
      @RequestBody DoorControlSingleRecordCountRequest request) {
    return doorControlRecordService.getSingleDoorControlRecordCount(request);
  }
}
