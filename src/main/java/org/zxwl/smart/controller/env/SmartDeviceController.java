package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.env.SmartDeviceCreateRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceDeleteRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceListRequest;
import org.zxwl.smart.domain.request.env.SmartDeviceUpdateRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.env.SmartDeviceVO;
import org.zxwl.smart.service.env.SmartDeviceService;

@Tag(name = "智能设备管理")
@ZHPTRestController("/smartDevice")
@AllArgsConstructor
public class SmartDeviceController {

  private final SmartDeviceService smartDeviceService;

  @Operation(summary = "获取智能设备列表")
  @PostMapping("/getSmartDeviceList")
  public PageResult<SmartDeviceVO> getSmartDeviceList(@RequestBody SmartDeviceListRequest request) {
    return smartDeviceService.getSmartDeviceList(request);
  }

  @Operation(summary = "创建智能设备")
  @PostMapping("/createSmartDevice")
  public BaseResult createSmartDevice(@RequestBody SmartDeviceCreateRequest request) {
    return smartDeviceService.createSmartDevice(request);
  }

  @Operation(summary = "更新智能设备")
  @PostMapping("/updateSmartDevice")
  public BaseResult updateSmartDevice(@RequestBody SmartDeviceUpdateRequest request) {
    return smartDeviceService.updateSmartDevice(request);
  }

  @Operation(summary = "删除智能设备")
  @PostMapping("/deleteSmartDevice")
  public BaseResult deleteSmartDevice(@RequestBody SmartDeviceDeleteRequest request) {
    return smartDeviceService.deleteSmartDevice(request);
  }
}
