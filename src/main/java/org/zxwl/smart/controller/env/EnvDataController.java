package org.zxwl.smart.controller.env;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.client.hk.ConnectionManager;
import org.zxwl.smart.common.annotation.AreaControlApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.env.*;
import org.zxwl.smart.domain.request.env.EnvDeviceLogDeleteRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.env.*;
import org.zxwl.smart.mybatis.mapper.device.EnvDeviceMapper;
import org.zxwl.smart.service.env.EnvDataService;

@Tag(name = "✅环控数据")
@ZHPTRestController("/envDevice")
@AllArgsConstructor
public class EnvDataController {

  private final EnvDataService envDataService;
  private final EnvDeviceMapper envDeviceMapper;
  private final ConnectionManager connectionManager;

  @Operation(summary = "获取环控数据")
  @PostMapping("/getEnvData")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public SimpleResult<EnvDataVO> getEnvData(@RequestBody EnvDataRequest request) {
    return envDataService.getEnvData(request);
  }

  @Operation(summary = "获取环控空气质量汇总记录列表")
  @PostMapping("/getEnvAirQualitySummaryRecordList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public ListResult<EnvAirQualitySummaryRecordVO> getEnvAirQualitySummaryRecordList(
      @RequestBody EnvAirQualitySummaryRecordRequest request) {
    return envDataService.getEnvAirQualitySummaryRecordList(request);
  }

  @Operation(summary = "获取环控空气质量记录列表")
  @PostMapping("/getEnvAirQualityRecordList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public PageResult<EnvAirQualityRecordVO> getEnvAirQualityRecordList(
      @RequestBody EnvAirQualityRecordRequest request) {
    return envDataService.getEnvAirQualityRecordList(request);
  }

  @Operation(summary = "导出环控空气质量记录表")
  @PostMapping("/getEnvAirQualityRecordExcel")
  @WebApi(requireAuth = true)
  public void getEnvAirQualityRecordExcel(
      @RequestBody EnvAirQualityRecordRequest request, HttpServletResponse response) {
    envDataService.getEnvAirQualityRecordExcel(request, response);
  }

  @Operation(summary = "获取环控能耗记录列表")
  @PostMapping("/getEnvEnergyRecordList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public PageResult<EnvEnergyRecordVO> getEnvEnergyRecordList(
      @RequestBody EnvEnvEnergyRecordRequest request) {
    return envDataService.getEnvEnergyRecordList(request);
  }

  @Operation(summary = "导出环控能耗记录表")
  @PostMapping("/getEnvEnergyRecordExcel")
  @WebApi(requireAuth = true)
  public void getEnvEnergyRecordExcel(
      @RequestBody EnvEnvEnergyRecordRequest request, HttpServletResponse response) {
    envDataService.getEnvEnergyRecordExcel(request, response);
  }

  @Operation(summary = "获取设备调控日志记录")
  @PostMapping("/getEnvDeviceLogList")
  @WebApi(requireAuth = true)
  @AreaControlApi(requireAuth = true)
  public PageResult<EnvDeviceLogVO> getEnvDeviceLogList(@RequestBody EnvDeviceLogRequest request) {
    return envDataService.getEnvDeviceLogList(request);
  }

  @Operation(summary = "导出设备调控日志记录表")
  @PostMapping("/getEnvDeviceLogExcel")
  @WebApi(requireAuth = true)
  public void getEnvDeviceLogExcel(
      @RequestBody EnvDeviceLogRequest request, HttpServletResponse response) {
    envDataService.getEnvDeviceLogExcel(request, response);
  }

  @Operation(summary = "删除设备调控日志记录")
  @PostMapping("/deleteEnvDeviceLog")
  @WebApi(requireAuth = true)
  public BaseResult deleteEnvDeviceLog(@RequestBody EnvDeviceLogDeleteRequest request) {
    return envDataService.deleteEnvDeviceLog(request);
  }
}
