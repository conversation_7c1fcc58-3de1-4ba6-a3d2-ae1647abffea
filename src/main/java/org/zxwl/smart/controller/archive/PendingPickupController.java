package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.pickup.PendingPickupCreateRequest;
import org.zxwl.smart.domain.request.pickup.PendingPickupDeleteRequest;
import org.zxwl.smart.domain.request.pickup.PendingPickupListRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.pickup.PendingPickupVO;
import org.zxwl.smart.service.archive.PendingPickupService;

@Tag(name = "✅档案待取")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class PendingPickupController {

  private final PendingPickupService pendingPickupService;

  @Operation(summary = "获取档案待取列表")
  @PostMapping("/getArchivePendingPickupList")
  @WebApi(requireAuth = true)
  public PageResult<PendingPickupVO> getArchivePendingPickupList(
      @RequestBody PendingPickupListRequest request) {
    return pendingPickupService.getArchivePendingPickupList(request);
  }

  @Operation(summary = "创建档案待取")
  @PostMapping("/createArchivePendingPickup")
  @WebApi(requireAuth = true)
  public BaseResult createArchivePendingPickup(@RequestBody PendingPickupCreateRequest request) {
    return pendingPickupService.createArchivePendingPickup(request);
  }

  @Operation(summary = "删除档案待取")
  @PostMapping("/deleteArchivePendingPickup")
  @WebApi(requireAuth = true)
  public BaseResult deleteArchivePendingPickup(@RequestBody PendingPickupDeleteRequest request) {
    return pendingPickupService.deleteArchivePendingPickup(request);
  }
}
