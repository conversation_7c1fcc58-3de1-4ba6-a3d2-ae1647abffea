package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.archive.*;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.*;
import org.zxwl.smart.service.archive.ArchiveStatsService;

@Tag(name = "✅档案统计")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveStatsController {

  private final ArchiveStatsService archiveStatsService;

  @Operation(summary = "获取档案在库统计")
  @PostMapping("/getArchiveStockStat")
  @WebApi(requireAuth = true)
  public SimpleResult<ArchiveStockStatVO> getArchiveStockStat(
      @RequestBody ArchiveStockStatRequest request) {
    return archiveStatsService.getArchiveStockStat(request);
  }

  @Operation(summary = "获取档案在架统计")
  @PostMapping("/getArchiveShelvingStat")
  @WebApi(requireAuth = true)
  public SimpleResult<ArchiveShelvingStatVO> getArchiveShelvingStat(
      @RequestBody ArchiveShelvingStatRequest request) {
    return archiveStatsService.getArchiveShelvingStat(request);
  }

  @Operation(summary = "获取档案门类统计")
  @PostMapping("/getArchiveTypeStat")
  @WebApi(requireAuth = true)
  public ListResult<ArchiveTypeStatVO> getArchiveTypeStat(
      @RequestBody ArchiveTypeStatRequest request) {
    return archiveStatsService.getArchiveTypeStat(request);
  }

  @Operation(summary = "获取档案存取数量统计")
  @PostMapping("/getArchiveAccessStat")
  @WebApi(requireAuth = true)
  public SimpleResult<ArchiveAccessStatVO> getArchiveOperatorStat(
      @RequestBody ArchiveAccessStatRequest request) {
    return archiveStatsService.getArchiveAccessStat(request);
  }

  @Operation(summary = "获取档案出入库统计")
  @PostMapping("/getArchiveStockStatList")
  @WebApi(requireAuth = true)
  public ListResult<ArchiveStockStatListVO> getArchiveStockStatList(
      @RequestBody ArchiveStockStatListRequest request) {
    return archiveStatsService.getArchiveStockStatList(request);
  }
}
