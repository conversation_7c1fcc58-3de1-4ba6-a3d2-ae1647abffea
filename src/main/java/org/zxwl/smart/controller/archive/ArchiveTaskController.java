package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskListCompleteManualRequest;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskListRequest;
import org.zxwl.smart.domain.request.pickup.ArchiveTaskProcessRequest;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.pickup.ArchiveTaskListVO;
import org.zxwl.smart.domain.response.pickup.ArchiveTaskProcessVO;
import org.zxwl.smart.service.archive.ArchiveTaskService;

@Tag(name = "✅档案存取任务")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveTaskController {

  private ArchiveTaskService archiveTaskService;

  @Operation(summary = "获取档案存取任务列表")
  @PostMapping("/getArchiveTaskList")
  @WebApi(requireAuth = true)
  PageResult<ArchiveTaskListVO> getArchiveTaskList(@RequestBody ArchiveTaskListRequest request) {
    return archiveTaskService.getArchiveTaskList(request);
  }

  @Operation(summary = "获取档案存取任务过程")
  @PostMapping("/getArchiveTaskProcess")
  @WebApi(requireAuth = true)
  ListResult<ArchiveTaskProcessVO> getArchiveTaskProcess(@RequestBody ArchiveTaskProcessRequest request) {
    return archiveTaskService.getArchiveTaskProcess(request);
  }

  @Operation(summary = "手动批量完成档案存取任务")
  @PostMapping("/completeArchiveTaskListManual")
  @WebApi(requireAuth = true)
  BaseResult completeArchiveTaskListManual(@RequestBody ArchiveTaskListCompleteManualRequest request) {
    return archiveTaskService.completeArchiveTaskListManual(request);
  }
}
