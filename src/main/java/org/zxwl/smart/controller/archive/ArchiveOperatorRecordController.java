package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.archive.OperatorRecordShelveExcelRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordShelveListRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordStockExcelRequest;
import org.zxwl.smart.domain.request.archive.OperatorRecordStockListRequest;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.archive.OperatorRecordListVO;
import org.zxwl.smart.service.archive.ArchiveOperatorRecordService;

@Tag(name = "档案操作记录")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveOperatorRecordController {

  private final ArchiveOperatorRecordService recordService;

  @Operation(summary = "获取档案出入库操作记录列表")
  @PostMapping("/getArchiveOperateStockRecordList")
  @WebApi(requireAuth = true)
  public PageResult<OperatorRecordListVO> getArchiveOperateStockRecordList(
      @RequestBody OperatorRecordStockListRequest request) {
    return recordService.getArchiveOperateStockRecordList(request);
  }

  @Operation(summary = "获取档案上下架操作记录列表")
  @PostMapping("/getArchiveOperateShelveRecordList")
  @WebApi(requireAuth = true)
  public PageResult<OperatorRecordListVO> getArchiveOperateShelveRecordList(
      @RequestBody OperatorRecordShelveListRequest request) {
    return recordService.getArchiveOperateShelveRecordList(request);
  }

  @Operation(summary = "导出档案出入库操作记录表")
  @PostMapping("/getArchiveOperateStockRecordExcel")
  @WebApi(requireAuth = true)
  public void getArchiveOperateStockRecordExcel(
      @RequestBody OperatorRecordStockExcelRequest request, HttpServletResponse response) {
    recordService.getArchiveOperateStockRecordExcel(request, response);
  }

  @Operation(summary = "导出档案上下架库操作记录表")
  @PostMapping("/getArchiveOperateShelveRecordExcel")
  @WebApi(requireAuth = true)
  public void getArchiveOperateShelveRecordExcel(
      @RequestBody OperatorRecordShelveExcelRequest request, HttpServletResponse response) {
    recordService.getArchiveOperateShelveRecordExcel(request, response);
  }
}
