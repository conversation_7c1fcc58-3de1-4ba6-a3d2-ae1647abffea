package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.WorkstationApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.archive.ArchiveTypeCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeListRequest;
import org.zxwl.smart.domain.request.archive.ArchiveTypeUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.ArchiveTypeListVO;
import org.zxwl.smart.service.archive.ArchiveTypeService;

@Tag(name = "✅档案门类")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveTypeController {

  private final ArchiveTypeService archiveTypeService;

  @Operation(summary = "获取档案门类列表")
  @PostMapping("/getArchiveTypeList")
  @WebApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  public ListResult<ArchiveTypeListVO> getArchiveTypeList(
      @RequestBody ArchiveTypeListRequest request) {
    return archiveTypeService.getArchiveTypeList(request);
  }

  @Operation(summary = "新建档案门类")
  @PostMapping("/createArchiveType")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createArchiveType(
      @RequestBody ArchiveTypeCreateRequest request) {
    return archiveTypeService.createArchiveType(request);
  }

  @Operation(summary = "更新档案门类")
  @PostMapping("/updateArchiveType")
  @WebApi(requireAuth = true)
  public BaseResult updateArchiveType(@RequestBody ArchiveTypeUpdateRequest request) {
    return archiveTypeService.updateArchiveType(request);
  }

  @Operation(summary = "删除档案门类")
  @PostMapping("/deleteArchiveType")
  @WebApi(requireAuth = true)
  public BaseResult deleteArchiveType(@RequestBody ArchiveTypeDeleteRequest request) {
    return archiveTypeService.deleteArchiveType(request);
  }
}
