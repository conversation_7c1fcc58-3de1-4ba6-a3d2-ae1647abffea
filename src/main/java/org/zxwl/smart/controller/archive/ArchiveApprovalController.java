package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.ApprovedListVO;
import org.zxwl.smart.domain.response.pickup.ApprovedVO;
import org.zxwl.smart.domain.response.pickup.PendingApprovalListVO;
import org.zxwl.smart.domain.response.pickup.PendingApprovalVO;
import org.zxwl.smart.service.archive.ArchiveApprovalService;

@Tag(name = "档案审批（我的事项）")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveApprovalController {

  private final ArchiveApprovalService archiveApprovalService;

  @Operation(summary = "获取待审批列表")
  @PostMapping("/getArchivePendingApprovalList")
  @WebApi(requireAuth = true)
  public PageResult<PendingApprovalListVO> getArchivePendingApprovalList(
      @RequestBody PendingApprovalListRequest request) {
    return archiveApprovalService.getArchivePendingApprovalList(request);
  }

  @Operation(summary = "获取待审批详情")
  @PostMapping("/getArchivePendingApproval")
  @WebApi(requireAuth = true)
  public SimpleResult<PendingApprovalVO> getArchivePendingApproval(
      @RequestBody PendingApprovalRequest request) {
    return archiveApprovalService.getArchivePendingApproval(request);
  }

  @Operation(summary = "审批待审批")
  @PostMapping("/approveArchive")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateListVO> approveArchive(@RequestBody ApprovalRequest request) {
    return archiveApprovalService.approveArchive(request);
  }

  @Operation(summary = "获取已审批列表")
  @PostMapping("/getArchiveApprovedList")
  @WebApi(requireAuth = true)
  public PageResult<ApprovedListVO> getArchiveApprovedList(
      @RequestBody ApprovedListRequest request) {
    return archiveApprovalService.getArchiveApprovedList(request);
  }

  @Operation(summary = "获取已审批详情")
  @PostMapping("/getArchiveApproved")
  @WebApi(requireAuth = true)
  public SimpleResult<ApprovedVO> getArchiveApproved(@RequestBody ApprovedRequest request) {
    return archiveApprovalService.getArchiveApproved(request);
  }
}
