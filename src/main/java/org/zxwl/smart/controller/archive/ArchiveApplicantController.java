package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.pickup.*;
import org.zxwl.smart.domain.response.BaseCreateListVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.pickup.ApplicantListVO;
import org.zxwl.smart.domain.response.pickup.ApplicantVO;
import org.zxwl.smart.service.archive.ArchiveApplicantService;

@Tag(name = "✅档案取档申请")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveApplicantController {

  private final ArchiveApplicantService archiveApplicantService;

  @Operation(summary = "获取取档申请列表")
  @PostMapping("/getArchiveApplicantList")
  @WebApi(requireAuth = true)
  public PageResult<ApplicantListVO> getArchiveApplicantList(
      @RequestBody ApplicantListRequest request) {
    return archiveApplicantService.getArchiveApplicantList(request);
  }

  @Operation(summary = "获取取档申请")
  @PostMapping("/getArchiveApplicant")
  @WebApi(requireAuth = true)
  public SimpleResult<ApplicantVO> getArchiveApplicant(@RequestBody ApplicantRequest request) {
    return archiveApplicantService.getArchiveApplicant(request);
  }

  @Operation(summary = "创建取档申请")
  @PostMapping("/createArchiveApplicant")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateListVO> createArchiveApplicant(
      @RequestBody ApplicantCreateRequest request) {
    return archiveApplicantService.createArchiveApplicant(request);
  }

  @Operation(summary = "撤销取档申请")
  @PostMapping("/revokeArchiveApplicant")
  @WebApi(requireAuth = true)
  public BaseResult revokeArchiveApplicant(@RequestBody ApplicantRevokeRequest request) {
    return archiveApplicantService.revokeArchiveApplicant(request);
  }

  @Operation(summary = "删除取档申请")
  @PostMapping("/deleteArchiveApplicant")
  @WebApi(requireAuth = true)
  public BaseResult deleteArchiveApplicant(@RequestBody ApplicantDeleteRequest request) {
    return archiveApplicantService.deleteArchiveApplicant(request);
  }
}
