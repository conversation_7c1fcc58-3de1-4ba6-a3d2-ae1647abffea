package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.*;
import org.zxwl.smart.domain.request.archive.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.archive.*;
import org.zxwl.smart.service.archive.ArchiveAttachmentService;
import org.zxwl.smart.service.archive.ArchiveService;

@Tag(name = "✅档案")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveController {

  private final ArchiveService archiveService;
  private final ArchiveAttachmentService archiveAttachmentService;

  @Operation(summary = "获取档案列表")
  @PostMapping("/getArchiveList")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @ArchiveCabinetApi
  @HRCabinetApi
  @SmartRackApi
  @LightRackApi
  @BoxRackApi
  public PageResult<ArchiveListVO> getArchiveList(@RequestBody ArchiveListRequest request) {
    return archiveService.getArchiveList(request);
  }

  @Operation(summary = "获取当前账号待取档案列表")
  @PostMapping("/getArchivePendingOutListByAccountLocal")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @ArchiveCabinetApi
  @HRCabinetApi
  @SmartRackApi
  @LightRackApi
  @BoxRackApi
  public PageResult<ArchivePendingOutListVO> getArchivePendingOutListByAccountLocal(
      @RequestBody ArchivePendingOutListRequest request) {
    return archiveService.getArchivePendingOutListByAccountLocal(request);
  }

  @Operation(summary = "获取档案详情")
  @PostMapping("/getArchive")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @ArchiveCabinetApi
  @HRCabinetApi
  @LightRackApi
  @SmartRackApi
  @BoxRackApi
  public SimpleResult<ArchiveVO> getArchive(@RequestBody ArchiveRequest request) {
    return archiveService.getArchive(request);
  }

  @Operation(summary = "获取待入库档案列表")
  @PostMapping("/getArchivePendingStorageList")
  @WebApi(requireAuth = true)
  public PageResult<ArchivePendingStorageListVO> getArchivePendingStorageList(
      @RequestBody ArchivePendingStorageListRequest request) {
    return archiveService.getArchivePendingStorageList(request);
  }

  @Operation(summary = "获取回收站档案列表")
  @PostMapping("/getDeletedArchiveList")
  @WebApi(requireAuth = true)
  public PageResult<ArchiveDeletedListVO> getDeletedArchiveList(
      @RequestBody ArchiveDeletedListRequest request) {
    return archiveService.getDeletedArchiveList(request);
  }

  @Operation(summary = "创建档案")
  @PostMapping("/createArchive")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createArchive(@RequestBody ArchiveCreateRequest request) {
    return archiveService.createArchive(request);
  }

  @Operation(summary = "更新档案")
  @PostMapping("/updateArchive")
  @WebApi(requireAuth = true)
  public BaseResult updateArchive(@RequestBody ArchiveUpdateRequest request) {
    return archiveService.updateArchive(request);
  }

  @Operation(summary = "更新档案在库档案状态")
  @PostMapping("/updateArchiveStockStatus")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  public BaseResult updateArchiveStockStatus(@RequestBody UpdateStockStatusRequest request) {
    return archiveService.updateArchiveStockStatus(request);
  }

  @Operation(summary = "删除档案")
  @PostMapping("/deleteArchive")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  public BaseResult deleteArchive(@RequestBody ArchiveDeleteRequest request) {
    return archiveService.deleteArchive(request);
  }

  @Operation(summary = "删除档案（物理删除）")
  @PostMapping("/deleteArchivePhysically")
  @WebApi(requireAuth = true)
  public BaseResult deleteArchivePhysically(@RequestBody ArchiveDeleteRequest request) {
    return archiveService.deleteArchivePhysically(request);
  }

  @Operation(summary = "恢复物理删除的档案")
  @PostMapping("/updateDeletedArchiveRestored")
  @WebApi(requireAuth = true)
  public BaseResult updateDeletedArchiveRestored(@RequestBody ArchiveRestoreRequest request) {
    return archiveService.updateDeletedArchiveRestored(request);
  }

  @Operation(summary = "档案绑定TID")
  @PostMapping("/archiveBindTID")
  @WebApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  public BaseResult archiveBindTID(@RequestBody ArchiveBindTIDRequest request) {
    return archiveService.archiveBindTID(request);
  }

  @Operation(summary = "更新档案绑定TID")
  @PostMapping("/updateArchiveTID")
  @WebApi(requireAuth = true)
  public BaseResult updateArchiveTID(@RequestBody ArchiveUpdateTIDRequest request) {
    return archiveService.updateArchiveTID(request);
  }

  @Operation(summary = "档案解绑TID")
  @PostMapping("/unbindArchiveTID")
  @WebApi(requireAuth = true)
  @WorkstationApi(requireAuth = true)
  public BaseResult unbindArchiveTID(@RequestBody ArchiveUnbindTIDRequest request) {
    return archiveService.unbindArchiveTID(request);
  }

  @Operation(summary = "更新档案位置", description = "该接口只为发光密集架用来做档案上架前的位置信息录入")
  @PostMapping("/updateArchiveLocation")
  @LightRackApi(requireAuth = true)
  public BaseResult updateArchiveLocation(@RequestBody UpdateLocationRequest request) {
    return archiveService.updateArchiveLocation(request);
  }

  @Operation(summary = "档案上架")
  @PostMapping("/archiveShelve")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  @HRCabinetApi(requireAuth = true)
  @SmartRackApi(requireAuth = true)
  @LightRackApi(requireAuth = true)
  @BoxRackApi(requireAuth = true)
  public BaseResult archiveShelve(@RequestBody ArchiveShelveRequest request) {
    return archiveService.archiveShelve(request);
  }

  @Operation(summary = "档案下架")
  @PostMapping("/archiveUnshelve")
  @WebApi(requireAuth = true)
  @HandheldApi(requireAuth = true)
  @InventoryCarApi(requireAuth = true)
  @ArchiveCabinetApi(requireAuth = true)
  @HRCabinetApi(requireAuth = true)
  @SmartRackApi(requireAuth = true)
  @LightRackApi(requireAuth = true)
  @BoxRackApi(requireAuth = true)
  public BaseResult archiveUnshelve(@RequestBody ArchiveUnshelveRequest request) {
    return archiveService.archiveUnshelve(request);
  }

  @Operation(summary = "档案装盒")
  @PostMapping("/archiveBoxing")
  @WebApi(requireAuth = true)
  public BaseResult archiveBoxing(@RequestBody ArchiveBoxingRequest request) {
    return archiveService.archiveBoxing(request);
  }

  @Operation(summary = "档案拆盒")
  @PostMapping("/archiveUnboxing")
  @WebApi(requireAuth = true)
  public BaseResult archiveUnboxing(@RequestBody ArchiveUnboxingRequest request) {
    return archiveService.archiveUnboxing(request);
  }

  @Operation(summary = "获取档案附件列表")
  @PostMapping("/getArchiveAttachmentList")
  @WebApi(requireAuth = true)
  public ListResult<ArchiveAttachmentVO> getArchiveAttachmentList(
      @RequestBody ArchiveAttachmentRequest request) {
    return archiveAttachmentService.getArchiveAttachmentList(request);
  }

  @Operation(summary = "创建档案附件")
  @PostMapping("/createArchiveAttachment")
  @WebApi(requireAuth = true)
  public BaseResult createArchiveAttachment(@RequestBody ArchiveAttachmentCreateRequest request) {
    return archiveAttachmentService.createArchiveAttachment(request);
  }

  @Operation(summary = "更新档案附件")
  @PostMapping("/updateArchiveAttachment")
  @WebApi(requireAuth = true)
  public BaseResult updateArchiveAttachment(@RequestBody ArchiveAttachmentUpdateRequest request) {
    return archiveAttachmentService.updateArchiveAttachment(request);
  }

  @Operation(summary = "删除档案附件")
  @PostMapping("/deleteArchiveAttachment")
  @WebApi(requireAuth = true)
  public BaseResult deleteArchiveAttachment(@RequestBody ArchiveAttachmentDeleteRequest request) {
    return archiveAttachmentService.deleteArchiveAttachment(request);
  }
}
