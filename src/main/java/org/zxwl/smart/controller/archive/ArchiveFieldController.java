package org.zxwl.smart.controller.archive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.archive.ArchiveFieldCreateRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldDeleteRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldListRequest;
import org.zxwl.smart.domain.request.archive.ArchiveFieldUpdateRequest;
import org.zxwl.smart.domain.response.BaseCreateVO;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.ListResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.archive.ArchiveFieldListVO;
import org.zxwl.smart.service.archive.ArchiveFieldService;

@Tag(name = "✅档案字段")
@ZHPTRestController("/archive")
@AllArgsConstructor
public class ArchiveFieldController {

  private final ArchiveFieldService archiveFieldService;

  @Operation(summary = "获取档案字段列表")
  @PostMapping("/getArchiveFieldList")
  @WebApi(requireAuth = true)
  public ListResult<ArchiveFieldListVO> getArchiveFieldList(
      @RequestBody ArchiveFieldListRequest request) {
    return archiveFieldService.getArchiveFieldList(request);
  }

  @Operation(summary = "新增档案字段")
  @PostMapping("/createArchiveField")
  @WebApi(requireAuth = true)
  public SimpleResult<BaseCreateVO> createArchiveField(
      @RequestBody ArchiveFieldCreateRequest request) {
    return archiveFieldService.createArchiveField(request);
  }

  @Operation(summary = "更新档案字段")
  @PostMapping("/updateArchiveField")
  @WebApi(requireAuth = true)
  public BaseResult updateArchiveField(@RequestBody ArchiveFieldUpdateRequest request) {
    return archiveFieldService.updateArchiveField(request);
  }

  @Operation(summary = "删除档案字段")
  @PostMapping("/deleteArchiveField")
  @WebApi(requireAuth = true)
  public BaseResult deleteArchiveField(@RequestBody ArchiveFieldDeleteRequest request) {
    return archiveFieldService.deleteArchiveField(request);
  }
}
