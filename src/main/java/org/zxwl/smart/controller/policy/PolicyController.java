package org.zxwl.smart.controller.policy;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.policy.*;
import org.zxwl.smart.domain.response.BaseResult;
import org.zxwl.smart.domain.response.PageResult;
import org.zxwl.smart.domain.response.SimpleResult;
import org.zxwl.smart.domain.response.policy.PolicyListVO;
import org.zxwl.smart.domain.response.policy.PolicyVO;
import org.zxwl.smart.service.policy.PolicyService;

@Tag(name = "✅智能策略")
@ZHPTRestController("/policy")
@AllArgsConstructor
public class PolicyController {

  private final PolicyService policyService;

  @Operation(summary = "获取策略列表")
  @PostMapping("/getPolicyList")
  @WebApi(requireAuth = true)
  public PageResult<PolicyListVO> getPolicyList(@RequestBody PolicyListRequest request) {
    return policyService.getPolicyList(request);
  }

  @Operation(summary = "获取策略详情")
  @PostMapping("/getPolicy")
  @WebApi(requireAuth = true)
  public SimpleResult<PolicyVO> getPolicy(@RequestBody PolicyRequest request) {
    return policyService.getPolicy(request);
  }

  @Operation(summary = "更新策略")
  @PostMapping("/updatePolicy")
  @WebApi(requireAuth = true)
  public BaseResult updatePolicy(@RequestBody PolicyUpdateRequest request) {
    return policyService.updatePolicy(request);
  }

  @Operation(summary = "更新策略启用状态")
  @PostMapping("/updatePolicyEnable")
  @WebApi(requireAuth = true)
  public BaseResult updatePolicyEnable(@RequestBody PolicyEnableRequest request) {
    return policyService.updatePolicyEnable(request);
  }
}
