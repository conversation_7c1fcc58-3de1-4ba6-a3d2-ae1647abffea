package org.zxwl.smart.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.zxwl.smart.common.annotation.AreaControlApi;
import org.zxwl.smart.common.annotation.WebApi;
import org.zxwl.smart.common.annotation.ZHPTRestController;
import org.zxwl.smart.domain.request.config.*;
import org.zxwl.smart.domain.response.*;
import org.zxwl.smart.domain.response.config.ConfigVO;
import org.zxwl.smart.service.config.ConfigService;

@Tag(name = "✅配置")
@ZHPTRestController("/config")
@AllArgsConstructor
public class ConfigController {

  private final ConfigService configService;

  @Operation(summary = "获取配置列表")
  @PostMapping("/getConfigList")
  @WebApi
  @AreaControlApi
  public ListResult<ConfigVO> getConfigList(@RequestBody ConfigListRequest request) {
    return configService.getConfigList(request);
  }

  @Operation(summary = "新增配置列表")
  @PostMapping("/createConfig")
  @WebApi(requireAuth = true)
  public BaseResult createConfig(@RequestBody ConfigInsertRequest request) {
    return configService.createConfig(request);
  }

  @Operation(summary = "修改配置列表")
  @PostMapping("/updateConfig")
  @WebApi(requireAuth = true)
  public BaseResult updateConfig(@RequestBody ConfigUpdateRequest request) {
    return configService.updateConfig(request);
  }

  @Operation(summary = "修改服务端配置", description = "服务端调试接口，前端不需要对接")
  @PostMapping("/updateServerConfig")
  @WebApi
  public BaseResult updateServerConfig(@RequestBody ServerConfigUpdateRequest request) {
    return configService.updateServerConfig(request);
  }

  @Operation(summary = "删除配置列表")
  @PostMapping("/deleteConfigList")
  @WebApi(requireAuth = true)
  public BaseResult deleteConfigList(@RequestBody ConfigListDeleteRequest request) {
    return configService.deleteConfigList(request);
  }
}
