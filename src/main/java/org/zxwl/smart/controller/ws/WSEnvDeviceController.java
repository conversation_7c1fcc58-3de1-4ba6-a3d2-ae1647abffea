package org.zxwl.smart.controller.ws;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;
import org.zxwl.smart.cache.AccountLocal;
import org.zxwl.smart.domain.request.ws.SocketRequest;
import org.zxwl.smart.domain.response.ws.SocketVO;

@Slf4j
@Controller
@AllArgsConstructor
public class WSEnvDeviceController {

  @MessageMapping("/hello")
  @SendTo("/topic/greetings")
  public SocketVO hello(
      SocketRequest request,
      @Header("simpSessionAttributes") Map<String, Object> sessionAttributes) {

    log.info("当前线程：{}", Thread.currentThread().getName());
    log.info("Session Attributes: {}", sessionAttributes);

    // 从session attributes中获取数据
    Long userId = (Long) sessionAttributes.get("userId");
    Long organizationId = (Long) sessionAttributes.get("organizationId");

    log.info("userId: {}, organizationId: {}", userId, organizationId);

    // 也可以使用AccountLocal中的数据
    log.info(
        "AccountLocal - userId: {}, organizationId: {}",
        AccountLocal.getUserId(),
        AccountLocal.getOrganizationId());

    return new SocketVO("hello " + request.getName());
  }
}
