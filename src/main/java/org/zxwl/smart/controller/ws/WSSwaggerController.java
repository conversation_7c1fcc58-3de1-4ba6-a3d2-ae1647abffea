package org.zxwl.smart.controller.ws;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.zxwl.smart.domain.response.env.EnvDataVO;
import org.zxwl.smart.domain.response.ws.WSAreaDataChangedVO;
import org.zxwl.smart.domain.response.ws.WSDeviceDataChangedVO;

/**
 * WebSocket接口文档控制器
 *
 * <p>此控制器仅用于在Swagger中展示WebSocket接口文档，不提供实际功能。 实际的WebSocket连接请使用 ws://localhost:18084/ws
 */
@Tag(name = "✅WebSocket接口文档", description = "WebSocket实时推送接口说明（仅文档展示）")
@RestController
@RequestMapping("/swagger")
public class WSSwaggerController {

  @Operation(
      summary = "WebSocket连接说明",
      description =
          """
            ## WebSocket连接信息

            ### 连接地址
            ```
            ws://localhost:18084/ws?Terminal=web&Authorization=Bearer_your_token_here
            ```

            ### 连接参数
            - **Terminal**: 终端类型
            - **Authorization**: 认证令牌

            ### 前端连接示例
            ```javascript
            // 使用原生WebSocket + STOMP
            const stompClient = new StompJs.Client({
                brokerURL: 'ws://localhost:18084/ws?Terminal=web&Authorization=Bearer_your_token'
            });
            stompClient.onConnect = (frame) => {
                setConnected(true);
                console.log("Connected: " + frame);
                stompClient.subscribe("/topic/deviceDataChanged", (message) => {
                    console.log('收到消息:', JSON.parse(message.body));
                });
                stompClient.subscribe("/topic/areaDataChanged", (message) => {
                    console.log('收到消息:', JSON.parse(message.body));
                });
                stompClient.subscribe("/topic/warehouseDataChanged", (message) => {
                    console.log('收到消息:', JSON.parse(message.body));
                });
            };
            stompClient.activate();
            ```

            ### 注意事项
            - 此接口仅用于文档展示，实际请使用WebSocket连接
            - 需要有效的JWT令牌才能建立连接
            - 支持跨域连接
            """)
  @PostMapping("/websocket-connection")
  public Object webSocketConnection() {
    return new Object() {
      public final String message = "此为WebSocket文档说明接口，实际请使用 ws://localhost:18084/ws 进行连接";
      public final String wsUrl = "ws://localhost:18084/ws";
      public final String[] requiredParams = {"Terminal=web", "Authorization=Bearer_your_token"};
      public final String[] supportedProtocols = {"WebSocket", "SockJS"};
    };
  }

  @Operation(
      summary = "环控设备数据变化推送",
      description =
          """
            ### 订阅主题
            ```
            /topic/deviceDataChanged
            ```

            ### 触发条件
            - 环控设备数据发生变更时自动推送
            - 包括温度、湿度、空气质量等数据变化
            """)
  @PostMapping("device-data-changed")
  public WSDeviceDataChangedVO deviceDataChanged() {
    return new WSDeviceDataChangedVO();
  }

  @Operation(
      summary = "环控区域数据变化推送",
      description =
          """
            ### 订阅主题
            ```
            /topic/areaDataChanged
            ```

            ### 触发条件
            - 环控设备区域数据发生变更时自动推送
            - 包括温度、湿度、空气质量等数据变化
            """)
  @PostMapping("area-data-changed")
  public WSAreaDataChangedVO areaDataChanged() {
    return new WSAreaDataChangedVO();
  }

  @Operation(
      summary = "库房数据变化推送",
      description =
          """
            ### 订阅主题
            ```
            /topic/warehouseDataChanged
            ```

            ### 触发条件
            - 库房数据发生变更时自动推送
            - 包括库房的温度、湿度、空气质量等数据变化
            """)
  @PostMapping("warehouse-data-changed")
  public EnvDataVO warehouseDataChanged() {
    return new EnvDataVO();
  }
}
