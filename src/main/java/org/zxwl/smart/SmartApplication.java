package org.zxwl.smart;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@OpenAPIDefinition(info = @Info(title = "智慧平台2.0", version = "v1", description = "智慧平台2.0接口"))
@EnableAspectJAutoProxy
@EnableScheduling
@SpringBootApplication
public class SmartApplication {

  public static void main(String[] args) {
    SpringApplication.run(SmartApplication.class, args);
  }
}
