-- we don't know how to generate root <with-no-name> (class Root) :(

grant alter, alter routine, create, create routine, create temporary tables, create view, delete, drop, event, execute, index, insert, lock tables, references, select, show view, trigger, update, grant option on halo.* to halo;

grant select on performance_schema.* to 'mysql.session'@localhost;

grant trigger on sys.* to 'mysql.sys'@localhost;

grant audit_abort_exempt, firewall_exempt, select, system_user on *.* to 'mysql.infoschema'@localhost;

grant audit_abort_exempt, authentication_policy_admin, backup_admin, clone_admin, connection_admin, firewall_exempt, persist_ro_variables_admin, session_variables_admin, shutdown, super, system_user, system_variables_admin on *.* to 'mysql.session'@localhost;

grant audit_abort_exempt, firewall_exempt, system_user on *.* to 'mysql.sys'@localhost;

grant alter, alter routine, application_password_admin, audit_abort_exempt, audit_admin, authentication_policy_admin, backup_admin, binlog_admin, binlog_encryption_admin, clone_admin, connection_admin, create, create role, create routine, create tablespace, create temporary tables, create user, create view, delete, drop, drop role, encryption_key_admin, event, execute, file, firewall_exempt, flush_optimizer_costs, flush_status, flush_tables, flush_user_resources, group_replication_admin, group_replication_stream, index, innodb_redo_log_archive, innodb_redo_log_enable, insert, lock tables, passwordless_user_admin, persist_ro_variables_admin, process, references, reload, replication client, replication slave, replication_applier, replication_slave_admin, resource_group_admin, resource_group_user, role_admin, select, sensitive_variables_observer, service_connection_admin, session_variables_admin, set_user_id, show databases, show view, show_routine, shutdown, super, system_user, system_variables_admin, table_encryption_admin, telemetry_log_admin, trigger, update, xa_recover_admin, grant option on *.* to root;

grant alter, alter routine, application_password_admin, audit_abort_exempt, audit_admin, authentication_policy_admin, backup_admin, binlog_admin, binlog_encryption_admin, clone_admin, connection_admin, create, create role, create routine, create tablespace, create temporary tables, create user, create view, delete, drop, drop role, encryption_key_admin, event, execute, file, firewall_exempt, flush_optimizer_costs, flush_status, flush_tables, flush_user_resources, group_replication_admin, group_replication_stream, index, innodb_redo_log_archive, innodb_redo_log_enable, insert, lock tables, passwordless_user_admin, persist_ro_variables_admin, process, references, reload, replication client, replication slave, replication_applier, replication_slave_admin, resource_group_admin, resource_group_user, role_admin, select, sensitive_variables_observer, service_connection_admin, session_variables_admin, set_user_id, show databases, show view, show_routine, shutdown, super, system_user, system_variables_admin, table_encryption_admin, telemetry_log_admin, trigger, update, xa_recover_admin, grant option on *.* to root@localhost;

create table air_quality_record
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    device_id            bigint                             not null comment '设备id',
    device_name          varchar(60)                        not null comment '设备名字',
    temperature          decimal(5, 2)                      null comment '温度 (单位：摄氏度)',
    humidity             decimal(5, 2)                      null comment '湿度 (单位：百分比)',
    tvoc                 decimal(10, 2)                     null comment 'TVOC (总挥发性有机物)',
    co2                  decimal(10, 2)                     null comment '二氧化碳 (单位：ppm)',
    pm2_5                decimal(10, 2)                     null comment 'PM2.5 (单位：微克/立方米)',
    pm10                 decimal(10, 2)                     null comment 'PM10 (单位：微克/立方米)',
    ozone                decimal(10, 2)                     null comment '臭氧 (单位：ppm)',
    formaldehyde         decimal(10, 2)                     null comment '甲醛 (单位：ppm)',
    illuminance          decimal(10, 2)                     null comment '光照度 (单位：lux)',
    no2                  decimal(10, 2)                     null comment '二氧化氮 (单位：ppm)',
    so2                  decimal(10, 2)                     null comment '二氧化硫 (单位：ppm)',
    atmospheric_pressure decimal(10, 2)                     null comment '大气压 (单位：hPa)',
    oxygen               decimal(10, 2)                     null comment '氧气浓度 (单位：%)',
    created_at           datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '云测仪历史记录表';

create index idx_device_id
    on air_quality_record (device_id);

create table archive
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    organization_id     bigint                                not null comment '机构id',
    warehouse_id        bigint                                null comment '所属库房id',
    archive_type_id     bigint                                not null comment '档案门类id',
    parent_id           bigint                                null comment '上层id（盒id或档案id）',
    type                varchar(20) default 'archive'         not null comment '类型，box档案盒，archive档案，volume卷内档案',
    pickup_status       varchar(20) default 'normal'          not null comment '取档状态，normal正常，applying申请中，outbound_in出库中，outbound已出库，returning归还中',
    stock_status        varchar(20) default 'out'             not null comment '在库状态，in在库，out不在库',
    shelving_status     varchar(20) default 'out'             not null comment '在架状态，pending_in待上架，in在架，pending_out待下架，out不在架',
    archive_name        varchar(100)                          null comment '名字',
    archive_no          varchar(100)                          null comment '编号',
    tid                 varchar(60)                           null comment '标签TID',
    rack_group_no       int                                   null comment '档案所在密集架组号',
    rack_column_no      int                                   null comment '档案所在密集架列号',
    rack_panel_No       int                                   null comment '档案所在密集架面号',
    rack_section_no     int                                   null comment '档案所在密集架节号',
    rack_layer_no       int                                   null comment '档案所在密集架层号',
    rack_grid_no        int                                   null comment '档案所在密集架格号',
    hr_cabinet_group_no int                                   null comment '档案所在人事柜组号',
    hr_cabinet_no       int                                   null comment '档案所在人事柜号',
    hr_cabinet_layer_no int                                   null comment '档案所在人事柜层号',
    hr_cabinet_grid_no  int                                   null comment '档案所在人事柜格号',
    cabinet_group_no    int                                   null comment '档案所在柜组号',
    cabinet_no          int                                   null comment '档案所在柜号',
    cabinet_grid_no     int                                   null comment '档案所在柜格号',
    shelving_id         bigint                                null comment '上架人id',
    shelving_at         datetime                              null comment '上架时间',
    created_id          bigint                                null comment '创建人id',
    updated_id          bigint                                null comment '更新人id',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at           bigint      default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_archive_type_id_archive_no
        unique (archive_type_id, archive_no, delete_at)
)
    comment '档案表';

create index idx_tid
    on archive (tid);

create table archive_applicant
(
    id              bigint auto_increment comment '主键id'
        primary key,
    archive_id      bigint                             not null comment '档案id',
    status          varchar(20)                        not null comment '状态，pending待审批，approved审批通过，reject审批拒绝，revoke已撤销',
    borrow_intent   varchar(20)                        not null comment '借阅目的，internal内部查阅，external外部查阅，transfer移交，outbound出库',
    applicant_name  varchar(60)                        not null comment '申请人姓名',
    applicant_phone varchar(20)                        not null comment '申请人手机号，用于催还',
    applicant_email varchar(60)                        null comment '申请人邮箱',
    borrow_days     int                                not null comment '借阅天数',
    borrow_at       datetime                           not null comment '借阅时间',
    operator_id     bigint                             not null comment '经办人id',
    remark          varchar(100)                       null comment '备注',
    approval_at     datetime                           null comment '审批时间',
    need_back_at    datetime                           null comment '应归还时间，审批通过后该字段才会有值',
    created_id      bigint                             null comment '创建人id',
    updated_id      bigint                             null comment '更新人id',
    created_at      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at       bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '档案取档申请表';

create table archive_approval
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    archive_applicant_id bigint                             not null comment '取档申请id',
    approval_status      varchar(20)                        not null comment '审批状态，approved审批通过，reject审批拒绝',
    approver_id          bigint                             not null comment '审批人id',
    approval_at          datetime                           not null comment '审批时间',
    approval_comment     varchar(100)                       null comment '审批意见',
    created_at           datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at           datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at            bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '档案取档审批表';

create index idx_approver_id
    on archive_approval (approver_id);

create index idx_archive_applicant_id
    on archive_approval (archive_applicant_id);

create table archive_field
(
    id               bigint auto_increment comment '主键id'
        primary key,
    archive_type_id  bigint                                null comment '档案门类id',
    field_type       varchar(20) default 'string'          not null comment '字段类型，string字符串，image图片，file文件',
    fixed_field_name varchar(60)                           null comment '固定列名，archiveName档案名字，archiveNo档案编号',
    field_name       varchar(60)                           not null comment '字段名字',
    field_ordinal    int         default 0                 not null comment '字段位置，越小越靠前',
    field_width      int                                   null comment '字段列宽度',
    is_show_list     tinyint     default 1                 not null comment '是否列表展示，0不展示，1展示',
    is_input         tinyint     default 1                 not null comment '是否支持录入，0不支持，1支持',
    is_required      tinyint     default 0                 not null comment '是否是必填，0不必填，1必填',
    is_search        tinyint     default 0                 not null comment '是否支持搜索，0不支持，1支持',
    is_ad_search     tinyint     default 0                 not null comment '是否支持高级搜索，0不支持，1支持',
    remark           varchar(100)                          null comment '备注',
    created_id       bigint                                null comment '创建人id',
    updated_id       bigint                                null comment '更新人id',
    created_at       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at        bigint      default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '档案字段表';

create table archive_field_value
(
    id                bigint auto_increment comment '主键id'
        primary key,
    archive_id        bigint                             not null comment '所属档案id',
    archives_field_id bigint                             not null comment '档案字段id',
    field_name        varchar(60)                        not null comment '字段名',
    field_value       varchar(100)                       null comment '字段值',
    created_at        datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '档案字段值表';

create index idx_archive_id_archives_field_id
    on archive_field_value (archive_id, archives_field_id);

create table archive_operator_record
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    organization_id     bigint                                not null comment '机构id',
    operator_type       varchar(20)                           not null comment '操作类型，create-新建，update-更新，delete-删除，bind_tid-绑定TID，update_tid-更新TID，unbind_tid-解绑TID，boxing-装盒，unboxing-拆盒，stock_in-入库，stock_out-出库，shelve-上架，unshelve-下架',
    archive_type_id     bigint                                not null comment '档案门类id',
    archive_id          bigint                                not null comment '档案id',
    archive_type        varchar(20) default 'archive'         not null comment '类型，box档案盒，archive档案，volume卷内档案',
    archive_name        varchar(100)                          null comment '名字',
    archive_no          varchar(100)                          null comment '编号',
    rack_group_no       int                                   null comment '档案所在密集架组号',
    rack_column_no      int                                   null comment '档案所在密集架列号',
    rack_panel_No       int                                   null comment '档案所在密集架面号',
    rack_section_no     int                                   null comment '档案所在密集架节号',
    rack_layer_no       int                                   null comment '档案所在密集架层号',
    rack_grid_no        int                                   null comment '档案所在密集架格号',
    hr_cabinet_group_no int                                   null comment '档案所在人事柜组号',
    hr_cabinet_no       int                                   null comment '档案所在人事柜号',
    hr_cabinet_layer_no int                                   null comment '档案所在人事柜层号',
    hr_cabinet_grid_no  int                                   null comment '档案所在人事柜格号',
    cabinet_group_no    int                                   null comment '档案所在柜组号',
    cabinet_no          int                                   null comment '档案所在柜号',
    cabinet_grid_no     int                                   null comment '档案所在柜格号',
    operator_id         bigint                                null comment '操作人id',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '档案操作记录表';

create index idx_created_at
    on archive_operator_record (created_at);

create index idx_operator_id
    on archive_operator_record (operator_id);

create index idx_organization_id_archive_type_id_archive_id
    on archive_operator_record (organization_id, archive_type_id, archive_id);

create table archive_pending_pickup
(
    id         bigint auto_increment comment '主键id'
        primary key,
    archive_id bigint                             not null comment '档案id',
    user_id    bigint                             not null comment '用户id',
    created_id bigint                             null comment '创建人id',
    created_at datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_archive_id_user_id
        unique (archive_id, user_id)
)
    comment '档案待取表';

create table archive_pickup
(
    id              bigint auto_increment comment '主键id'
        primary key,
    archive_id      bigint                             not null comment '档案id',
    status          varchar(20)                        not null comment '状态，pending待审批，approved审批通过，reject审批拒绝，extracted已取出，returned已归还，revoke已撤销',
    borrow_intent   varchar(20)                        not null comment '借阅目的，internal内部查阅，external外部查阅，transfer移交，outbound出库',
    applicant_name  varchar(60)                        not null comment '申请人姓名',
    applicant_phone varchar(20)                        not null comment '手机号，用于催还',
    applicant_email varchar(60)                        null comment '申请人邮箱',
    borrow_days     int                                not null comment '借阅天数',
    borrow_at       datetime                           not null comment '借阅时间',
    need_back_at    datetime                           not null comment '应归还时间',
    handler_name    varchar(60)                        not null comment '经办人姓名',
    approval_at     datetime                           null comment '审批时间',
    extracted_at    datetime                           null comment '取出时间',
    returned_at     datetime                           null comment '归还时间',
    remark          varchar(100)                       null comment '备注',
    created_id      bigint                             null comment '创建人id',
    updated_id      bigint                             null comment '更新人id',
    created_at      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at       bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '档案取出表';

create index idx_archive_id
    on archive_pickup (archive_id);

create index idx_status_delete_at
    on archive_pickup (status, delete_at);

create table archive_task
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    archive_approval_id bigint                             null comment '审批id',
    operator_id         bigint                             null comment '经办人id',
    archive_id          bigint                             not null comment '档案id',
    task_type           varchar(20)                        not null comment '任务类型，in存入，out取出',
    status              varchar(20)                        not null comment '状态，pending待执行，executing执行中，pending_pickup待取出，completed完成',
    execution_type      varchar(20)                        not null comment '执行方式，桁架，AGV',
    task_source         varchar(20)                        not null comment '任务来源',
    created_id          bigint                             null comment '创建人id',
    updated_id          bigint                             null comment '更新人id',
    created_at          datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at           bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_archive_id
        unique (archive_id, delete_at)
)
    comment '档案存取任务表';

create index idx_operator_id
    on archive_task (operator_id);

create table archive_type
(
    id                bigint auto_increment comment '主键id'
        primary key,
    organization_id   bigint                                not null comment '机构id',
    pid               bigint                                null comment '父类id',
    type              varchar(20) default 'archive'         not null comment '门类类型，archive档案级，box盒级',
    type_name         varchar(60)                           not null comment '门类名称',
    other_name        varchar(60)                           null comment '门类别名',
    is_support_manual tinyint     default 0                 not null comment '是否支持手动上架，0不支持，1支持',
    created_id        bigint                                null comment '创建人id',
    updated_id        bigint                                null comment '更新人id',
    created_at        datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at         bigint      default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '档案门类表';

create table cabinet
(
    id               bigint auto_increment comment '主键id'
        primary key,
    cabinet_group_id bigint                             not null comment '所属柜组id',
    cabinet_no       int                                not null comment '柜子编号',
    is_main_cabinet  tinyint                            not null comment '是否是主柜 0不是，1是',
    cabinet_grid_num int                                not null comment '柜子总格数',
    created_id       bigint                             null comment '创建人id',
    updated_id       bigint                             null comment '更新人id',
    created_at       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_cabinet_group_id_cabinet_no
        unique (cabinet_group_id, cabinet_no)
)
    comment '柜体信息表';

create table cabinet_grid
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    cabinet_group_id   bigint                                not null comment '柜组id',
    cabinet_id         bigint                                not null comment '柜子id',
    grid_capacity      int         default 1                 not null comment '格子容量，柜子按配置产生一般为20',
    grid_used_capacity int         default 0                 not null comment '柜子已用容量',
    grid_status        varchar(20) default 'normal'          not null comment '格口状态，normal正常，open打开，lock锁定',
    grid_no            int                                   not null comment '格子号/格子编号',
    created_id         bigint                                null comment '创建人id',
    updated_id         bigint                                null comment '更新人id',
    created_at         datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_cabinet_id_grid_no
        unique (cabinet_id, grid_no)
)
    comment '柜子格口表';

create index idx_cabinet_group_id
    on cabinet_grid (cabinet_group_id);

create table cabinet_group
(
    id                      bigint auto_increment comment '主键id'
        primary key,
    organization_id         bigint                             not null comment '机构id',
    warehouse_id            bigint                             not null comment '所属库房id',
    cabinet_type            varchar(20)                        null comment '柜体类型',
    group_no                int                                not null comment '柜组编号',
    group_name              varchar(60)                        not null comment '柜组名',
    main_cabinet_no         int                                not null comment '主柜编号',
    sub_cabinet_count       int                                not null comment '副柜数量',
    main_cabinet_grid_count int                                not null comment '主柜格数',
    sub_cabinet_grid_count  int                                not null comment '副柜格数',
    cabinet_grid_capacity   int                                not null comment '格口容量',
    control_ip              varchar(20)                        null comment '通信ip',
    control_port            int                                null comment '通信端口',
    remark                  varchar(100)                       null comment '备注',
    created_id              bigint                             null comment '创建人id',
    updated_id              bigint                             null comment '更新人id',
    created_at              datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at              datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at               bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_group_no
        unique (group_no, delete_at)
)
    comment '柜组';

create index idx_warehouse_id
    on cabinet_group (warehouse_id);

create table dept
(
    id         bigint auto_increment comment '主键id'
        primary key,
    dept_no    varchar(60)                        not null comment '部门编号',
    dept_name  varchar(60)                        not null comment '部门名字',
    remark     varchar(100)                       null comment '备注',
    created_id bigint                             not null comment '创建人id',
    updated_id bigint                             not null comment '更新人id',
    created_at datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at  bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '部门信息表';

create table device_alarm
(
    id             bigint auto_increment comment '主键id'
        primary key,
    warehouse_id   bigint                             not null comment '所属库房id',
    device_type    varchar(30)                        not null comment '设备类型',
    device_id      bigint                             not null comment '设备id',
    alarm_status   varchar(20)                        not null comment '告警状态，active未解除，cleared已解除',
    deal_status    varchar(20)                        not null comment '长津湖里状态，unprocessed待处理，processed已处理',
    alarm_type     varchar(20)                        not null comment '告警真实性，real真实，false误报，fault故障，test测试',
    alarm_category varchar(20)                        null comment '告警类别，fire火警，water_leakage漏水，security安防，environment_control环控，device_management管理设备',
    alarm_content  varchar(200)                       not null comment '告警信息',
    created_at     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '环控设备告警信息表';

create table device_log
(
    id              bigint auto_increment comment '主键id'
        primary key,
    warehouse_id    bigint                             not null comment '所属库房id',
    device_type     varchar(30)                        not null comment '设备类型',
    device_id       bigint                             not null comment '设备id',
    device_name     varchar(60)                        not null comment '设备名字',
    operate_type    varchar(30)                        not null comment '操作类型',
    operate_context varchar(500)                       null comment '操作描述',
    remark          varchar(100)                       null comment '备注',
    operator_id     bigint                             null comment '操作人id',
    operated_at     datetime                           not null comment '操作时间',
    created_at      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '环控设备操作日志表';

create index idx_device_id
    on device_log (device_id);

create table door_control_device
(
    id            bigint auto_increment comment '主键id'
        primary key,
    warehouse_id  bigint                             not null comment '所属库房id',
    device_status varchar(20)                        not null comment '设备状态，online在线、offline离线',
    device_name   varchar(60)                        not null comment '设备名称',
    device_no     varchar(60)                        not null comment '设备编号',
    device_ip     varchar(20)                        null comment '设备ip',
    device_port   int                                null comment '设备port',
    visual_config varchar(200)                       null comment '地图配置信息',
    created_id    bigint                             not null comment '创建人id',
    updated_id    bigint                             not null comment '更新人id',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at     bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '门禁设备表';

create index idx_warehouse_id
    on door_control_device (warehouse_id);

create table door_control_record
(
    id                     bigint auto_increment comment '主键id'
        primary key,
    door_control_device_id bigint                             not null comment '设备id',
    device_name            varchar(60)                        not null comment '设备名字',
    device_no              varchar(60)                        not null comment '设备编号',
    is_open_door           tinyint  default 1                 not null comment '是否是开门，0关门，1开门',
    control_method         varchar(20)                        not null comment '开门方式，user_account，id_card，finger_id，face_id，remote_control',
    device_user_id         bigint                             null comment '设备关联用户id',
    device_user_name       varchar(60)                        null comment '设备关联用户名',
    created_at             datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '门禁设备历史记录表';

create index idx_door_control_device_id
    on door_control_record (door_control_device_id);

create table door_control_user
(
    id                     bigint auto_increment comment '主键id'
        primary key,
    door_control_device_id bigint                                not null comment '设备id',
    user_status            varchar(20) default 'normal'          not null comment '用户状态，normal正常，disable禁用',
    user_name              varchar(60)                           null comment '用户名',
    user_account           varchar(60)                           not null comment '用户账号',
    user_pwd               varchar(60)                           not null comment '用户密码',
    id_card                varchar(60)                           null comment '卡号',
    finger_id              varchar(100)                          null comment '指纹id',
    face_id                varchar(100)                          null comment '人脸地址',
    created_id             bigint                                not null comment '创建人id',
    updated_id             bigint                                not null comment '更新人id',
    created_at             datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at             datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at              bigint      default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '门禁用户表';

create index idx_door_control_device_id
    on door_control_user (door_control_device_id);

create table eas_device_alarm_record
(
    id           bigint auto_increment comment '主键id'
        primary key,
    warehouse_id bigint                             not null comment '所属库房id',
    device_id    bigint                             not null comment '防盗门id',
    device_ip    varchar(20)                        not null comment '防盗门ip',
    device_name  varchar(60)                        null comment '防盗门名称',
    device_no    varchar(60)                        null comment '防盗门编号',
    archive_id   bigint                             not null comment '档案id',
    archive_name varchar(60)                        null comment '档案名称',
    archive_no   varchar(60)                        null comment '档案编号',
    created_at   datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '防盗门设备告警记录表';

create index idx_device_id
    on eas_device_alarm_record (device_ip);

create index idx_warehouse_id
    on eas_device_alarm_record (warehouse_id);

create table eas_device_person_num
(
    id            bigint auto_increment comment '主键id'
        primary key,
    device_ip     varchar(20)                        not null comment '防盗门ip',
    total_in_num  int      default 0                 not null comment '总进入人数',
    total_out_num int      default 0                 not null comment '总离开人数',
    date_at       date     default (curdate())       not null comment '统计日期',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_device_ip_calendar
        unique (device_ip, date_at)
)
    comment '防盗门人数统计表';

create table energy_consumption_record
(
    id          bigint auto_increment comment '主键id'
        primary key,
    device_id   bigint                             not null comment '设备id',
    device_name varchar(60)                        not null comment '设备名字',
    voltage     decimal(10, 2)                     null comment '电压',
    current     decimal(10, 2)                     null comment '电流',
    power       decimal(10, 2)                     null comment '功率',
    created_at  datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '能耗记录表';

create index idx_device_id
    on energy_consumption_record (device_id);

create table env_device
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    warehouse_id       bigint                             not null comment '所属库房id',
    device_type        varchar(20)                        not null comment '设备类型',
    device_status      varchar(20)                        not null comment '设备状态，online在线、offline离线',
    device_name        varchar(60)                        not null comment '设备名称',
    device_no          varchar(60)                        null comment '设备编号',
    device_ip          varchar(20)                        null comment '设备ip',
    device_port        int                                null comment '设备port',
    device_serial_port int                                null comment '设备串口（数据汇集箱上的串口）',
    device_serial_addr int                                null comment '串口地址（设备线路上的串口地址）',
    visual_config      varchar(200)                       null comment '地图配置信息',
    created_id         bigint                             not null comment '创建人id',
    updated_id         bigint                             not null comment '更新人id',
    created_at         datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at          bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '环控设备表';

create index idx_warehouse_id
    on env_device (warehouse_id);

create table env_device_area
(
    id            bigint auto_increment comment '主键id'
        primary key,
    warehouse_id  bigint                             not null comment '所属库房id',
    area_name     varchar(60)                        not null comment '区域名称',
    visual_config varchar(200)                       null comment '地图配置信息',
    created_id    bigint                             not null comment '创建人id',
    updated_id    bigint                             not null comment '更新人id',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at     bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '环控设备区';

create index idx_warehouse_id
    on env_device_area (warehouse_id);

create table env_device_area_device
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    env_device_area_id bigint                             not null comment '所属区域id',
    env_device_id      bigint                             not null comment '所属设备id',
    created_at         datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_area_id_device_id
        unique (env_device_area_id, env_device_id)
)
    comment '环控设备区与设备关系表';

create table env_device_field_value
(
    id          bigint auto_increment comment '主键id'
        primary key,
    device_id   bigint                             not null comment '设备id',
    filed_name  varchar(60)                        not null comment '属性名',
    filed_value varchar(60)                        null comment '属性值',
    created_at  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_device_id_filed_name
        unique (device_id, filed_name)
)
    comment '环控设备扩展属性表';

create table hr_cabinet_grid
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    cabinet_group_id   bigint                             not null comment '柜组id',
    cabinet_layer_id   bigint                             not null comment '柜层id',
    grid_capacity      int      default 1                 not null comment '格子容量',
    grid_used_capacity int      default 0                 not null comment '格子已用容量',
    grid_no            int                                not null comment '格子号/格子编号',
    created_id         bigint                             null comment '创建人id',
    updated_id         bigint                             null comment '更新人id',
    created_at         datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_cabinet_layer_id_grid_no
        unique (cabinet_layer_id, grid_no)
)
    comment '人事柜子格口表';

create index idx_cabinet_group_id
    on hr_cabinet_grid (cabinet_group_id);

create table hr_cabinet_group
(
    id              bigint auto_increment comment '主键id'
        primary key,
    organization_id bigint                             not null comment '机构id',
    warehouse_id    bigint                             not null comment '所属库房id',
    group_no        int                                not null comment '柜组编号',
    group_name      varchar(60)                        not null comment '柜组名',
    cabinet_num     int                                not null comment '柜体数量',
    layer_num       int                                not null comment '柜层数',
    layer_grid_num  int                                not null comment '柜层格数',
    control_ip      varchar(20)                        null comment '通信ip',
    control_port    int                                null comment '通信端口',
    remark          varchar(100)                       null comment '备注',
    created_id      bigint                             null comment '创建人id',
    updated_id      bigint                             null comment '更新人id',
    created_at      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at       bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_group_no
        unique (group_no, delete_at)
)
    comment '人事柜组';

create table hr_cabinet_layer
(
    id               bigint auto_increment comment '主键id'
        primary key,
    cabinet_group_id bigint                             not null comment '所属柜组id',
    cabinet_no       int                                not null comment '柜体编号',
    layer_no         int                                not null comment '柜层编号',
    cabinet_grid_num int                                not null comment '柜层总格数',
    created_id       bigint                             null comment '创建人id',
    updated_id       bigint                             null comment '更新人id',
    created_at       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_cabinet_group_id_cabinet_no_layer_no
        unique (cabinet_group_id, cabinet_no, layer_no)
)
    comment '人事柜层信息表';

create table inventory_all
(
    id             bigint auto_increment comment '主键id'
        primary key,
    inventory_name varchar(100)                       null comment '盘点任务名称',
    correct_num    int      default 0                 not null comment '盘点正确数量',
    error_num      int      default 0                 not null comment '盘点异常数量',
    created_id     bigint                             not null comment '创建人id',
    created_at     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '全局盘点记录表';

create table inventory_all_error
(
    id               bigint auto_increment comment '主键id'
        primary key,
    inventory_all_id bigint                             not null comment '全关盘点记录id',
    error_status     varchar(20)                        not null comment '异常状态，错盘，未盘',
    archive_id       bigint                             null comment '档案id',
    box_id           bigint                             not null comment '档案盒id',
    layer_id         bigint                             null comment '密集架层位id',
    cabinet_id       bigint                             null comment '柜体id',
    grid_id          bigint                             null comment '格位id',
    remark           varchar(100)                       null comment '备注',
    created_at       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '全局盘点异常记录表';

create index idx_inventory_all_id
    on inventory_all_error (inventory_all_id);

create table inventory_detail
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    inventory_record_id bigint                             not null comment '盘点记录id',
    inventory_result_id bigint                             not null comment '盘点结果id',
    archive_id          bigint                             not null comment '档案id',
    status              varchar(20)                        not null comment '结果状态，on_shelf在架，off_shelf不在架，misplaced错架，borrow外借',
    created_at          datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '盘点详情表';

create index idx_inventory_record_id
    on inventory_detail (inventory_record_id);

create index index_inventory_result_id
    on inventory_detail (inventory_result_id);

create table inventory_record
(
    id                bigint auto_increment comment '主键id'
        primary key,
    organization_id   bigint                             not null comment '机构id',
    inventory_task_id bigint                             null comment '盘点任务id',
    device_id         bigint                             null comment '设备id',
    warehouse_id      bigint                             not null comment '库房id',
    inventor_type     varchar(20)                        not null comment '盘点类型，handheld手持机，inventory_car盘点车，cabinet智能柜，rack密集架',
    on_shelf_count    int      default 0                 not null comment '在架数',
    off_shelf_count   int      default 0                 not null comment '不在架数',
    misplaced_count   int      default 0                 not null comment '错架数',
    borrow_count      int      default 0                 not null comment '在借数',
    rack_group_no     int                                null comment '密集架组号',
    rack_column_no    int                                null comment '密集架列号',
    rack_panel_no     int                                null comment '密集架面号',
    rack_section_no   int                                null comment '密集架节号',
    rack_layer_no     int                                null comment '密集架层号',
    cabinet_group_no  int                                null comment '柜组号',
    cabinet_no        int                                null comment '柜体号',
    cabinet_grid_no   int                                null comment '柜格号',
    created_id        bigint                             null comment '创建人id',
    created_at        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '盘点记录表';

create table inventory_result
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    inventory_record_id bigint                             not null comment '盘点记录id',
    on_shelf_count      int      default 0                 not null comment '在架数',
    off_shelf_count     int      default 0                 not null comment '不在架数',
    misplaced_count     int      default 0                 not null comment '错架数',
    borrow_count        int      default 0                 not null comment '在借数',
    rack_group_no       int                                null comment '密集架组号',
    rack_column_no      int                                null comment '密集架列号',
    rack_panel_no       int                                null comment '密集架面号',
    rack_section_no     int                                null comment '密集架节号',
    rack_layer_no       int                                null comment '密集架层号',
    cabinet_group_no    int                                null comment '柜组号',
    cabinet_no          int                                null comment '柜体号',
    cabinet_grid_no     int                                null comment '柜格号',
    created_at          datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '盘点结果表';

create index idx_inventory_record_id
    on inventory_result (inventory_record_id);

create table inventory_task
(
    id                bigint auto_increment comment '主键id'
        primary key,
    warehouse_id      bigint                             not null comment '库房id',
    warehouse_area_id bigint                             null comment '库区id',
    task_name         varchar(60)                        null comment '任务名字',
    task_execution_at datetime                           not null comment '任务执行时间',
    task_long_time    double                             null comment '任务时长',
    created_id        bigint                             not null comment '创建人id',
    created_at        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at         bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '盘点任务表';

create table organization
(
    id                bigint auto_increment comment '主键id'
        primary key,
    parent_id         bigint                             null comment '上级机构id',
    organization_no   varchar(60)                        not null comment '机构编号',
    organization_name varchar(60)                        not null comment '机构名称',
    level             int      default 1                 not null comment '机构层级',
    address           varchar(100)                       null comment '机构路径',
    remark            varchar(100)                       null comment '备注',
    created_id        bigint                             null comment '创建人id',
    updated_id        bigint                             null comment '更新人id',
    created_at        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at         bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_organization_name
        unique (organization_name, delete_at),
    constraint uk_organization_no
        unique (organization_no, delete_at)
)
    comment '机构表';

create table organization_user
(
    id              bigint auto_increment comment '主键id'
        primary key,
    organization_id bigint                             not null comment '机构id',
    archive_type_id bigint                             not null comment '档案门类id',
    user_id         bigint                             not null comment '用户id',
    created_at      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    constraint uk_organization_id_user_id
        unique (organization_id, user_id)
)
    comment '机构用户关系表';

create table rack_grid
(
    id            bigint auto_increment comment '主键id'
        primary key,
    rack_group_id bigint                             not null comment '所属架组id',
    rack_layer_id bigint                             not null comment '密集架层id',
    grid_no       int                                not null comment '格子号/格子编号',
    created_id    bigint                             null comment '创建人id',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_rack_layer_id_grid_no
        unique (rack_layer_id, grid_no)
)
    comment '密集架格口表';

create index idx_rack_group_id
    on rack_grid (rack_group_id);

create table rack_group
(
    id              bigint auto_increment comment '主键id'
        primary key,
    organization_id bigint                             not null comment '机构id',
    warehouse_id    bigint                             not null comment '所属库房id',
    rack_type       varchar(20)                        not null comment '密集架类型，普通密集架，智能密集架，盒定位密集架，发光密集架',
    rack_vendor     varchar(20)                        not null comment '密集架厂商，知行，天骄，方德...',
    column_num      int                                not null comment '列数',
    panel_num       int                                not null comment '面数',
    section_num     int                                not null comment '节数',
    layer_num       int                                not null comment '层数',
    grid_num        int                                not null comment '格数',
    group_no        int                                not null comment '组编号',
    group_name      varchar(60)                        not null comment '架组名',
    fixed_column_no int                                not null comment '固定列号',
    control_ip      varchar(20)                        null comment '架体ip',
    control_port    int                                null comment '架体端口',
    monitor_ip      varchar(20)                        null comment '监控ip',
    monitor_port    int                                null comment '监控端口',
    remark          varchar(100)                       null comment '备注',
    created_id      bigint                             null comment '创建人id',
    updated_id      bigint                             null comment '更新人id',
    created_at      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at       bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_group_no
        unique (group_no, delete_at)
)
    comment '密集架组';

create index idx_warehouse_id
    on rack_group (warehouse_id);

create table rack_layer
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    rack_group_id       bigint                             not null comment '所属架组id',
    archive_type_id     bigint                             null comment '层位关联门类id',
    column_no           int                                not null comment '列号',
    panel_no            int                                not null comment '面号',
    section_no          int                                not null comment '节号',
    layer_no            int                                not null comment '层号',
    layer_tid           varchar(60)                        null comment '层标TID',
    layer_capacity      int                                null comment '层容量',
    layer_used_capacity int      default 0                 not null comment '层已用容量',
    on_count            int      default 0                 not null comment '在架数',
    wrong_count         int      default 0                 not null comment '错架数',
    inventory_at        datetime                           null comment '盘点时间',
    created_id          bigint                             null comment '创建人id',
    created_at          datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_rack_group_id_colum_panel_section_layer
        unique (rack_group_id, column_no, panel_no, section_no, layer_no)
)
    comment '密集架层位表';

create table recycling
(
    id          int auto_increment
        primary key,
    name        varchar(255) null comment '回收箱名称',
    online      varchar(255) null comment '在线状态: not_online 不在线，online在线',
    read_time   datetime     null comment '读取时间',
    create_time datetime     null comment '创建时间',
    ip          varchar(255) null comment 'ip地址'
);

create table rfid_list
(
    id            int auto_increment
        primary key,
    no            varchar(255) null comment 'RFID号',
    serial_number varchar(255) null comment '序列号',
    read_time     datetime     null comment '最近读取时间'
);

create table robot_inventory_record
(
    id                        bigint auto_increment comment '主键id'
        primary key,
    robot_inventory_task_id   bigint                             not null comment '盘点任务id',
    warehouse_id              bigint                             not null comment '盘点库房id',
    robot_id                  bigint                             not null comment '盘点机器人id',
    robot_inventory_task_name varchar(100)                       null comment '盘点任务名称',
    inventory_status          varchar(20)                        not null comment '盘点状态',
    created_id                bigint                             not null comment '创建人id',
    created_at                datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at                datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '机器人盘点记录表';

create index idx_robot_inventory_task_id
    on robot_inventory_record (robot_inventory_task_id);

create table robot_inventory_record_detail
(
    id                        bigint auto_increment comment '主键id'
        primary key,
    robot_inventory_record_id bigint                             not null comment '机器人盘点记录id',
    rack_group_id             bigint                             not null comment '密集架组id',
    rack_colum_no             bigint                             null comment '密集架列编号',
    rack_panel_no             bigint                             null comment '密集架面编号',
    archive_id                bigint                             not null comment '档案id',
    archive_no                varchar(60)                        null comment '档案号',
    archive_name              varchar(60)                        null comment '档案名',
    created_at                datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at                datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '机器人盘点记录详情表';

create index idx_robot_inventory_record_id
    on robot_inventory_record_detail (robot_inventory_record_id);

create table robot_inventory_task
(
    id             bigint auto_increment comment '主键id'
        primary key,
    warehouse_id   bigint                             not null comment '盘点库房id',
    task_name      varchar(60)                        null comment '任务名字',
    task_status    varchar(20)                        not null comment '任务状态，unstarted未开启，started已开始，finished完成',
    robot_id       bigint                             not null comment '盘点机器人id',
    task_cycle     varchar(20)                        not null comment '任务周期，single单次，weekly每周，monthly每月，yearly每年',
    task_cycle_day int                                null comment '任务执行周期天数',
    task_time      time                               not null comment '任务执行时间',
    created_id     bigint                             not null comment '创建人id',
    updated_id     bigint                             not null comment '更新人id',
    created_at     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at      bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '机器人盘点任务表';

create index idx_warehouse_id
    on robot_inventory_task (warehouse_id);

create table robot_inventory_task_detail
(
    id                      bigint auto_increment comment '主键id'
        primary key,
    robot_inventory_task_id bigint                             not null comment '机器人盘点任务id',
    rack_group_id           bigint                             not null comment '密集架组id',
    rack_colum_no           bigint                             null comment '密集架列编号',
    rack_panel_no           bigint                             null comment '密集架面编号',
    created_at              datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at              datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '机器人盘点任务详情表';

create index idx_robot_inventory_task_id
    on robot_inventory_task_detail (robot_inventory_task_id);

create table smart_device
(
    id            bigint auto_increment comment '主键id'
        primary key,
    warehouse_id  bigint                             not null comment '所属库房id',
    device_type   varchar(20)                        not null comment '设备类型，盘点机器人，交接机器人，存取档机器人',
    device_status varchar(20)                        not null comment '设备状态，online在线、offline离线',
    device_name   varchar(60)                        not null comment '设备名称',
    device_no     varchar(60)                        not null comment '设备编号',
    device_ip     varchar(20)                        null comment '设备ip',
    device_port   int                                null comment '设备port',
    visual_config varchar(200)                       null comment '地图配置信息',
    created_id    bigint                             not null comment '创建人id',
    updated_id    bigint                             not null comment '更新人id',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at     bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '智能设备表';

create index idx_warehouse_id
    on smart_device (warehouse_id);

create table strategy
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    warehouse_id         bigint                             not null comment '所属库房id',
    strategy_name        varchar(60)                        not null comment '策略名称',
    strategy_description varchar(100)                       null comment '策略描述',
    strategy_context     varchar(1000)                      not null comment '策略具体内容，JSON字符串',
    created_id           bigint                             not null comment '创建人id',
    updated_id           bigint                             not null comment '更新人id',
    created_at           datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at           datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at            bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '智能策略表';

create index idx_warehouse_id
    on strategy (warehouse_id);

create table surveillance_device
(
    id             bigint auto_increment comment '主键id'
        primary key,
    warehouse_id   bigint                             not null comment '所属库房id',
    device_status  varchar(20)                        not null comment '设备状态，online在线、offline离线',
    device_name    varchar(60)                        not null comment '设备名称',
    device_no      varchar(60)                        not null comment '设备编号',
    device_ip      varchar(20)                        not null comment '设备ip',
    device_port    int                                not null comment '设备port',
    channel_no     int                                not null comment '通道号',
    rtmp_url       varchar(200)                       null comment '推流地址',
    device_account varchar(60)                        not null comment '设备账号',
    device_pwd     varchar(60)                        not null comment '设备密码',
    visual_config  varchar(200)                       null comment '地图配置信息',
    created_id     bigint                             not null comment '创建人id',
    updated_id     bigint                             not null comment '更新人id',
    created_at     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at      bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '视频监控设备表';

create index idx_warehouse_id
    on surveillance_device (warehouse_id);

create table surveillance_solution
(
    id            bigint auto_increment comment '主键id'
        primary key,
    solution_name varchar(60)                        not null comment '方案名字',
    solution_type varchar(20)                        not null comment '分栏类型，1x1，2x2，3x3，1+5，1+7',
    device1_id    bigint                             null comment '设备1id',
    device2_id    bigint                             null comment '设备2id',
    device3_id    bigint                             null comment '设备3id',
    device4_id    bigint                             null comment '设备4id',
    device5_id    bigint                             null comment '设备5id',
    device6_id    bigint                             null comment '设备6id',
    device7_id    bigint                             null comment '设备7id',
    device8_id    bigint                             null comment '设备8id',
    created_id    bigint                             not null comment '创建人id',
    updated_id    bigint                             not null comment '更新人id',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at     bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除'
)
    comment '监控方案表';

create table sys_api
(
    id          bigint auto_increment comment '主键id'
        primary key,
    function_id int         null comment '功能ID',
    api_uri     varchar(64) null comment '接口请求URI'
)
    comment '接口请求api表';

create index idx_function_id
    on sys_api (function_id);

create table sys_configuration
(
    id             bigint auto_increment comment '主键id'
        primary key,
    config_type    varchar(30)   not null comment '配置类型，sys系统配置表，visual可视化平台配置表',
    config_context varchar(2000) not null comment '配置内容，JSON字符串'
)
    comment '配置信息表';

create table sys_function
(
    id             bigint auto_increment comment '主键id'
        primary key,
    function_name  varchar(32)       null comment '功能名称',
    is_control     tinyint default 0 not null comment '是否需要控制，0不需要控制，1需要控制',
    ordinal        int     default 0 not null comment '序号，用来自定义顺序',
    parent_name    varchar(32)       not null comment '父节点名称',
    parent_ordinal int     default 0 not null comment '父节点序号，用来自定义顺序',
    grand_name     varchar(32)       not null comment '祖节点名称',
    grand_ordinal  int     default 0 not null comment '祖节点序号，用来自定义顺序'
)
    comment '功能表';

create table sys_menu
(
    id        bigint auto_increment comment '主键id'
        primary key,
    parent_id bigint default 0 not null comment '父菜单id',
    ordinal   int    default 0 not null comment '序号，用来指定菜单的顺序',
    menu_code varchar(32)      not null comment '菜单编码，用来区分菜单的唯一标识',
    menu_name varchar(32)      not null comment '菜单名'
)
    comment '菜单';

create table sys_menu_function
(
    id          bigint auto_increment comment '主键id'
        primary key,
    menu_id     int null comment '菜单id',
    function_id int null comment '功能id'
)
    comment '菜单功能关系表';

create index idx_menu_id_function_id
    on sys_menu_function (menu_id, function_id);

create table sys_role
(
    id          bigint auto_increment comment '主键id'
        primary key,
    role_name   varchar(64)                  not null comment '角色名',
    role_status varchar(32) default 'normal' not null comment '角色状态',
    remark      varchar(128)                 null comment '备注'
)
    comment '角色表';

create table sys_role_function
(
    id          bigint auto_increment comment '主键id'
        primary key,
    role_id     int not null comment '角色id',
    function_id int not null comment '功能id',
    constraint uk_role_id_function_id
        unique (role_id, function_id)
)
    comment '角色功能关系表';

create table temperature_humidity_record
(
    id          bigint auto_increment comment '主键id'
        primary key,
    device_id   bigint                             not null comment '设备id',
    device_name varchar(60)                        not null comment '设备名字',
    temperature decimal(10, 2)                     null comment '温度 (单位：摄氏度)',
    humidity    decimal(10, 2)                     null comment '湿度 (单位：百分比)',
    created_at  datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '温湿度历史记录表';

create index idx_device_id
    on temperature_humidity_record (device_id);

create table user
(
    id           bigint auto_increment comment '主键id'
        primary key,
    dept_id      bigint                                null comment '部门id',
    role_id      bigint                                null comment '角色id',
    admin_flag   tinyint     default 0                 not null comment '超管标识，0普通员工，1超管',
    user_status  varchar(20) default 'normal'          not null comment '用户状态，normal正常，disable禁用',
    user_sex     varchar(20) default 'male'            not null comment '性别，male男，female女',
    mobile       varchar(60)                           null comment '电话号码',
    email        varchar(60)                           null comment '邮箱',
    user_name    varchar(60)                           not null comment '用户名',
    user_account varchar(60)                           not null comment '用户账号',
    user_pwd     varchar(200)                          not null comment '用户密码',
    id_card      varchar(60)                           null comment '卡号',
    finger_id    varchar(2000)                         null comment '指纹id',
    face_id      varchar(2000)                         null comment '人脸地址',
    gesture      varchar(100)                          null comment '手势',
    created_id   bigint                                null comment '创建人id',
    updated_id   bigint                                null comment '更新人id',
    created_at   datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at    bigint      default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_id_card
        unique (id_card, delete_at),
    constraint uk_user_account
        unique (user_account, delete_at)
)
    comment '用户表';

create index idx_dept_id
    on user (dept_id);

create index idx_role_id
    on user (role_id);

create table users_login_record
(
    id         bigint auto_increment comment '主键id'
        primary key,
    user_id    bigint                             not null comment '用户id',
    is_login   tinyint  default 1                 not null comment '0登出，1登录',
    login_type varchar(20)                        null comment '登录方式，account账号，id_card卡号，finger指纹，face人脸',
    remark     varchar(128)                       null comment '备注',
    created_at datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户登录记录表';

create table warehouse
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    organization_id    bigint                             not null comment '机构id',
    warehouse_floor_id bigint                             not null comment '所属楼层id',
    warehouse_no       int                                not null comment '库房编号',
    warehouse_name     varchar(60)                        not null comment '库房名',
    visual_config      varchar(200)                       null comment '地图配置信息',
    created_id         bigint                             null comment '创建人id',
    updated_id         bigint                             null comment '更新人id',
    created_at         datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at          bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_floor_id_warehouse_no
        unique (warehouse_floor_id, warehouse_no, delete_at)
)
    comment '库房表';

create table warehouse_building
(
    id            bigint auto_increment comment '主键id'
        primary key,
    building_no   int                                not null comment '建筑编号',
    building_name varchar(60)                        not null comment '建筑名',
    location      varchar(200)                       null comment '省、市、区的完整地址描述（如：省-市-区）',
    visual_config varchar(200)                       null comment '地图配置信息',
    created_id    bigint                             null comment '创建人id',
    updated_id    bigint                             null comment '更新人id',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at     bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_building_no
        unique (building_no, delete_at)
)
    comment '库房建筑表';

create table warehouse_floor
(
    id                    bigint auto_increment comment '主键id'
        primary key,
    warehouse_building_id bigint                             not null comment '所属建筑id',
    floor_no              int                                not null comment '层编号',
    floor_name            varchar(60)                        not null comment '层名',
    visual_config         varchar(200)                       null comment '地图配置信息',
    created_id            bigint                             null comment '创建人id',
    updated_id            bigint                             null comment '更新人id',
    created_at            datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    delete_at             bigint   default 0                 not null comment '删除时间戳，标记是否被逻辑删除',
    constraint uk_building_id_floor_no
        unique (warehouse_building_id, floor_no, delete_at)
)
    comment '库房楼层表';

