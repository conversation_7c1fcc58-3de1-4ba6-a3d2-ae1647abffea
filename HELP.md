### Swagger3

* [knife4j地址](http://localhost:18084/doc.html)
* [Swagger-UI 地址，建议使用上面的knife4j，界面更直观](http://localhost:18084/swagger-ui.html)
* [OpenAPI3 地址](http://localhost:18084/v3/api-docs)

### 项目环境

* [SpringBoot3.3.8](https://docs.spring.io/spring-boot/3.3/reference/index.html)
* [GraalVM Native Image Support](https://docs.spring.io/spring-boot/3.3.8/reference/packaging/native-image/introducing-graalvm-native-images.html)
* [MyBatis Framework](https://mybatis.org/spring-boot-starter/mybatis-spring-boot-autoconfigure/)
* [Create an OCI image](https://docs.spring.io/spring-boot/3.3.8/maven-plugin/build-image.html)
* [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/3.3.8/maven-plugin)
* [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
* [Java17.0.2下载](https://mirrors.huaweicloud.com/openjdk/)
* [MySQL8.0.41](https://dev.mysql.com/downloads/mysql/)

### 项目目录

`src/main/java`下目录

```angular2html
|_aspect：放置切面代码
|_cache：缓存相关，包括线程缓存、本机缓存、分布式缓存
|_client：服务端调用其他服务接口封装
|_common：通用类
   |__annotation：放置项目自定义注解
   |__convert：转换类
   |__exception：自定义的异常
   |__properties：properties映射类
   |__utils：放置工具类和辅助代码
|_config：放置配置类
|_constant：放置常量、枚举等定义
   |__consist：存放常量定义
   |__enums：存放枚举定义
|_controller：放置控制器代码
|_domain：接口请求和响应实体相关
   |__annotation：自定义JSR303注解
   |__bo：业务逻辑层的数据封装实体类
   |__request：请求实体类
   |__response：响应实体类
|_filter：放置一些过滤、拦截相关的代码
|_handler：放置自定义的Handler
|_mybatis: 跟MyBatis交互的类
    |__entity：数据库映射实体类
    |__ext：进行连表查询时，对entity进行的扩展
    |__mapper：放置数据访问层代码接口
|_runner：放置自定义的Runner
|_scheduler：放置定时任务类
|_service：放置具体的业务逻辑代码（接口和实现分离）
   |__impl：存放业务逻辑实际实现
```

`/src/main/resources`目录

```angular2html
|_mappers：存放mybatis的XML映射文件（如果是mybatis项目）
|_static：存放网页静态资源，比如下面的js/css/img
   |__js：
   |__css：
   |__img：
   |__font：
   |__等等
|_template：存放网页模板，比如thymeleaf/freemarker模板等
   |__header
   |__sidebar
   |__bottom
   |__XXX.html等等
|_application.properties       基本配置文件
|_application-dev.properties   开发环境配置文件
|_application-prod.properties  生产环境配置文件
```

**注意：controller、service、request、response、convert 等包下面都是分模块进行文件存放的**

- zhpt：智慧平台相关代码
- ops：运维平台相关代码